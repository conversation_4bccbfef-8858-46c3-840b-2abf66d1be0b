#!/usr/bin/env python3
"""
Test Phemex Timeframes

Test different timeframe formats to find what works.
"""

import ccxt
import asyncio

async def test_timeframes():
    """Test different timeframe formats."""
    print("🕐 TESTING PHEMEX TIMEFRAMES")
    print("=" * 50)
    
    # Use CALEB's API keys for authenticated requests
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Load markets
    markets = exchange.load_markets()
    
    # Test symbols
    test_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
    
    # Test timeframes
    test_timeframes = [
        '1m', '3m', '5m', '15m', '30m',
        '1h', '2h', '4h', '6h', '12h', 
        '1d', '3d', '1w', '1M'
    ]
    
    for symbol in test_symbols:
        print(f"\n📊 Testing {symbol}:")
        print("-" * 30)
        
        for timeframe in test_timeframes:
            try:
                print(f"   {timeframe}...", end=" ")
                
                # Try to fetch just 2 candles
                ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=2)
                
                if ohlcv and len(ohlcv) > 0:
                    print(f"✅ WORKS ({len(ohlcv)} candles)")
                else:
                    print(f"❌ No data")
                    
            except Exception as e:
                error_msg = str(e)
                if "30000" in error_msg:
                    print(f"❌ Invalid args")
                elif "30001" in error_msg:
                    print(f"❌ Invalid symbol")
                else:
                    print(f"❌ {error_msg[:50]}...")

async def test_public_vs_private():
    """Test public vs private API access."""
    print(f"\n🔓 TESTING PUBLIC VS PRIVATE API")
    print("=" * 50)
    
    # Public exchange (no API keys)
    public_exchange = ccxt.phemex({
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Private exchange (with API keys)
    private_exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Load markets for both
    public_markets = public_exchange.load_markets()
    private_markets = private_exchange.load_markets()
    
    symbol = 'BTC/USDT:USDT'
    timeframe = '1h'
    
    print(f"📊 Testing {symbol} {timeframe}:")
    
    # Test public API
    print("   Public API...", end=" ")
    try:
        ohlcv = public_exchange.fetch_ohlcv(symbol, timeframe, limit=2)
        print(f"✅ WORKS ({len(ohlcv)} candles)")
    except Exception as e:
        print(f"❌ {str(e)[:50]}...")
    
    # Test private API
    print("   Private API...", end=" ")
    try:
        ohlcv = private_exchange.fetch_ohlcv(symbol, timeframe, limit=2)
        print(f"✅ WORKS ({len(ohlcv)} candles)")
    except Exception as e:
        print(f"❌ {str(e)[:50]}...")

async def test_different_symbol_formats():
    """Test different ways to specify the same symbol."""
    print(f"\n🎯 TESTING DIFFERENT SYMBOL FORMATS")
    print("=" * 50)
    
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True
    })
    
    # Load markets
    markets = exchange.load_markets()
    
    # Try different ways to access BTC perpetual
    btc_formats = [
        'BTC/USDT:USDT',
        'BTC/USD:BTC', 
        'BTCUSDT',
        'BTCUSD'
    ]
    
    print("🟡 BTC Formats:")
    for symbol_format in btc_formats:
        print(f"   {symbol_format}...", end=" ")
        
        if symbol_format in markets:
            try:
                # Try with different options
                exchange.options['defaultType'] = 'swap'
                ohlcv = exchange.fetch_ohlcv(symbol_format, '1h', limit=2)
                print(f"✅ WORKS")
            except Exception as e:
                print(f"❌ {str(e)[:30]}...")
        else:
            print(f"❌ Not in markets")

async def main():
    """Main function."""
    print("🚀 PHEMEX TIMEFRAME TESTER")
    print("=" * 60)
    
    try:
        # Test timeframes
        await test_timeframes()
        
        # Test public vs private
        await test_public_vs_private()
        
        # Test symbol formats
        await test_different_symbol_formats()
        
        print(f"\n🎉 TIMEFRAME TEST COMPLETE!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
