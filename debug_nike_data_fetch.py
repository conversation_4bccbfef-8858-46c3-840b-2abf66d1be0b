#!/usr/bin/env python3
"""
Debug Nike Data Fetch

Test the exact same setup as our Nike bot to find the data fetching issue.
"""

import ccxt
import asyncio
import pandas as pd
from datetime import datetime

async def test_exact_nike_setup():
    """Test the exact same setup as Nike bot."""
    print("🔍 DEBUGGING NIKE DATA FETCH")
    print("=" * 50)
    
    # Exact same exchange setup as Nike bot
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Test both symbols
    test_configs = [
        {'symbol': 'BTC', 'timeframes': {'daily': '1d', 'medium': '4h', 'lower': '1h'}},
        {'symbol': 'ADA', 'timeframes': {'daily': '1d', 'medium': '1h', 'lower': '15m'}}
    ]
    
    for config in test_configs:
        symbol = config['symbol']
        timeframes = config['timeframes']
        
        print(f"\n📊 Testing {symbol}:")
        print(f"   Timeframes: {list(timeframes.values())}")
        
        # Use correct Phemex symbol format (same as Nike bot)
        symbol_mapping = {
            'BTC': 'BTC/USDT:USDT',
            'ADA': 'ADA/USDT:USDT'
        }
        phemex_symbol = symbol_mapping.get(symbol, f"{symbol}/USDT:USDT")
        print(f"   Phemex symbol: {phemex_symbol}")
        
        data = {}
        
        for tf_name, tf_interval in timeframes.items():
            try:
                print(f"   {tf_name} ({tf_interval})...", end=" ")
                
                # Test sync call (same as Nike bot)
                ohlcv = exchange.fetch_ohlcv(phemex_symbol, tf_interval, limit=200)
                
                if ohlcv and len(ohlcv) > 0:
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    data[tf_name] = df
                    
                    print(f"✅ {len(df)} candles, latest: ${df['close'].iloc[-1]:.4f}")
                else:
                    print(f"❌ No data received")
                    
            except Exception as tf_error:
                print(f"❌ {tf_error}")
        
        print(f"   Result: {len(data)}/{len(timeframes)} timeframes successful")

def test_sync_in_async_context():
    """Test if calling sync methods from async context causes issues."""
    print(f"\n🔄 TESTING SYNC IN ASYNC CONTEXT")
    print("=" * 50)
    
    # This is what our Nike bot does - sync exchange in async function
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Test calling sync methods from this async function
    try:
        print("   Loading markets...", end=" ")
        markets = exchange.load_markets()
        print(f"✅ {len(markets)} markets")
        
        print("   Fetching BTC 1h...", end=" ")
        ohlcv = exchange.fetch_ohlcv('BTC/USDT:USDT', '1h', limit=5)
        print(f"✅ {len(ohlcv)} candles")
        
        print("   Fetching ADA 15m...", end=" ")
        ohlcv = exchange.fetch_ohlcv('ADA/USDT:USDT', '15m', limit=5)
        print(f"✅ {len(ohlcv)} candles")
        
        print("✅ Sync calls work fine in async context")
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def test_with_different_limits():
    """Test with different limit values."""
    print(f"\n📊 TESTING DIFFERENT LIMITS")
    print("=" * 50)
    
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    symbol = 'BTC/USDT:USDT'
    timeframe = '1h'
    
    # Test different limits
    test_limits = [5, 10, 50, 100, 200, 500, 1000]
    
    print(f"Testing {symbol} {timeframe} with different limits:")
    
    for limit in test_limits:
        try:
            print(f"   limit={limit}...", end=" ")
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            print(f"✅ {len(ohlcv)} candles")
        except Exception as e:
            print(f"❌ {str(e)[:50]}...")

async def main():
    """Main debug function."""
    print("🚀 NIKE DATA FETCH DEBUG")
    print("=" * 60)
    
    # Test exact Nike setup
    await test_exact_nike_setup()
    
    # Test sync in async context
    test_sync_in_async_context()
    
    # Test different limits
    await test_different_limits()
    
    print(f"\n🎯 DEBUG COMPLETE")

if __name__ == "__main__":
    asyncio.run(main())
