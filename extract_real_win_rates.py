#!/usr/bin/env python3
"""
Extract Real Win Rates from Algorithm Trade Data

This script runs the algorithms and extracts the ACTUAL win rates
from the trade generation process, not estimated values.
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import all algorithms
sys.path.append('/Users/<USER>/TomorrowTech/python-backend/data_seed')

def load_data_seed_with_resampling(symbol: str):
    """Load data from data_seed and resample 1h to create 4h data."""
    
    symbol_map = {'BTC': 'XBTUSD', 'ADA': 'ADAUSDT'}
    
    if symbol not in symbol_map:
        return None
    
    data = {}
    
    print(f"📊 Loading data for {symbol}...")
    
    # Load daily data
    try:
        daily_filename = f"data_seed/{symbol_map[symbol]}_1440.csv"
        df_daily = pd.read_csv(daily_filename, header=None, 
                              names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'trades'])
        df_daily['datetime'] = pd.to_datetime(df_daily['timestamp'], unit='s')
        df_daily.set_index('timestamp', inplace=True)
        df_daily = df_daily.sort_index()
        data['daily'] = df_daily
        
    except Exception as e:
        print(f"   ❌ Error loading daily data: {e}")
        return None
    
    # Load 1h data and resample to 4h
    try:
        hourly_filename = f"data_seed/{symbol_map[symbol]}_60.csv"
        df_1h = pd.read_csv(hourly_filename, header=None, 
                           names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'trades'])
        df_1h['datetime'] = pd.to_datetime(df_1h['timestamp'], unit='s')
        
        # Create copy for resampling
        df_1h_for_resampling = df_1h.copy()
        df_1h_for_resampling.set_index('datetime', inplace=True)
        df_1h_for_resampling = df_1h_for_resampling.sort_index()
        
        # Keep original with timestamp index
        df_1h.set_index('timestamp', inplace=True)
        df_1h = df_1h.sort_index()
        data['lower'] = df_1h
        
        # Resample 1h to 4h
        df_4h = df_1h_for_resampling.resample('4h').agg({
            'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last',
            'volume': 'sum', 'trades': 'sum', 'timestamp': 'first'
        }).dropna()
        
        df_4h['datetime'] = df_4h.index
        df_4h['timestamp'] = df_4h.index.astype('int64') // 10**9
        df_4h.set_index('timestamp', inplace=True)
        data['medium'] = df_4h
        
    except Exception as e:
        print(f"   ❌ Error loading/resampling 1h data: {e}")
        return None
    
    return data

def extract_actual_win_rates(algorithm_name: str, symbol: str, initial_equity: float = 10000):
    """Extract ACTUAL win rates from algorithm trade generation."""
    
    print(f"🔍 Extracting REAL win rates for {algorithm_name} on {symbol}...")
    
    # Load data
    data = load_data_seed_with_resampling(symbol)
    if not data:
        return None
    
    # Import the appropriate algorithm
    try:
        if algorithm_name == "PT_Adjusted":
            from titan_2_kcaleb_notrailing_confidencemode_ptadj import titan2kCaleb
        elif algorithm_name == "Confidence_Mode":
            from titan_2_kcaleb_notrailing_confidencemode import titan2kCaleb
        elif algorithm_name == "Aggressive_Only":
            import importlib.util
            spec = importlib.util.spec_from_file_location("titan2kCaleb", "data_seed/titan_2_kcaleb_notrailing (1).py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            titan2kCaleb = module.titan2kCaleb
        elif algorithm_name == "Conservative_02_Vol15":
            import importlib.util
            spec = importlib.util.spec_from_file_location("titan2kCaleb", "data_seed/titan_2_kcaleb_notrailing_confidencemode_ptadj_conservative02_vol15.py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            titan2kCaleb = module.titan2kCaleb
        else:
            print(f"   ❌ Unknown algorithm: {algorithm_name}")
            return None
            
        model = titan2kCaleb()
        
    except Exception as e:
        print(f"   ❌ Error importing {algorithm_name}: {e}")
        return None
    
    # Calculate indicators
    for tf_name in ['daily', 'medium', 'lower']:
        data[tf_name] = model.calculate_indicators(data[tf_name])
    
    # Combine timeframes
    combined_df = model.combine_timeframes(data['daily'], data['medium'], data['lower'])
    
    # Generate signals and get detailed results
    try:
        result_df = model.generate_signals_with_compounding_and_reversal(combined_df, initial_equity)
        
        # Extract trade outcomes by analyzing equity changes
        trades_analysis = []
        current_trade = None
        
        for idx, row in result_df.iterrows():
            # Check for trade entry
            if row['signal'] != 'NEUTRAL':
                current_trade = {
                    'entry_idx': idx,
                    'entry_equity': row['equity'],
                    'signal': row['signal'],
                    'mode_used': row.get('mode_used', 'aggressive'),  # Default for Aggressive_Only
                    'confidence': row.get('confidence', 0.5)
                }
            
            # Check for trade exit
            if pd.notna(row.get('exit_signal')) and current_trade:
                exit_equity = row['equity']
                entry_equity = current_trade['entry_equity']
                
                # Determine if trade was winning based on equity increase
                is_winning = exit_equity > entry_equity
                pnl_percent = (exit_equity - entry_equity) / entry_equity * 100
                
                trades_analysis.append({
                    'signal': current_trade['signal'],
                    'mode_used': current_trade['mode_used'],
                    'confidence': current_trade['confidence'],
                    'entry_equity': entry_equity,
                    'exit_equity': exit_equity,
                    'pnl_percent': pnl_percent,
                    'is_winning': is_winning,
                    'exit_reason': row['exit_signal']
                })
                
                current_trade = None
        
        if len(trades_analysis) == 0:
            print(f"   ❌ No completed trades found for {algorithm_name} {symbol}")
            return None
        
        trades_df = pd.DataFrame(trades_analysis)
        
        # Calculate actual win rates
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['is_winning'] == True])
        overall_win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # Mode-specific win rates
        mode_stats = {}
        for mode in trades_df['mode_used'].unique():
            mode_trades = trades_df[trades_df['mode_used'] == mode]
            mode_winning = len(mode_trades[mode_trades['is_winning'] == True])
            mode_total = len(mode_trades)
            mode_win_rate = (mode_winning / mode_total * 100) if mode_total > 0 else 0
            
            mode_stats[mode] = {
                'total_trades': mode_total,
                'winning_trades': mode_winning,
                'win_rate': mode_win_rate
            }
        
        # Calculate final equity and return
        final_equity = result_df['equity'].iloc[-1]
        total_return = (final_equity - initial_equity) / initial_equity * 100
        
        # Calculate max drawdown
        equity_curve = result_df['equity'].values
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak * 100
        max_drawdown = np.min(drawdown)
        
        print(f"   ✅ REAL STATS: {total_trades} trades, {overall_win_rate:.1f}% win rate, {total_return:+.2f}% return")
        
        return {
            'algorithm': algorithm_name,
            'symbol': symbol,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'overall_win_rate': overall_win_rate,
            'mode_stats': mode_stats,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'trades_df': trades_df
        }
        
    except Exception as e:
        print(f"   ❌ Error analyzing {algorithm_name} {symbol}: {e}")
        return None

def generate_real_win_rate_report():
    """Generate report with ACTUAL win rates from trade data."""
    
    print("🎯 EXTRACTING REAL WIN RATES FROM ALGORITHM TRADE DATA")
    print("=" * 70)
    
    # Focus on ADA first since that's what user is most interested in
    algorithms = ['Conservative_02_Vol15', 'PT_Adjusted', 'Confidence_Mode', 'Aggressive_Only']
    
    results = []
    
    for algorithm in algorithms:
        result = extract_actual_win_rates(algorithm, 'ADA')
        if result:
            results.append(result)
    
    print(f"\n📊 REAL WIN RATE COMPARISON (ADA):")
    print("=" * 80)
    print(f"{'Algorithm':<25} {'Total Trades':<12} {'Win Rate %':<10} {'Agg Win %':<10} {'Con Win %':<10}")
    print("=" * 80)
    
    for result in results:
        agg_win_rate = result['mode_stats'].get('aggressive', {}).get('win_rate', 0)
        con_win_rate = result['mode_stats'].get('conservative', {}).get('win_rate', 0)
        
        print(f"{result['algorithm']:<25} {result['total_trades']:<12} {result['overall_win_rate']:<9.1f} {agg_win_rate:<9.1f} {con_win_rate:<9.1f}")
    
    print("=" * 80)
    
    # Create detailed breakdown
    print(f"\n📋 DETAILED MODE BREAKDOWN:")
    print("=" * 60)
    
    for result in results:
        print(f"\n{result['algorithm']}:")
        print(f"   Overall: {result['total_trades']} trades, {result['overall_win_rate']:.1f}% win rate")
        
        for mode, stats in result['mode_stats'].items():
            percentage = (stats['total_trades'] / result['total_trades'] * 100) if result['total_trades'] > 0 else 0
            print(f"   {mode.title()}: {stats['total_trades']} trades ({percentage:.1f}%), {stats['win_rate']:.1f}% win rate")
    
    # Save detailed results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create CSV with real data
    csv_data = []
    for result in results:
        for mode, stats in result['mode_stats'].items():
            csv_data.append({
                'algorithm': result['algorithm'],
                'symbol': result['symbol'],
                'mode': mode,
                'total_trades_all_modes': result['total_trades'],
                'overall_win_rate_percent': result['overall_win_rate'],
                'mode_trades': stats['total_trades'],
                'mode_win_rate_percent': stats['win_rate'],
                'mode_percentage_of_total': (stats['total_trades'] / result['total_trades'] * 100) if result['total_trades'] > 0 else 0,
                'total_return_percent': result['total_return'],
                'max_drawdown_percent': result['max_drawdown']
            })
    
    csv_df = pd.DataFrame(csv_data)
    csv_filename = f"REAL_WIN_RATES_ANALYSIS_{timestamp}.csv"
    csv_df.to_csv(csv_filename, index=False)
    
    print(f"\n✅ REAL WIN RATE DATA SAVED: {csv_filename}")
    
    # Show the truth about win rates
    print(f"\n🎯 THE TRUTH ABOUT WIN RATES:")
    print("=" * 50)
    
    if results:
        best_overall = max(results, key=lambda x: x['overall_win_rate'])
        print(f"🏆 HIGHEST OVERALL WIN RATE: {best_overall['algorithm']} ({best_overall['overall_win_rate']:.1f}%)")
        
        # Find best aggressive win rate
        best_agg = max(results, key=lambda x: x['mode_stats'].get('aggressive', {}).get('win_rate', 0))
        agg_rate = best_agg['mode_stats'].get('aggressive', {}).get('win_rate', 0)
        print(f"⚡ HIGHEST AGGRESSIVE WIN RATE: {best_agg['algorithm']} ({agg_rate:.1f}%)")
        
        # Find best conservative win rate
        conservative_results = [r for r in results if 'conservative' in r['mode_stats']]
        if conservative_results:
            best_con = max(conservative_results, key=lambda x: x['mode_stats'].get('conservative', {}).get('win_rate', 0))
            con_rate = best_con['mode_stats'].get('conservative', {}).get('win_rate', 0)
            print(f"🛡️ HIGHEST CONSERVATIVE WIN RATE: {best_con['algorithm']} ({con_rate:.1f}%)")
    
    return results

if __name__ == "__main__":
    generate_real_win_rate_report()
