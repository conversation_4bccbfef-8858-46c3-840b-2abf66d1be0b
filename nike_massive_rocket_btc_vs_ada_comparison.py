#!/usr/bin/env python3
"""
Nike Massive Rocket: BTC vs ADA 4-Year Comparison (2021-2025)

Side-by-side backtesting to determine which asset is more profitable
for CALEB's live trading system.
"""

import sys
import os
import pandas as pd
import numpy as np
# import matplotlib.pyplot as plt
# import seaborn as sns
from datetime import datetime, timedelta
import importlib.util
import warnings
warnings.filterwarnings('ignore')

# Add data_seed to path
sys.path.append('/Users/<USER>/TomorrowTech/python-backend/data_seed')

def load_nike_massive_rocket():
    """Load Nike's Massive Rocket algorithm."""
    try:
        spec = importlib.util.spec_from_file_location(
            "NikesMassiveRocket",
            "/Users/<USER>/TomorrowTech/python-backend/data_seed/Nike's Massive Rocket Algo.py"
        )
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module.NikesMassiveRocket()
    except Exception as e:
        print(f"❌ Error loading Nike Massive Rocket: {e}")
        return None

def load_historical_data(symbol, start_date='2021-01-01'):
    """Load 4 years of historical data for backtesting."""
    print(f"📊 Loading {symbol} data from {start_date}...")
    
    try:
        # Load data files (using actual file names from data_seed)
        data_files = {
            'BTC': {
                'daily': '/Users/<USER>/TomorrowTech/python-backend/data_seed/XBTUSDT_1440.csv',
                '4h': '/Users/<USER>/TomorrowTech/python-backend/data_seed/XBTUSDT_240.csv',
                '1h': '/Users/<USER>/TomorrowTech/python-backend/data_seed/XBTUSDT_60.csv'
            },
            'ADA': {
                'daily': '/Users/<USER>/TomorrowTech/python-backend/data_seed/ADAUSDT_1440.csv',
                '1h': '/Users/<USER>/TomorrowTech/python-backend/data_seed/ADAUSDT_60.csv',
                '15m': '/Users/<USER>/TomorrowTech/python-backend/data_seed/ADAUSDT_15.csv'
            }
        }
        
        if symbol not in data_files:
            print(f"❌ No data files configured for {symbol}")
            return None
        
        symbol_data = {}
        files = data_files[symbol]
        
        for timeframe, filepath in files.items():
            if os.path.exists(filepath):
                # Read CSV with proper column names
                df = pd.read_csv(filepath, header=None,
                               names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'count'])

                # Convert timestamp from Unix to datetime
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')

                # Filter to 4-year period
                start_dt = pd.to_datetime(start_date)
                df = df[df['timestamp'] >= start_dt].copy()

                if len(df) > 0:
                    symbol_data[timeframe] = df
                    print(f"   ✅ {timeframe}: {len(df)} candles ({df['timestamp'].min()} → {df['timestamp'].max()})")
                else:
                    print(f"   ❌ {timeframe}: No data in date range")
            else:
                print(f"   ❌ {timeframe}: File not found - {filepath}")
        
        return symbol_data if symbol_data else None
        
    except Exception as e:
        print(f"❌ Error loading {symbol} data: {e}")
        return None

def run_nike_backtest(symbol, data, algorithm, initial_balance=500):
    """Run Nike Massive Rocket backtest on symbol data."""
    print(f"\n🚀 Running Nike Massive Rocket backtest: {symbol}")
    print(f"💰 Initial balance: ${initial_balance} USDT")
    
    try:
        # Prepare data for algorithm
        if symbol == 'BTC':
            timeframes = {'daily': 'daily', 'medium': '4h', 'lower': '1h'}
        else:  # ADA
            timeframes = {'daily': 'daily', 'medium': '1h', 'lower': '15m'}
        
        # Get the timeframe with most data points for iteration
        main_timeframe = 'daily'
        if main_timeframe not in data:
            main_timeframe = list(data.keys())[0]
        
        main_df = data[main_timeframe].copy()
        main_df = main_df.sort_values('timestamp').reset_index(drop=True)
        
        # Backtest results
        results = {
            'dates': [],
            'prices': [],
            'signals': [],
            'positions': [],
            'equity': [],
            'trades': [],
            'returns': [],
            'drawdowns': [],
            'modes': []
        }
        
        current_equity = initial_balance
        position = 0
        entry_price = 0
        stop_loss = 0
        take_profit = 0
        peak_equity = initial_balance
        
        print(f"📈 Processing {len(main_df)} data points...")
        
        # Process each data point
        for i in range(200, len(main_df)):  # Start after 200 candles for indicators
            current_date = main_df.iloc[i]['timestamp']
            current_price = main_df.iloc[i]['close']
            
            # Prepare multi-timeframe data for algorithm
            algo_data = {}
            for tf_name, tf_key in timeframes.items():
                if tf_key in data:
                    tf_df = data[tf_key]
                    # Get data up to current date
                    tf_current = tf_df[tf_df['timestamp'] <= current_date].tail(200)
                    if len(tf_current) > 50:  # Minimum for indicators
                        algo_data[tf_name] = tf_current
            
            if len(algo_data) != len(timeframes):
                continue  # Skip if not all timeframes available
            
            # Run algorithm
            try:
                signal_result = algorithm.generate_signal(algo_data, current_equity)
                
                signal = signal_result.get('signal', 'NEUTRAL')
                mode = signal_result.get('mode', 'conservative')
                position_size = signal_result.get('position_size', 0)
                leverage = signal_result.get('leverage', 1.0)
                
                # Handle position management
                if position == 0 and signal in ['BUY', 'SELL']:
                    # Enter position
                    position = position_size if signal == 'BUY' else -position_size
                    entry_price = current_price
                    
                    # Set stops from algorithm
                    stop_loss = signal_result.get('stop_loss', current_price * 0.95)
                    take_profit = signal_result.get('take_profit', current_price * 1.05)
                    
                    results['trades'].append({
                        'date': current_date,
                        'type': 'ENTRY',
                        'signal': signal,
                        'price': current_price,
                        'size': position_size,
                        'leverage': leverage,
                        'mode': mode
                    })
                
                elif position != 0:
                    # Check exit conditions
                    exit_trade = False
                    exit_reason = ''
                    
                    if position > 0:  # Long position
                        if current_price <= stop_loss:
                            exit_trade = True
                            exit_reason = 'STOP_LOSS'
                        elif current_price >= take_profit:
                            exit_trade = True
                            exit_reason = 'TAKE_PROFIT'
                        elif signal == 'SELL':
                            exit_trade = True
                            exit_reason = 'SIGNAL_REVERSAL'
                    
                    else:  # Short position
                        if current_price >= stop_loss:
                            exit_trade = True
                            exit_reason = 'STOP_LOSS'
                        elif current_price <= take_profit:
                            exit_trade = True
                            exit_reason = 'TAKE_PROFIT'
                        elif signal == 'BUY':
                            exit_trade = True
                            exit_reason = 'SIGNAL_REVERSAL'
                    
                    if exit_trade:
                        # Calculate P&L
                        if position > 0:
                            pnl = (current_price - entry_price) * abs(position)
                        else:
                            pnl = (entry_price - current_price) * abs(position)
                        
                        current_equity += pnl
                        
                        results['trades'].append({
                            'date': current_date,
                            'type': 'EXIT',
                            'reason': exit_reason,
                            'price': current_price,
                            'pnl': pnl,
                            'equity': current_equity
                        })
                        
                        position = 0
                        entry_price = 0
                
                # Update peak equity and drawdown
                if current_equity > peak_equity:
                    peak_equity = current_equity
                
                drawdown = (peak_equity - current_equity) / peak_equity * 100
                
                # Store results
                results['dates'].append(current_date)
                results['prices'].append(current_price)
                results['signals'].append(signal)
                results['positions'].append(position)
                results['equity'].append(current_equity)
                results['returns'].append((current_equity / initial_balance - 1) * 100)
                results['drawdowns'].append(drawdown)
                results['modes'].append(mode)
                
            except Exception as algo_error:
                # Algorithm error - continue with neutral
                results['dates'].append(current_date)
                results['prices'].append(current_price)
                results['signals'].append('NEUTRAL')
                results['positions'].append(position)
                results['equity'].append(current_equity)
                results['returns'].append((current_equity / initial_balance - 1) * 100)
                results['drawdowns'].append((peak_equity - current_equity) / peak_equity * 100)
                results['modes'].append('conservative')
        
        # Calculate final statistics
        final_equity = current_equity
        total_return = (final_equity / initial_balance - 1) * 100
        max_drawdown = max(results['drawdowns']) if results['drawdowns'] else 0
        
        # Count trades
        entry_trades = [t for t in results['trades'] if t['type'] == 'ENTRY']
        exit_trades = [t for t in results['trades'] if t['type'] == 'EXIT']
        
        # Calculate win rate
        profitable_trades = [t for t in exit_trades if t['pnl'] > 0]
        win_rate = len(profitable_trades) / len(exit_trades) * 100 if exit_trades else 0
        
        stats = {
            'symbol': symbol,
            'initial_balance': initial_balance,
            'final_equity': final_equity,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': len(exit_trades),
            'win_rate': win_rate,
            'avg_trade_pnl': np.mean([t['pnl'] for t in exit_trades]) if exit_trades else 0,
            'results': results
        }
        
        print(f"✅ {symbol} Backtest Complete:")
        print(f"   Final Equity: ${final_equity:.2f}")
        print(f"   Total Return: {total_return:.2f}%")
        print(f"   Max Drawdown: {max_drawdown:.2f}%")
        print(f"   Total Trades: {len(exit_trades)}")
        print(f"   Win Rate: {win_rate:.1f}%")
        
        return stats
        
    except Exception as e:
        print(f"❌ Backtest error for {symbol}: {e}")
        return None

def create_comparison_charts(btc_stats, ada_stats):
    """Create side-by-side comparison charts."""
    print(f"\n📊 Chart creation disabled - focusing on numerical comparison")

    # Create detailed text-based comparison
    print(f"\n📈 DETAILED PERFORMANCE COMPARISON:")
    print("=" * 80)

    print(f"🟡 BTC DETAILED METRICS:")
    print(f"   Initial Balance: ${btc_stats['initial_balance']:,.2f}")
    print(f"   Final Equity: ${btc_stats['final_equity']:,.2f}")
    print(f"   Total Return: {btc_stats['total_return']:,.2f}%")
    print(f"   Max Drawdown: {btc_stats['max_drawdown']:.2f}%")
    print(f"   Total Trades: {btc_stats['total_trades']}")
    print(f"   Win Rate: {btc_stats['win_rate']:.1f}%")
    print(f"   Avg Trade P&L: ${btc_stats['avg_trade_pnl']:.2f}")

    print(f"\n🔵 ADA DETAILED METRICS:")
    print(f"   Initial Balance: ${ada_stats['initial_balance']:,.2f}")
    print(f"   Final Equity: ${ada_stats['final_equity']:,.2f}")
    print(f"   Total Return: {ada_stats['total_return']:,.2f}%")
    print(f"   Max Drawdown: {ada_stats['max_drawdown']:.2f}%")
    print(f"   Total Trades: {ada_stats['total_trades']}")
    print(f"   Win Rate: {ada_stats['win_rate']:.1f}%")
    print(f"   Avg Trade P&L: ${ada_stats['avg_trade_pnl']:.2f}")

    # Risk-adjusted comparison
    print(f"\n⚖️ RISK-ADJUSTED COMPARISON:")
    print("-" * 50)

    btc_sharpe = btc_stats['total_return'] / btc_stats['max_drawdown'] if btc_stats['max_drawdown'] > 0 else 0
    ada_sharpe = ada_stats['total_return'] / ada_stats['max_drawdown'] if ada_stats['max_drawdown'] > 0 else 0

    print(f"   BTC Risk-Adjusted Return: {btc_sharpe:.2f}")
    print(f"   ADA Risk-Adjusted Return: {ada_sharpe:.2f}")

    if btc_sharpe > ada_sharpe:
        print(f"   🏆 BTC has better risk-adjusted performance")
    else:
        print(f"   🏆 ADA has better risk-adjusted performance")

def main():
    """Main comparison function."""
    print("🚀 NIKE MASSIVE ROCKET: BTC vs ADA 4-YEAR COMPARISON")
    print("=" * 80)
    print("📅 Period: 2021-01-01 to Present")
    print("💰 Initial Balance: $500 USDT per asset")
    print("🎯 Algorithm: Nike's Massive Rocket (Conservative + Aggressive modes)")
    
    # Load algorithm
    algorithm = load_nike_massive_rocket()
    if not algorithm:
        print("❌ Failed to load Nike Massive Rocket algorithm")
        return
    
    # Load historical data
    print(f"\n📊 LOADING HISTORICAL DATA")
    print("-" * 50)
    
    btc_data = load_historical_data('BTC', '2021-01-01')
    ada_data = load_historical_data('ADA', '2021-01-01')
    
    if not btc_data:
        print("❌ Failed to load BTC data")
        return
    
    if not ada_data:
        print("❌ Failed to load ADA data")
        return
    
    # Run backtests
    print(f"\n🚀 RUNNING BACKTESTS")
    print("-" * 50)
    
    btc_stats = run_nike_backtest('BTC', btc_data, algorithm, 500)
    ada_stats = run_nike_backtest('ADA', ada_data, algorithm, 500)
    
    if not btc_stats or not ada_stats:
        print("❌ Backtest failed")
        return
    
    # Create comparison
    print(f"\n📊 FINAL COMPARISON RESULTS")
    print("=" * 80)
    
    print(f"🟡 BTC RESULTS:")
    print(f"   Final Equity: ${btc_stats['final_equity']:,.2f}")
    print(f"   Total Return: {btc_stats['total_return']:,.2f}%")
    print(f"   Max Drawdown: {btc_stats['max_drawdown']:.2f}%")
    print(f"   Total Trades: {btc_stats['total_trades']}")
    print(f"   Win Rate: {btc_stats['win_rate']:.1f}%")
    
    print(f"\n🔵 ADA RESULTS:")
    print(f"   Final Equity: ${ada_stats['final_equity']:,.2f}")
    print(f"   Total Return: {ada_stats['total_return']:,.2f}%")
    print(f"   Max Drawdown: {ada_stats['max_drawdown']:.2f}%")
    print(f"   Total Trades: {ada_stats['total_trades']}")
    print(f"   Win Rate: {ada_stats['win_rate']:.1f}%")
    
    # Determine winner
    print(f"\n🏆 RECOMMENDATION FOR CALEB:")
    print("-" * 50)
    
    if btc_stats['total_return'] > ada_stats['total_return']:
        winner = 'BTC'
        winner_return = btc_stats['total_return']
        winner_drawdown = btc_stats['max_drawdown']
    else:
        winner = 'ADA'
        winner_return = ada_stats['total_return']
        winner_drawdown = ada_stats['max_drawdown']
    
    print(f"🎯 WINNER: {winner}")
    print(f"   Superior Return: {winner_return:,.2f}%")
    print(f"   Max Drawdown: {winner_drawdown:.2f}%")
    print(f"   Recommended for live trading with Nike Massive Rocket")
    
    # Create detailed comparison
    create_comparison_charts(btc_stats, ada_stats)
    
    print(f"\n✅ COMPARISON COMPLETE!")
    print(f"🚀 Ready to configure live trading system with {winner}")

if __name__ == "__main__":
    main()
