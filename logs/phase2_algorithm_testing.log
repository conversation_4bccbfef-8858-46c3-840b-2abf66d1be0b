2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Loaded personal environment variables from .env.personal
2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Using YOUR API Key: 6e9dd1a7...
2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Using YOUR Secret: qY6u0gH5...
2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Testnet: False
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - Initialized Personal Phemex service
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - Account: PERSONAL_CASHCOLDGAME
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - Default Leverage: 10x
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - BTC Position Size: 0.5%
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - ADA Position Size: 0.3%
2025-06-15 10:48:26,918 - __main__ - INFO - 🚀 Starting Phase 2: Algorithm Integration Testing
2025-06-15 10:48:26,919 - __main__ - INFO - ============================================================
2025-06-15 10:48:26,919 - __main__ - INFO - PURPOSE: Test TITAN algorithms with fixed Phemex service
2025-06-15 10:48:26,919 - __main__ - INFO - MODELS: TITAN2K (BTC) + Trend-Tuned (ADA)
2025-06-15 10:48:26,919 - __main__ - INFO - THRESHOLDS: Original backtested (0.4)
2025-06-15 10:48:26,919 - __main__ - INFO - ============================================================
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: BTC TITAN2K Signal Generation
2025-06-15 10:48:26,919 - __main__ - INFO - 🔍 Testing BTC TITAN2K signal generation
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - INFO - ❌ FAIL BTC Signal Generation: Failed to get market data
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: ADA Trend-Tuned Signal Generation
2025-06-15 10:48:26,919 - __main__ - INFO - 🔍 Testing ADA Trend-Tuned signal generation
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - INFO - ❌ FAIL ADA Signal Generation: Failed to get market data
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: Confidence Threshold Filtering
2025-06-15 10:48:26,919 - __main__ - INFO - 🔍 Testing confidence threshold filtering
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - INFO - ❌ FAIL Confidence Filtering: Failed to get market data
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: Position Size Calculation
2025-06-15 10:48:26,920 - __main__ - INFO - 🔍 Testing algorithmic position size calculation
2025-06-15 10:48:29,904 - __main__ - INFO - ✅ PASS Position Size Calculation: BTC: 0.002310 ($244.08), ADA: 192.49 ($122.04)
2025-06-15 10:48:29,904 - __main__ - INFO - 
🔬 Running: ATR-based Risk Management
2025-06-15 10:48:29,904 - __main__ - INFO - 🔍 Testing ATR-based risk management
2025-06-15 10:48:29,904 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,904 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,904 - __main__ - INFO - ❌ FAIL Risk Management: Failed to get market data
2025-06-15 10:48:29,904 - __main__ - INFO - 
🔬 Running: Signal-to-Execution Pipeline
2025-06-15 10:48:29,905 - __main__ - INFO - 🔍 Testing signal-to-execution pipeline
2025-06-15 10:48:29,905 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,905 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Signal Pipeline: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - 
============================================================
2025-06-15 10:48:29,905 - __main__ - INFO - 📊 PHASE 2 TEST RESULTS
2025-06-15 10:48:29,905 - __main__ - INFO - ============================================================
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL BTC Signal Generation [CRITICAL]: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL ADA Signal Generation [CRITICAL]: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Confidence Filtering: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ✅ PASS Position Size Calculation: BTC: 0.002310 ($244.08), ADA: 192.49 ($122.04)
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Risk Management: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Signal Pipeline: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - 
🎯 SUMMARY:
2025-06-15 10:48:29,905 - __main__ - INFO - Total Tests: 1/6 passed
2025-06-15 10:48:29,905 - __main__ - INFO - Critical Tests: 0/3 passed
2025-06-15 10:48:29,905 - __main__ - ERROR - 🚨 CRITICAL FAILURES: BTC Signal Generation, ADA Signal Generation
2025-06-15 10:48:29,905 - __main__ - ERROR - ❌ Phase 2 FAILED - Cannot proceed to Phase 3
2025-06-15 10:48:29,905 - __main__ - INFO - 
🔧 Please resolve critical failures before proceeding
2025-06-15 10:49:24,474 - services.personal_phemex_service - INFO - Loaded personal environment variables from .env.personal
2025-06-15 10:49:24,474 - services.personal_phemex_service - INFO - Using YOUR API Key: 6e9dd1a7...
2025-06-15 10:49:24,474 - services.personal_phemex_service - INFO - Using YOUR Secret: qY6u0gH5...
2025-06-15 10:49:24,474 - services.personal_phemex_service - INFO - Testnet: False
2025-06-15 10:49:24,478 - services.personal_phemex_service - INFO - Initialized Personal Phemex service
2025-06-15 10:49:24,478 - services.personal_phemex_service - INFO - Account: PERSONAL_CASHCOLDGAME
2025-06-15 10:49:24,478 - services.personal_phemex_service - INFO - Default Leverage: 10x
2025-06-15 10:49:24,479 - services.personal_phemex_service - INFO - BTC Position Size: 0.5%
2025-06-15 10:49:24,479 - services.personal_phemex_service - INFO - ADA Position Size: 0.3%
2025-06-15 10:49:24,479 - __main__ - INFO - 🚀 Starting Phase 2: Algorithm Integration Testing
2025-06-15 10:49:24,479 - __main__ - INFO - ============================================================
2025-06-15 10:49:24,479 - __main__ - INFO - PURPOSE: Test TITAN algorithms with fixed Phemex service
2025-06-15 10:49:24,479 - __main__ - INFO - MODELS: TITAN2K (BTC) + Trend-Tuned (ADA)
2025-06-15 10:49:24,479 - __main__ - INFO - THRESHOLDS: Original backtested (0.4)
2025-06-15 10:49:24,479 - __main__ - INFO - ============================================================
2025-06-15 10:49:24,479 - __main__ - INFO - 
🔬 Running: BTC TITAN2K Signal Generation
2025-06-15 10:49:24,479 - __main__ - INFO - 🔍 Testing BTC TITAN2K signal generation
2025-06-15 10:49:24,479 - services.market_data_service - INFO - Fetching BTC 1d data from 2024-06-15 to 2025-06-15
2025-06-15 10:49:24,737 - services.market_data_service - INFO - Fetched 365 candles
2025-06-15 10:49:24,738 - __main__ - INFO - ✅ BTC 1d: 365 candles loaded
2025-06-15 10:49:24,738 - services.market_data_service - INFO - Fetching BTC 4h data from 2024-06-15 to 2025-06-15
2025-06-15 10:49:24,954 - services.market_data_service - INFO - Fetched 717 candles
2025-06-15 10:49:24,955 - __main__ - INFO - ✅ BTC 4h: 717 candles loaded
2025-06-15 10:49:24,955 - services.market_data_service - INFO - Fetching BTC 1h data from 2024-06-15 to 2025-06-15
2025-06-15 10:49:25,157 - services.market_data_service - INFO - Fetched 706 candles
2025-06-15 10:49:25,157 - __main__ - INFO - ✅ BTC 1h: 706 candles loaded
2025-06-15 10:49:27,289 - __main__ - INFO - ❌ FAIL BTC Signal Generation: Invalid signal: NEUTRAL
2025-06-15 10:49:27,289 - __main__ - INFO - 
🔬 Running: ADA Trend-Tuned Signal Generation
2025-06-15 10:49:27,289 - __main__ - INFO - 🔍 Testing ADA Trend-Tuned signal generation
2025-06-15 10:49:27,289 - services.market_data_service - INFO - Fetching ADA 1d data from 2024-06-15 to 2025-06-15
2025-06-15 10:49:27,293 - services.market_data_service - INFO - Loaded 290 rows from data_seed
2025-06-15 10:49:27,293 - __main__ - INFO - ✅ ADA 1d: 290 candles loaded
2025-06-15 10:49:27,294 - services.market_data_service - INFO - Fetching ADA 1h data from 2024-06-15 to 2025-06-15
2025-06-15 10:49:27,322 - services.market_data_service - INFO - Loaded 6943 rows from data_seed
2025-06-15 10:49:27,322 - __main__ - INFO - ✅ ADA 1h: 6943 candles loaded
2025-06-15 10:49:27,322 - services.market_data_service - INFO - Fetching ADA 15m data from 2024-06-15 to 2025-06-15
2025-06-15 10:49:27,412 - services.market_data_service - INFO - Loaded 25120 rows from data_seed
2025-06-15 10:49:27,412 - __main__ - INFO - ✅ ADA 15m: 25120 candles loaded
2025-06-15 10:51:07,948 - __main__ - INFO - ❌ FAIL ADA Signal Generation: Invalid signal: NEUTRAL
2025-06-15 10:51:07,953 - __main__ - INFO - 
🔬 Running: Confidence Threshold Filtering
2025-06-15 10:51:07,953 - __main__ - INFO - 🔍 Testing confidence threshold filtering
2025-06-15 10:51:07,953 - services.market_data_service - INFO - Fetching BTC 1d data from 2024-06-15 to 2025-06-15
2025-06-15 10:51:08,199 - services.market_data_service - INFO - Fetched 365 candles
2025-06-15 10:51:08,199 - __main__ - INFO - ✅ BTC 1d: 365 candles loaded
2025-06-15 10:51:08,199 - services.market_data_service - INFO - Fetching BTC 4h data from 2024-06-15 to 2025-06-15
2025-06-15 10:51:08,398 - services.market_data_service - INFO - Fetched 717 candles
2025-06-15 10:51:08,398 - __main__ - INFO - ✅ BTC 4h: 717 candles loaded
2025-06-15 10:51:08,398 - services.market_data_service - INFO - Fetching BTC 1h data from 2024-06-15 to 2025-06-15
2025-06-15 10:51:08,565 - services.market_data_service - INFO - Fetched 706 candles
2025-06-15 10:51:08,566 - __main__ - INFO - ✅ BTC 1h: 706 candles loaded
2025-06-15 10:51:08,566 - services.market_data_service - INFO - Fetching ADA 1d data from 2024-06-15 to 2025-06-15
2025-06-15 10:51:08,570 - services.market_data_service - INFO - Loaded 290 rows from data_seed
2025-06-15 10:51:08,570 - __main__ - INFO - ✅ ADA 1d: 290 candles loaded
2025-06-15 10:51:08,570 - services.market_data_service - INFO - Fetching ADA 1h data from 2024-06-15 to 2025-06-15
2025-06-15 10:51:08,598 - services.market_data_service - INFO - Loaded 6943 rows from data_seed
2025-06-15 10:51:08,598 - __main__ - INFO - ✅ ADA 1h: 6943 candles loaded
2025-06-15 10:51:08,598 - services.market_data_service - INFO - Fetching ADA 15m data from 2024-06-15 to 2025-06-15
2025-06-15 10:51:08,688 - services.market_data_service - INFO - Loaded 25120 rows from data_seed
2025-06-15 10:51:08,688 - __main__ - INFO - ✅ ADA 15m: 25120 candles loaded
