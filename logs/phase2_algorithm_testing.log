2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Loaded personal environment variables from .env.personal
2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Using YOUR API Key: 6e9dd1a7...
2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Using YOUR Secret: qY6u0gH5...
2025-06-15 10:48:26,914 - services.personal_phemex_service - INFO - Testnet: False
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - Initialized Personal Phemex service
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - Account: PERSONAL_CASHCOLDGAME
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - Default Leverage: 10x
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - BTC Position Size: 0.5%
2025-06-15 10:48:26,918 - services.personal_phemex_service - INFO - ADA Position Size: 0.3%
2025-06-15 10:48:26,918 - __main__ - INFO - 🚀 Starting Phase 2: Algorithm Integration Testing
2025-06-15 10:48:26,919 - __main__ - INFO - ============================================================
2025-06-15 10:48:26,919 - __main__ - INFO - PURPOSE: Test TITAN algorithms with fixed Phemex service
2025-06-15 10:48:26,919 - __main__ - INFO - MODELS: TITAN2K (BTC) + Trend-Tuned (ADA)
2025-06-15 10:48:26,919 - __main__ - INFO - THRESHOLDS: Original backtested (0.4)
2025-06-15 10:48:26,919 - __main__ - INFO - ============================================================
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: BTC TITAN2K Signal Generation
2025-06-15 10:48:26,919 - __main__ - INFO - 🔍 Testing BTC TITAN2K signal generation
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - INFO - ❌ FAIL BTC Signal Generation: Failed to get market data
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: ADA Trend-Tuned Signal Generation
2025-06-15 10:48:26,919 - __main__ - INFO - 🔍 Testing ADA Trend-Tuned signal generation
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - INFO - ❌ FAIL ADA Signal Generation: Failed to get market data
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: Confidence Threshold Filtering
2025-06-15 10:48:26,919 - __main__ - INFO - 🔍 Testing confidence threshold filtering
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:26,919 - __main__ - INFO - ❌ FAIL Confidence Filtering: Failed to get market data
2025-06-15 10:48:26,919 - __main__ - INFO - 
🔬 Running: Position Size Calculation
2025-06-15 10:48:26,920 - __main__ - INFO - 🔍 Testing algorithmic position size calculation
2025-06-15 10:48:29,904 - __main__ - INFO - ✅ PASS Position Size Calculation: BTC: 0.002310 ($244.08), ADA: 192.49 ($122.04)
2025-06-15 10:48:29,904 - __main__ - INFO - 
🔬 Running: ATR-based Risk Management
2025-06-15 10:48:29,904 - __main__ - INFO - 🔍 Testing ATR-based risk management
2025-06-15 10:48:29,904 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,904 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,904 - __main__ - INFO - ❌ FAIL Risk Management: Failed to get market data
2025-06-15 10:48:29,904 - __main__ - INFO - 
🔬 Running: Signal-to-Execution Pipeline
2025-06-15 10:48:29,905 - __main__ - INFO - 🔍 Testing signal-to-execution pipeline
2025-06-15 10:48:29,905 - __main__ - ERROR - Error getting market data for BTC: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,905 - __main__ - ERROR - Error getting market data for ADA: 'MarketDataService' object has no attribute 'get_ohlcv_data'
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Signal Pipeline: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - 
============================================================
2025-06-15 10:48:29,905 - __main__ - INFO - 📊 PHASE 2 TEST RESULTS
2025-06-15 10:48:29,905 - __main__ - INFO - ============================================================
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL BTC Signal Generation [CRITICAL]: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL ADA Signal Generation [CRITICAL]: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Confidence Filtering: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ✅ PASS Position Size Calculation: BTC: 0.002310 ($244.08), ADA: 192.49 ($122.04)
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Risk Management: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - ❌ FAIL Signal Pipeline: Failed to get market data
2025-06-15 10:48:29,905 - __main__ - INFO - 
🎯 SUMMARY:
2025-06-15 10:48:29,905 - __main__ - INFO - Total Tests: 1/6 passed
2025-06-15 10:48:29,905 - __main__ - INFO - Critical Tests: 0/3 passed
2025-06-15 10:48:29,905 - __main__ - ERROR - 🚨 CRITICAL FAILURES: BTC Signal Generation, ADA Signal Generation
2025-06-15 10:48:29,905 - __main__ - ERROR - ❌ Phase 2 FAILED - Cannot proceed to Phase 3
2025-06-15 10:48:29,905 - __main__ - INFO - 
🔧 Please resolve critical failures before proceeding
