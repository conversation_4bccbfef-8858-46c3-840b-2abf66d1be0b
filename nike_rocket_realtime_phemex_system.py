#!/usr/bin/env python3
"""
Nike's Rocket Real-Time Phemex Trading System

CALEB's Instructions Implementation:
- Real-time WebSocket monitoring (no 5-minute polling)
- Multi-account setup for institutional trading
- Massive Rocket and Baby Rocket algorithms
- Real-time P&L monitoring
- Flawless Phemex API integration

Account Structure:
- CALEB_MAIN: Massive Rocket BTC
- CALEB_SUB1: Massive Rocket ADA  
- CALEB_SUB2: Baby Rocket BTC (future)
- CALEB_SUB3: Baby Rocket ADA (future)
"""

import asyncio
import logging
import ccxt.pro as ccxt
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, List
import json
import os
from dotenv import load_dotenv

# Import Nike's algorithms
import sys
sys.path.append('/Users/<USER>/TomorrowTech/python-backend/data_seed')

logger = logging.getLogger(__name__)

class NikeRocketRealtimeSystem:
    """Real-time Nike Rocket trading system with WebSocket monitoring."""
    
    def __init__(self):
        """Initialize the real-time trading system."""
        self.running = False
        self.exchanges = {}
        self.algorithms = {}
        self.positions = {}
        self.last_signals = {}
        
        # Account configuration per CALEB's instructions
        self.accounts = {
            'CALEB_MAIN': {
                'algorithm': 'massive_rocket',
                'symbol': 'BTC',
                'api_key': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
                'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM'
            },
            'CALEB_SUB1': {
                'algorithm': 'massive_rocket', 
                'symbol': 'ADA',
                'api_key': 'a2cfaa90-469c-41d6-b954-7853887d1d7d',
                'secret': 'wm79OAQzbMIb7EjniYf1pU-buGaQ4PJf5L5awFjXXcw0MzE2MDUwZC03MGQ4LTQ4MzEtOWE0NC00N2I1OTRhYmQzNzI'
            }
            # Future accounts for Baby Rocket will be added here
        }
        
        self.initialize_system()
    
    def initialize_system(self):
        """Initialize algorithms and exchange connections."""
        logger.info("🚀 Initializing Nike Rocket Real-Time System")
        
        # Load Nike's algorithms (handle spaces in filenames)
        try:
            import importlib.util

            # Load Baby Rocket
            baby_spec = importlib.util.spec_from_file_location(
                "NikesBabyRocket",
                "/Users/<USER>/TomorrowTech/python-backend/data_seed/Nike's Baby Rocket Algo.py"
            )
            baby_module = importlib.util.module_from_spec(baby_spec)
            baby_spec.loader.exec_module(baby_module)

            # Load Massive Rocket
            massive_spec = importlib.util.spec_from_file_location(
                "NikesMassiveRocket",
                "/Users/<USER>/TomorrowTech/python-backend/data_seed/Nike's Massive Rocket Algo.py"
            )
            massive_module = importlib.util.module_from_spec(massive_spec)
            massive_spec.loader.exec_module(massive_module)

            self.algorithms = {
                'baby_rocket': baby_module.NikesBabyRocket(),
                'massive_rocket': massive_module.NikesMassiveRocket()
            }
            logger.info("✅ Nike Rocket algorithms loaded successfully")
            
        except Exception as e:
            logger.error(f"❌ Error loading Nike algorithms: {e}")
            raise
        
        # Initialize exchange connections for each account
        for account_name, config in self.accounts.items():
            try:
                exchange = ccxt.phemex({
                    'apiKey': config['api_key'],
                    'secret': config['secret'],
                    'sandbox': False,  # Production
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'swap'  # Perpetual contracts
                    }
                })
                
                self.exchanges[account_name] = exchange
                logger.info(f"✅ {account_name} exchange initialized ({config['algorithm']} {config['symbol']})")
                
            except Exception as e:
                logger.error(f"❌ Error initializing {account_name}: {e}")
    
    async def start_realtime_monitoring(self):
        """Start real-time WebSocket monitoring for all accounts."""
        logger.info("🌐 Starting real-time WebSocket monitoring")
        self.running = True
        
        # Create monitoring tasks for each account
        tasks = []
        
        for account_name, config in self.accounts.items():
            # Price monitoring task
            tasks.append(asyncio.create_task(
                self.monitor_price_realtime(account_name, config['symbol'])
            ))
            
            # Position monitoring task
            tasks.append(asyncio.create_task(
                self.monitor_positions_realtime(account_name)
            ))
            
            # Signal generation task
            tasks.append(asyncio.create_task(
                self.generate_signals_realtime(account_name, config)
            ))
        
        # Wait for all tasks
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"❌ Error in real-time monitoring: {e}")
        finally:
            self.running = False
    
    async def monitor_price_realtime(self, account_name: str, symbol: str):
        """Monitor real-time price updates via WebSocket."""
        exchange = self.exchanges[account_name]
        phemex_symbol = f"{symbol}/USDT:USDT"
        
        logger.info(f"📊 Starting real-time price monitoring: {account_name} {symbol}")
        
        try:
            while self.running:
                # Watch ticker for real-time price updates
                ticker = await exchange.watch_ticker(phemex_symbol)
                
                # Update price data
                price_data = {
                    'symbol': symbol,
                    'price': ticker['last'],
                    'bid': ticker['bid'],
                    'ask': ticker['ask'],
                    'timestamp': ticker['timestamp'],
                    'datetime': ticker['datetime']
                }
                
                # Process price update
                await self.process_price_update(account_name, price_data)
                
        except Exception as e:
            logger.error(f"❌ Price monitoring error for {account_name}: {e}")
    
    async def monitor_positions_realtime(self, account_name: str):
        """Monitor real-time position updates and P&L."""
        exchange = self.exchanges[account_name]
        
        logger.info(f"💰 Starting real-time P&L monitoring: {account_name}")
        
        try:
            while self.running:
                # Get current positions
                positions = await exchange.fetch_positions()
                
                # Filter active positions
                active_positions = [pos for pos in positions if pos['contracts'] != 0]
                
                if active_positions:
                    for position in active_positions:
                        pnl_data = {
                            'account': account_name,
                            'symbol': position['symbol'],
                            'side': position['side'],
                            'size': position['contracts'],
                            'entry_price': position['entryPrice'],
                            'mark_price': position['markPrice'],
                            'unrealized_pnl': position['unrealizedPnl'],
                            'percentage': position['percentage'],
                            'timestamp': datetime.now().isoformat()
                        }
                        
                        # Process P&L update
                        await self.process_pnl_update(account_name, pnl_data)
                
                # Update every 1 second for real-time monitoring
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"❌ Position monitoring error for {account_name}: {e}")
    
    async def generate_signals_realtime(self, account_name: str, config: Dict[str, Any]):
        """Generate trading signals in real-time using Nike algorithms."""
        algorithm = self.algorithms[config['algorithm']]
        symbol = config['symbol']
        
        logger.info(f"🎯 Starting real-time signal generation: {account_name} {config['algorithm']} {symbol}")
        
        try:
            while self.running:
                # Get multi-timeframe data
                data = await self.get_multi_timeframe_data(account_name, symbol)
                
                if data:
                    # Process through Nike algorithm
                    signals = await self.process_nike_algorithm(algorithm, data, account_name)
                    
                    if signals and signals['signal'] != 'NEUTRAL':
                        # Execute trade if signal is valid
                        await self.execute_nike_trade(account_name, signals)
                
                # Check for signals every 30 seconds (much faster than 5 minutes)
                await asyncio.sleep(30)
                
        except Exception as e:
            logger.error(f"❌ Signal generation error for {account_name}: {e}")
    
    async def get_multi_timeframe_data(self, account_name: str, symbol: str) -> Dict[str, pd.DataFrame]:
        """Get synchronized multi-timeframe data for Nike algorithms."""
        exchange = self.exchanges[account_name]
        phemex_symbol = f"{symbol}/USDT:USDT"
        
        try:
            data = {}
            timeframes = ['1d', '4h', '1h']
            
            for tf in timeframes:
                ohlcv = await exchange.fetch_ohlcv(phemex_symbol, tf, limit=200)
                
                if ohlcv:
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                    
                    # Convert to format expected by Nike algorithms
                    df.set_index('timestamp', inplace=True)
                    data[tf] = df
            
            return data
            
        except Exception as e:
            logger.error(f"❌ Error fetching multi-timeframe data: {e}")
            return {}
    
    async def process_nike_algorithm(self, algorithm, data: Dict[str, pd.DataFrame], account_name: str) -> Optional[Dict[str, Any]]:
        """Process data through Nike algorithm to generate signals."""
        try:
            # Calculate indicators for each timeframe
            processed_data = {}
            for tf, df in data.items():
                processed_data[tf] = algorithm.calculate_indicators(df)
            
            # Combine timeframes
            combined_df = algorithm.combine_timeframes(
                processed_data['1d'], 
                processed_data['4h'], 
                processed_data['1h']
            )
            
            # Generate signals with compounding
            result_df = algorithm.generate_signals_with_compounding_and_reversal(
                combined_df, initial_equity=10000
            )
            
            # Extract latest signal
            if not result_df.empty:
                latest = result_df.iloc[-1]
                
                signal_data = {
                    'signal': latest['signal'],
                    'mode_used': latest.get('mode_used', 'aggressive'),
                    'confidence': latest.get('confidence', 0.5),
                    'entry_price': latest['close'],
                    'stop_loss': latest.get('stop_loss', 0),
                    'take_profit': latest.get('take_profit', 0),
                    'position_size': latest.get('position_size', 0),
                    'account': account_name,
                    'timestamp': datetime.now().isoformat()
                }
                
                return signal_data
            
        except Exception as e:
            logger.error(f"❌ Error processing Nike algorithm: {e}")
        
        return None
    
    async def execute_nike_trade(self, account_name: str, signal_data: Dict[str, Any]):
        """Execute trade based on Nike algorithm signal."""
        exchange = self.exchanges[account_name]
        config = self.accounts[account_name]
        symbol = config['symbol']
        phemex_symbol = f"{symbol}/USDT:USDT"
        
        try:
            logger.info(f"🚀 Executing Nike trade: {account_name} {signal_data['signal']} {symbol}")
            
            # Calculate position size based on algorithm type
            position_size = await self.calculate_nike_position_size(
                account_name, signal_data, config['algorithm']
            )
            
            if position_size > 0:
                # Execute atomic trade (entry + stop loss + take profit)
                result = await self.execute_atomic_trade(
                    exchange, phemex_symbol, signal_data, position_size
                )
                
                if result['success']:
                    logger.info(f"✅ Nike trade executed successfully: {account_name}")
                    
                    # Send to AXON AI
                    await self.send_axon_signal(account_name, signal_data, result)
                    
                    # Send Discord alert
                    await self.send_discord_alert(account_name, signal_data, result)
                else:
                    logger.error(f"❌ Nike trade failed: {account_name} - {result}")
            
        except Exception as e:
            logger.error(f"❌ Error executing Nike trade: {e}")
    
    async def calculate_nike_position_size(self, account_name: str, signal_data: Dict[str, Any], algorithm_type: str) -> float:
        """Calculate position size based on Nike algorithm parameters."""
        try:
            exchange = self.exchanges[account_name]
            balance = await exchange.fetch_balance()
            account_equity = balance['USDT']['total']
            
            mode = signal_data['mode_used']
            
            # Nike algorithm risk parameters
            if algorithm_type == 'baby_rocket':
                risk_pct = 0.01 if mode == 'conservative' else 0.02  # 1%/2%
            else:  # massive_rocket
                risk_pct = 0.02 if mode == 'conservative' else 0.04  # 2%/4%
            
            # Calculate position size
            risk_amount = account_equity * risk_pct
            entry_price = signal_data['entry_price']
            stop_loss = signal_data['stop_loss']
            
            if stop_loss > 0:
                stop_distance = abs(entry_price - stop_loss)
                position_size = risk_amount / stop_distance
                
                # Apply leverage (up to 10x as per TITAN2K)
                leverage = min(10, max(1, position_size / account_equity))
                final_position_size = risk_amount * leverage / entry_price
                
                logger.info(f"💰 Position size calculated: {account_name} - ${final_position_size:.2f} ({risk_pct*100}% risk, {leverage:.1f}x leverage)")
                return final_position_size
            
        except Exception as e:
            logger.error(f"❌ Error calculating position size: {e}")
        
        return 0
    
    async def execute_atomic_trade(self, exchange, symbol: str, signal_data: Dict[str, Any], position_size: float) -> Dict[str, Any]:
        """Execute atomic trade with entry + stop loss + take profit."""
        try:
            side = 'buy' if signal_data['signal'] == 'BUY' else 'sell'
            
            # Place market entry order
            entry_order = await exchange.create_market_order(symbol, side, position_size)
            
            if entry_order:
                # Place stop loss
                sl_side = 'sell' if side == 'buy' else 'buy'
                stop_order = await exchange.create_order(
                    symbol, 'stopLimit', sl_side, position_size,
                    signal_data['stop_loss'],
                    params={
                        'stopPx': signal_data['stop_loss'],
                        'reduceOnly': True
                    }
                )
                
                # Place take profit
                tp_order = await exchange.create_order(
                    symbol, 'limit', sl_side, position_size,
                    signal_data['take_profit'],
                    params={'reduceOnly': True}
                )
                
                return {
                    'success': True,
                    'entry_order': entry_order,
                    'stop_order': stop_order,
                    'tp_order': tp_order
                }
            
        except Exception as e:
            logger.error(f"❌ Atomic trade execution error: {e}")
        
        return {'success': False, 'error': str(e)}
    
    async def process_price_update(self, account_name: str, price_data: Dict[str, Any]):
        """Process real-time price updates."""
        # Log price updates (can be sent to monitoring systems)
        logger.debug(f"📊 {account_name} {price_data['symbol']}: ${price_data['price']:.4f}")
    
    async def process_pnl_update(self, account_name: str, pnl_data: Dict[str, Any]):
        """Process real-time P&L updates."""
        # Log P&L updates
        pnl = pnl_data['unrealized_pnl']
        pct = pnl_data['percentage']
        
        logger.info(f"💰 {account_name} P&L: ${pnl:.2f} ({pct:.2f}%)")
        
        # Send to monitoring systems
        await self.send_pnl_update(account_name, pnl_data)
    
    async def send_axon_signal(self, account_name: str, signal_data: Dict[str, Any], trade_result: Dict[str, Any]):
        """Send signal to AXON AI frontend."""
        # Implementation for AXON integration
        pass
    
    async def send_discord_alert(self, account_name: str, signal_data: Dict[str, Any], trade_result: Dict[str, Any]):
        """Send trade alert to Discord."""
        # Implementation for Discord integration
        pass
    
    async def send_pnl_update(self, account_name: str, pnl_data: Dict[str, Any]):
        """Send P&L update to monitoring systems."""
        # Implementation for real-time P&L monitoring
        pass

async def main():
    """Main function to start Nike Rocket real-time system."""
    logger.info("🚀 Starting Nike Rocket Real-Time Phemex System")
    
    system = NikeRocketRealtimeSystem()
    
    try:
        await system.start_realtime_monitoring()
    except KeyboardInterrupt:
        logger.info("⏹️ System stopped by user")
    except Exception as e:
        logger.error(f"❌ System error: {e}")

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the system
    asyncio.run(main())
