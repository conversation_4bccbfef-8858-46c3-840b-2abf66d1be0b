#!/usr/bin/env python3
"""
Debug script to check actual combined_trend values from the models
"""

import os
import sys
import asyncio
import pandas as pd
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService

async def debug_trend_values():
    """Check what the actual combined_trend values are"""
    
    print("🔍 DEBUGGING TREND VALUES")
    print("="*50)
    
    # Initialize services
    market_data = MarketDataService()
    btc_model = TITAN2KModel(aggressive_mode=True)
    ada_model = TITAN2KTrendTuned(aggressive_mode=True)
    
    print(f"📊 BTC Model Threshold: {btc_model.trend_strength_threshold}")
    print(f"📊 ADA Model Threshold: {ada_model.trend_strength_threshold}")
    print()
    
    # Get recent data
    end_date = datetime.now()
    start_date = end_date - timedelta(days=90)
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    
    # Test BTC
    print("🔍 TESTING BTC MODEL:")
    try:
        # Get BTC data
        daily_df = await market_data.get_historical_data("BTC", "1d", start_date_str, end_date_str)
        medium_df = await market_data.get_historical_data("BTC", "4h", start_date_str, end_date_str)
        lower_df = await market_data.get_historical_data("BTC", "1h", start_date_str, end_date_str)
        
        # Process with BTC model
        result_df = btc_model.process_data(daily_df, medium_df, lower_df)
        
        # Get last 10 rows
        last_rows = result_df.tail(10)
        
        print("📈 Last 10 BTC combined_trend values:")
        for i, row in last_rows.iterrows():
            trend = row.get('combined_trend', 'N/A')
            signal = row.get('signal', 'N/A')
            confidence = row.get('confidence', 0)
            close = row.get('close', 0)
            
            status = "🟢 SIGNAL!" if signal in ['BUY', 'SELL'] else "🔴 NO SIGNAL"
            print(f"  ${close:,.0f} | Trend: {trend:.3f} | Signal: {signal} | Conf: {confidence:.2f} | {status}")
        
        # Check if any recent signals
        recent_signals = result_df[result_df['signal'].isin(['BUY', 'SELL'])].tail(5)
        if not recent_signals.empty:
            print(f"\n✅ Found {len(recent_signals)} recent BTC signals!")
            for i, row in recent_signals.iterrows():
                print(f"  {row['signal']} at ${row['close']:,.0f} (trend: {row['combined_trend']:.3f})")
        else:
            print("\n❌ No recent BTC signals found")
            
    except Exception as e:
        print(f"❌ Error testing BTC: {str(e)}")
    
    print("\n" + "="*50)
    
    # Test ADA
    print("🔍 TESTING ADA MODEL:")
    try:
        # Get ADA data
        daily_df = await market_data.get_historical_data("ADA", "1d", start_date_str, end_date_str)
        hourly_df = await market_data.get_historical_data("ADA", "1h", start_date_str, end_date_str)
        minute15_df = await market_data.get_historical_data("ADA", "15m", start_date_str, end_date_str)
        
        # Process with ADA model
        result_df = ada_model.process_data(daily_df, hourly_df, minute15_df)
        
        # Get last 10 rows
        last_rows = result_df.tail(10)
        
        print("📈 Last 10 ADA combined_trend values:")
        for i, row in last_rows.iterrows():
            trend = row.get('combined_trend', 'N/A')
            signal = row.get('signal', 'N/A')
            confidence = row.get('confidence', 0)
            close = row.get('close', 0)
            
            status = "🟢 SIGNAL!" if signal in ['BUY', 'SELL'] else "🔴 NO SIGNAL"
            print(f"  ${close:.3f} | Trend: {trend:.3f} | Signal: {signal} | Conf: {confidence:.2f} | {status}")
        
        # Check if any recent signals
        recent_signals = result_df[result_df['signal'].isin(['BUY', 'SELL'])].tail(5)
        if not recent_signals.empty:
            print(f"\n✅ Found {len(recent_signals)} recent ADA signals!")
            for i, row in recent_signals.iterrows():
                print(f"  {row['signal']} at ${row['close']:.3f} (trend: {row['combined_trend']:.3f})")
        else:
            print("\n❌ No recent ADA signals found")
            
    except Exception as e:
        print(f"❌ Error testing ADA: {str(e)}")
    
    print("\n" + "="*50)
    print("🎯 SUMMARY:")
    print("If combined_trend values are consistently below the threshold (0.4),")
    print("then the algorithms are working correctly but market conditions")
    print("are not favorable for trading.")
    print()
    print("If you want more trades, you can:")
    print("1. Lower the trend_strength_threshold (more risky)")
    print("2. Wait for better market conditions (recommended)")

if __name__ == "__main__":
    asyncio.run(debug_trend_values())
