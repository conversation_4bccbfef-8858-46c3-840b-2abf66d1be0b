#!/usr/bin/env python3
"""
Test Sync vs Async Phemex

Test if the issue is with async vs sync CCXT usage.
"""

import ccxt
import ccxt.pro as ccxt_pro
import asyncio

def test_sync_phemex():
    """Test synchronous Phemex."""
    print("🔄 TESTING SYNCHRONOUS PHEMEX")
    print("=" * 50)
    
    try:
        # Synchronous exchange
        exchange = ccxt.phemex({
            'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
            'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
            'sandbox': False,
            'enableRateLimit': True,
            'options': {'defaultType': 'swap'}
        })
        
        # Load markets
        markets = exchange.load_markets()
        print(f"✅ Markets loaded: {len(markets)}")
        
        # Test symbols
        test_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        test_timeframes = ['1h', '4h', '1d']
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}:")
            
            for timeframe in test_timeframes:
                try:
                    print(f"   {timeframe}...", end=" ")
                    
                    # Synchronous fetch
                    ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=5)
                    
                    if ohlcv and len(ohlcv) > 0:
                        print(f"✅ WORKS ({len(ohlcv)} candles)")
                        latest = ohlcv[-1]
                        print(f"      Latest: ${latest[4]:.4f}")
                    else:
                        print(f"❌ No data")
                        
                except Exception as e:
                    print(f"❌ {str(e)[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Sync test failed: {e}")
        return False

async def test_async_phemex():
    """Test asynchronous Phemex."""
    print(f"\n⚡ TESTING ASYNCHRONOUS PHEMEX")
    print("=" * 50)
    
    try:
        # Asynchronous exchange
        exchange = ccxt_pro.phemex({
            'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
            'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
            'sandbox': False,
            'enableRateLimit': True,
            'options': {'defaultType': 'swap'}
        })
        
        # Load markets
        markets = await exchange.load_markets()
        print(f"✅ Markets loaded: {len(markets)}")
        
        # Test symbols
        test_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        test_timeframes = ['1h', '4h', '1d']
        
        for symbol in test_symbols:
            print(f"\n📊 Testing {symbol}:")
            
            for timeframe in test_timeframes:
                try:
                    print(f"   {timeframe}...", end=" ")
                    
                    # Asynchronous fetch
                    ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=5)
                    
                    if ohlcv and len(ohlcv) > 0:
                        print(f"✅ WORKS ({len(ohlcv)} candles)")
                        latest = ohlcv[-1]
                        print(f"      Latest: ${latest[4]:.4f}")
                    else:
                        print(f"❌ No data")
                        
                except Exception as e:
                    print(f"❌ {str(e)[:50]}...")
        
        await exchange.close()
        return True
        
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False

def test_working_example():
    """Test the exact working example from our codebase."""
    print(f"\n✅ TESTING WORKING EXAMPLE FROM CODEBASE")
    print("=" * 50)
    
    try:
        # Exact same setup as working examples
        exchange = ccxt.phemex({
            'apiKey': "71b3cd21-f622-4328-816b-e7d7a6fa78c4",
            'secret': "LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM",
            'sandbox': False,
            'enableRateLimit': True,
            'options': {'defaultType': 'swap'}
        })
        
        # Test BTC timeframes (exact same as working example)
        print("📊 Testing BTC timeframes (from working example):")
        for timeframe in ['1d', '4h', '1h']:
            try:
                data = exchange.fetch_ohlcv('BTC/USDT:USDT', timeframe, limit=10)
                print(f"   ✅ {timeframe}: {len(data)} candles")
                if data:
                    latest = data[-1]
                    print(f"      Latest: ${latest[4]:.2f}")
            except Exception as e:
                print(f"   ❌ {timeframe}: {str(e)}")
        
        # Test ADA timeframes
        print("\n📊 Testing ADA timeframes:")
        for timeframe in ['1d', '1h', '15m']:
            try:
                data = exchange.fetch_ohlcv('ADA/USDT:USDT', timeframe, limit=10)
                print(f"   ✅ {timeframe}: {len(data)} candles")
                if data:
                    latest = data[-1]
                    print(f"      Latest: ${latest[4]:.4f}")
            except Exception as e:
                print(f"   ❌ {timeframe}: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Working example test failed: {e}")
        return False

async def main():
    """Main function."""
    print("🚀 SYNC VS ASYNC PHEMEX TEST")
    print("=" * 60)
    
    # Test synchronous
    sync_result = test_sync_phemex()
    
    # Test asynchronous
    async_result = await test_async_phemex()
    
    # Test working example
    working_result = test_working_example()
    
    print(f"\n🎯 RESULTS:")
    print(f"   Synchronous: {'✅ WORKS' if sync_result else '❌ FAILS'}")
    print(f"   Asynchronous: {'✅ WORKS' if async_result else '❌ FAILS'}")
    print(f"   Working Example: {'✅ WORKS' if working_result else '❌ FAILS'}")
    
    if working_result:
        print(f"\n🎉 SOLUTION FOUND: Use synchronous CCXT!")
    else:
        print(f"\n❌ Still investigating...")

if __name__ == "__main__":
    asyncio.run(main())
