#!/usr/bin/env python3
"""
Nike Rocket Real-Time Dashboard

Shows real-time activity from CALEB's accounts:
- Current positions and P&L
- Recent trades
- Signal activity
- AXON integration status

READ-ONLY dashboard for monitoring
"""

import asyncio
import ccxt.pro as ccxt
import pandas as pd
from datetime import datetime, timedelta
import json
import os

class NikeRocketDashboard:
    """Real-time dashboard for Nike Rocket system."""
    
    def __init__(self):
        self.exchanges = {}
        self.running = True
        
        # CALEB's accounts (READ-ONLY) - WORKING KEYS FROM caleb_live_trading_system.py
        self.accounts = {
            'CALEB_MAIN': {
                'symbol': 'BTC',
                'algorithm': 'Massive Rocket',
                'api_key': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',  # WORKING KEY
                'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM'  # WORKING SECRET
            },
            'CALEB_SUB1': {
                'symbol': 'ADA',
                'algorithm': 'Massive Rocket',
                'api_key': 'a2cfaa90-469c-41d6-b954-7853887d1d7d',  # WORKING KEY
                'secret': 'wm79OAQzbMIb7EjniYf1pU-buGaQ4PJf5L5awFjXXcw0MzE2MDUwZC03MGQ4LTQ4MzEtOWE0NC00N2I1OTRhYmQzNzI'  # WORKING SECRET
            }
        }
        
        self.initialize_dashboard()
    
    def initialize_dashboard(self):
        """Initialize dashboard connections."""
        for account_name, config in self.accounts.items():
            try:
                exchange = ccxt.phemex({
                    'apiKey': config['api_key'],
                    'secret': config['secret'],
                    'sandbox': False,
                    'enableRateLimit': True,
                    'options': {'defaultType': 'swap'}
                })
                
                self.exchanges[account_name] = exchange
                
            except Exception as e:
                print(f"❌ Error initializing {account_name}: {e}")
    
    async def get_account_summary(self, account_name: str):
        """Get account summary data."""
        try:
            exchange = self.exchanges[account_name]
            config = self.accounts[account_name]
            
            # Get balance
            balance = await exchange.fetch_balance()
            total_balance = balance['USDT']['total']
            free_balance = balance['USDT']['free']
            used_balance = balance['USDT']['used']
            
            # Get positions
            positions = await exchange.fetch_positions()
            active_positions = [pos for pos in positions if pos['contracts'] != 0]
            
            # Get recent trades
            symbol = f"{config['symbol']}/USDT:USDT"
            recent_trades = await exchange.fetch_my_trades(symbol, limit=5)
            
            return {
                'account': account_name,
                'symbol': config['symbol'],
                'algorithm': config['algorithm'],
                'balance': {
                    'total': total_balance,
                    'free': free_balance,
                    'used': used_balance
                },
                'positions': active_positions,
                'recent_trades': recent_trades
            }
            
        except Exception as e:
            print(f"❌ Error getting summary for {account_name}: {e}")
            return None
    
    async def display_dashboard(self):
        """Display real-time dashboard."""
        while self.running:
            try:
                # Clear screen
                os.system('clear' if os.name == 'posix' else 'cls')
                
                print("🚀 NIKE ROCKET REAL-TIME DASHBOARD")
                print("=" * 80)
                print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 🔄 Auto-refresh every 10s")
                print("=" * 80)
                
                # Get data for all accounts
                for account_name in self.accounts.keys():
                    summary = await self.get_account_summary(account_name)
                    
                    if summary:
                        print(f"\n📊 {summary['account']} - {summary['algorithm']} {summary['symbol']}")
                        print("-" * 60)
                        
                        # Balance info
                        balance = summary['balance']
                        print(f"💰 Balance: ${balance['total']:.2f} USDT (Free: ${balance['free']:.2f}, Used: ${balance['used']:.2f})")
                        
                        # Position info
                        if summary['positions']:
                            for pos in summary['positions']:
                                pnl = pos['unrealizedPnl']
                                pct = pos['percentage']
                                side_emoji = "🟢" if pos['side'] == 'long' else "🔴"
                                
                                print(f"📈 Position: {side_emoji} {pos['side'].upper()} {pos['contracts']} @ ${pos['entryPrice']:.4f}")
                                print(f"💵 P&L: ${pnl:.2f} ({pct:+.2f}%) | Mark: ${pos['markPrice']:.4f}")
                        else:
                            print("💤 No active positions")
                        
                        # Recent trades
                        if summary['recent_trades']:
                            print("📋 Recent Trades:")
                            for trade in summary['recent_trades'][-3:]:  # Last 3 trades
                                trade_time = datetime.fromtimestamp(trade['timestamp'] / 1000)
                                side_emoji = "🟢" if trade['side'] == 'buy' else "🔴"
                                print(f"   {side_emoji} {trade['side'].upper()} {trade['amount']} @ ${trade['price']:.4f} ({trade_time.strftime('%H:%M:%S')})")
                        else:
                            print("📋 No recent trades")
                
                print("\n" + "=" * 80)
                print("🎯 System Status:")
                print("   🚀 Execution Bot: Running (trading on accounts)")
                print("   👁️  Signal Monitor: Running (sending to AXON)")
                print("   📡 AXON Integration: Active")
                print("   ⏹️  Press Ctrl+C to stop dashboard")
                print("=" * 80)
                
                await asyncio.sleep(10)  # Refresh every 10 seconds
                
            except KeyboardInterrupt:
                self.running = False
                break
            except Exception as e:
                print(f"❌ Dashboard error: {e}")
                await asyncio.sleep(30)
    
    async def show_system_status(self):
        """Show system status once."""
        print("🚀 NIKE ROCKET SYSTEM STATUS")
        print("=" * 50)
        
        for account_name in self.accounts.keys():
            summary = await self.get_account_summary(account_name)
            
            if summary:
                config = self.accounts[account_name]
                balance = summary['balance']['total']
                positions = len(summary['positions'])
                
                print(f"✅ {account_name}:")
                print(f"   Algorithm: {config['algorithm']}")
                print(f"   Symbol: {config['symbol']}")
                print(f"   Balance: ${balance:.2f} USDT")
                print(f"   Active Positions: {positions}")
            else:
                print(f"❌ {account_name}: Connection failed")
        
        print("=" * 50)

async def main():
    """Main dashboard function."""
    dashboard = NikeRocketDashboard()
    
    print("🚀 NIKE ROCKET DASHBOARD")
    print("=" * 40)
    print("Choose mode:")
    print("1. Real-time dashboard (auto-refresh)")
    print("2. One-time status check")
    print("=" * 40)
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            print("\n🔄 Starting real-time dashboard...")
            await dashboard.display_dashboard()
        elif choice == "2":
            print("\n📊 Getting system status...")
            await dashboard.show_system_status()
        else:
            print("❌ Invalid choice")
    
    except KeyboardInterrupt:
        print("\n⏹️ Dashboard stopped")
    except Exception as e:
        print(f"❌ Dashboard error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
