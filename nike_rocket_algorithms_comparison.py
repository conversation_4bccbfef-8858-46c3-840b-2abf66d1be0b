#!/usr/bin/env python3
"""
Nike's Rocket Algorithms Comprehensive Comparison

Tests CALEB's two new rocket algorithms:
- Nike's Baby Rocket (Lower Risk: 1% conservative, 2% aggressive)
- Nike's Massive Rocket (Higher Risk: 2% conservative, 4% aggressive)

Full analysis on BTC and ADA with CSV generation and detailed statistics.
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import path
sys.path.append('/Users/<USER>/TomorrowTech/python-backend/data_seed')

def load_data_seed_with_resampling(symbol: str):
    """Load data from data_seed and resample 1h to create 4h data."""
    
    symbol_map = {'BTC': 'XBTUSD', 'ADA': 'ADAUSDT'}
    
    if symbol not in symbol_map:
        return None
    
    data = {}
    
    print(f"📊 Loading data for {symbol}...")
    
    # Load daily data
    try:
        daily_filename = f"data_seed/{symbol_map[symbol]}_1440.csv"
        df_daily = pd.read_csv(daily_filename, header=None, 
                              names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'trades'])
        df_daily['datetime'] = pd.to_datetime(df_daily['timestamp'], unit='s')
        df_daily.set_index('timestamp', inplace=True)
        df_daily = df_daily.sort_index()
        data['daily'] = df_daily
        
    except Exception as e:
        print(f"   ❌ Error loading daily data: {e}")
        return None
    
    # Load 1h data and resample to 4h
    try:
        hourly_filename = f"data_seed/{symbol_map[symbol]}_60.csv"
        df_1h = pd.read_csv(hourly_filename, header=None, 
                           names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'trades'])
        df_1h['datetime'] = pd.to_datetime(df_1h['timestamp'], unit='s')
        
        # Create copy for resampling
        df_1h_for_resampling = df_1h.copy()
        df_1h_for_resampling.set_index('datetime', inplace=True)
        df_1h_for_resampling = df_1h_for_resampling.sort_index()
        
        # Keep original with timestamp index
        df_1h.set_index('timestamp', inplace=True)
        df_1h = df_1h.sort_index()
        data['lower'] = df_1h
        
        # Resample 1h to 4h
        df_4h = df_1h_for_resampling.resample('4h').agg({
            'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last',
            'volume': 'sum', 'trades': 'sum', 'timestamp': 'first'
        }).dropna()
        
        df_4h['datetime'] = df_4h.index
        df_4h['timestamp'] = df_4h.index.astype('int64') // 10**9
        df_4h.set_index('timestamp', inplace=True)
        data['medium'] = df_4h
        
    except Exception as e:
        print(f"   ❌ Error loading/resampling 1h data: {e}")
        return None
    
    duration_years = (data['daily']['datetime'].iloc[-1] - data['daily']['datetime'].iloc[0]).days / 365.25
    print(f"   ✅ Loaded {duration_years:.1f} years of data")
    return data

def run_nike_rocket_algorithm(algorithm_name: str, symbol: str, initial_equity: float = 10000):
    """Run Nike's rocket algorithms and extract comprehensive statistics."""
    
    print(f"🚀 Running {algorithm_name} on {symbol}...")
    
    # Load data
    data = load_data_seed_with_resampling(symbol)
    if not data:
        return None
    
    # Import the appropriate algorithm
    try:
        if algorithm_name == "Nikes_Baby_Rocket":
            import importlib.util
            spec = importlib.util.spec_from_file_location("NikesBabyRocket", "data_seed/Nike's Baby Rocket Algo.py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            model = module.NikesBabyRocket()
            print(f"   ✅ Using Baby Rocket (1% conservative, 2% aggressive risk)")
        elif algorithm_name == "Nikes_Massive_Rocket":
            import importlib.util
            spec = importlib.util.spec_from_file_location("NikesMassiveRocket", "data_seed/Nike's Massive Rocket Algo.py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            model = module.NikesMassiveRocket()
            print(f"   ✅ Using Massive Rocket (2% conservative, 4% aggressive risk)")
        else:
            print(f"   ❌ Unknown algorithm: {algorithm_name}")
            return None
            
    except Exception as e:
        print(f"   ❌ Error importing {algorithm_name}: {e}")
        return None
    
    # Calculate indicators
    for tf_name in ['daily', 'medium', 'lower']:
        data[tf_name] = model.calculate_indicators(data[tf_name])
    
    # Combine timeframes
    combined_df = model.combine_timeframes(data['daily'], data['medium'], data['lower'])
    
    # Generate signals and get detailed results
    try:
        result_df = model.generate_signals_with_compounding_and_reversal(combined_df, initial_equity)
        
        # Extract trade outcomes by analyzing equity changes
        trades_analysis = []
        current_trade = None
        
        for idx, row in result_df.iterrows():
            # Check for trade entry
            if row['signal'] != 'NEUTRAL':
                current_trade = {
                    'entry_idx': idx,
                    'entry_equity': row['equity'],
                    'signal': row['signal'],
                    'mode_used': row.get('mode_used', 'aggressive'),
                    'confidence': row.get('confidence', 0.5),
                    'entry_datetime': row['datetime']
                }
            
            # Check for trade exit
            if pd.notna(row.get('exit_signal')) and current_trade:
                exit_equity = row['equity']
                entry_equity = current_trade['entry_equity']
                
                # Determine if trade was winning based on equity increase
                is_winning = exit_equity > entry_equity
                pnl_percent = (exit_equity - entry_equity) / entry_equity * 100
                
                trades_analysis.append({
                    'entry_datetime': current_trade['entry_datetime'],
                    'exit_datetime': row['datetime'],
                    'signal': current_trade['signal'],
                    'mode_used': current_trade['mode_used'],
                    'confidence': current_trade['confidence'],
                    'entry_equity': entry_equity,
                    'exit_equity': exit_equity,
                    'pnl_percent': pnl_percent,
                    'is_winning': is_winning,
                    'exit_reason': row['exit_signal']
                })
                
                current_trade = None
        
        if len(trades_analysis) == 0:
            print(f"   ❌ No completed trades found for {algorithm_name} {symbol}")
            return None
        
        trades_df = pd.DataFrame(trades_analysis)
        
        # Calculate comprehensive statistics
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['is_winning'] == True])
        overall_win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # Mode-specific statistics
        mode_stats = {}
        for mode in trades_df['mode_used'].unique():
            mode_trades = trades_df[trades_df['mode_used'] == mode]
            mode_winning = len(mode_trades[mode_trades['is_winning'] == True])
            mode_total = len(mode_trades)
            mode_win_rate = (mode_winning / mode_total * 100) if mode_total > 0 else 0
            
            mode_stats[mode] = {
                'total_trades': mode_total,
                'winning_trades': mode_winning,
                'win_rate': mode_win_rate,
                'percentage_of_total': (mode_total / total_trades * 100) if total_trades > 0 else 0
            }
        
        # Calculate performance metrics
        final_equity = result_df['equity'].iloc[-1]
        total_return = (final_equity - initial_equity) / initial_equity * 100
        
        # Calculate drawdown
        equity_curve = result_df['equity'].values
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak * 100
        max_drawdown = np.min(drawdown)
        
        # Extract equity curve for CSV
        equity_curve_data = result_df[['datetime', 'equity']].copy()
        equity_curve_data['drawdown'] = drawdown
        
        print(f"   ✅ {algorithm_name}: {total_return:+.2f}% return, {overall_win_rate:.1f}% win rate, {max_drawdown:.2f}% max DD, {total_trades} trades")
        
        return {
            'algorithm': algorithm_name,
            'symbol': symbol,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'overall_win_rate': overall_win_rate,
            'mode_stats': mode_stats,
            'final_equity': final_equity,
            'trades_df': trades_df,
            'equity_curve': equity_curve_data
        }
        
    except Exception as e:
        print(f"   ❌ Error running {algorithm_name} {symbol}: {e}")
        return None

def generate_nike_rocket_comparison():
    """Generate comprehensive comparison of Nike's rocket algorithms."""
    
    print("🚀 NIKE'S ROCKET ALGORITHMS COMPREHENSIVE COMPARISON")
    print("=" * 70)
    print("Testing CALEB's latest rocket algorithms:")
    print("🚀 Baby Rocket: Lower risk (1% conservative, 2% aggressive)")
    print("🚀 Massive Rocket: Higher risk (2% conservative, 4% aggressive)")
    print()
    
    algorithms = ['Nikes_Baby_Rocket', 'Nikes_Massive_Rocket']
    symbols = ['BTC', 'ADA']
    
    all_results = []
    
    # Test all combinations
    for symbol in symbols:
        print(f"\n📊 Processing {symbol}...")
        
        for algorithm in algorithms:
            result = run_nike_rocket_algorithm(algorithm, symbol)
            if result:
                all_results.append(result)
    
    if not all_results:
        print("❌ No results generated!")
        return
    
    # Generate timestamp for files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create comprehensive comparison table
    print(f"\n🚀 NIKE'S ROCKET ALGORITHMS COMPARISON:")
    print("=" * 100)
    print(f"{'Algorithm':<20} {'Symbol':<6} {'Return %':<15} {'Win Rate %':<10} {'Max DD %':<10} {'Trades':<8} {'Agg %':<8} {'Con %':<8}")
    print("=" * 100)
    
    for result in all_results:
        agg_stats = result['mode_stats'].get('aggressive', {'percentage_of_total': 0, 'win_rate': 0})
        con_stats = result['mode_stats'].get('conservative', {'percentage_of_total': 0, 'win_rate': 0})
        
        algo_display = result['algorithm'].replace('Nikes_', '').replace('_', ' ')
        return_str = f"{result['total_return']:+,.0f}" if result['total_return'] > 1000 else f"{result['total_return']:+,.2f}"
        
        print(f"{algo_display:<20} {result['symbol']:<6} {return_str:<15} {result['overall_win_rate']:<9.1f} {result['max_drawdown']:<9.2f} {result['total_trades']:<8} {agg_stats['percentage_of_total']:<7.1f} {con_stats['percentage_of_total']:<7.1f}")
    
    print("=" * 100)
    
    # Save equity curves as CSV files
    print(f"\n💾 SAVING EQUITY CURVES:")
    print("=" * 50)
    
    for result in all_results:
        # Save equity curve
        equity_filename = f"NIKE_ROCKET_equity_curve_{result['symbol']}_{result['algorithm']}_{timestamp}.csv"
        result['equity_curve'].to_csv(equity_filename, index=False)
        print(f"✅ Saved: {equity_filename}")
        
        # Save detailed trades
        trades_filename = f"NIKE_ROCKET_trades_{result['symbol']}_{result['algorithm']}_{timestamp}.csv"
        result['trades_df'].to_csv(trades_filename, index=False)
        print(f"✅ Saved: {trades_filename}")
    
    # Create comprehensive summary CSV
    summary_data = []
    for result in all_results:
        for mode, stats in result['mode_stats'].items():
            summary_data.append({
                'algorithm': result['algorithm'],
                'symbol': result['symbol'],
                'mode': mode,
                'total_return_percent': result['total_return'],
                'max_drawdown_percent': result['max_drawdown'],
                'total_trades_all_modes': result['total_trades'],
                'overall_win_rate_percent': result['overall_win_rate'],
                'mode_trades': stats['total_trades'],
                'mode_winning_trades': stats['winning_trades'],
                'mode_win_rate_percent': stats['win_rate'],
                'mode_percentage_of_total': stats['percentage_of_total'],
                'final_equity': result['final_equity']
            })
    
    summary_df = pd.DataFrame(summary_data)
    summary_filename = f"NIKE_ROCKET_ALGORITHMS_SUMMARY_{timestamp}.csv"
    summary_df.to_csv(summary_filename, index=False)
    
    print(f"✅ Saved comprehensive summary: {summary_filename}")
    
    # Generate detailed analysis
    print(f"\n🎯 DETAILED ANALYSIS:")
    print("=" * 60)
    
    # Compare by symbol
    for symbol in symbols:
        symbol_results = [r for r in all_results if r['symbol'] == symbol]
        if len(symbol_results) >= 2:
            baby = next((r for r in symbol_results if 'Baby' in r['algorithm']), None)
            massive = next((r for r in symbol_results if 'Massive' in r['algorithm']), None)
            
            if baby and massive:
                print(f"\n{symbol} COMPARISON:")
                print(f"🚀 Baby Rocket:    {baby['total_return']:+,.2f}% return, {baby['overall_win_rate']:.1f}% win rate, {baby['max_drawdown']:.2f}% DD")
                print(f"🚀 Massive Rocket: {massive['total_return']:+,.2f}% return, {massive['overall_win_rate']:.1f}% win rate, {massive['max_drawdown']:.2f}% DD")
                
                if massive['total_return'] > baby['total_return']:
                    multiplier = massive['total_return'] / baby['total_return'] if baby['total_return'] > 0 else float('inf')
                    print(f"   🏆 WINNER: Massive Rocket ({multiplier:.1f}x better returns)")
                else:
                    multiplier = baby['total_return'] / massive['total_return'] if massive['total_return'] > 0 else float('inf')
                    print(f"   🏆 WINNER: Baby Rocket ({multiplier:.1f}x better returns)")
    
    # Final recommendations
    print(f"\n🏆 FINAL RECOMMENDATIONS:")
    print("=" * 50)
    
    # Find best performers
    ada_results = [r for r in all_results if r['symbol'] == 'ADA']
    if ada_results:
        best_ada = max(ada_results, key=lambda x: x['total_return'])
        best_win_rate = max(ada_results, key=lambda x: x['overall_win_rate'])
        best_drawdown = max(ada_results, key=lambda x: x['max_drawdown'])  # Higher is better (less negative)
        
        print(f"🚀 HIGHEST ADA RETURNS: {best_ada['algorithm'].replace('Nikes_', '').replace('_', ' ')} ({best_ada['total_return']:+,.2f}%)")
        print(f"🎯 HIGHEST WIN RATE: {best_win_rate['algorithm'].replace('Nikes_', '').replace('_', ' ')} ({best_win_rate['overall_win_rate']:.1f}%)")
        print(f"🛡️ BEST RISK CONTROL: {best_drawdown['algorithm'].replace('Nikes_', '').replace('_', ' ')} ({best_drawdown['max_drawdown']:.2f}% DD)")
    
    print(f"\n✅ NIKE'S ROCKET ALGORITHMS COMPARISON COMPLETE!")
    print(f"📊 All equity curves and trade data saved with timestamp: {timestamp}")
    
    return all_results, timestamp

if __name__ == "__main__":
    generate_nike_rocket_comparison()
