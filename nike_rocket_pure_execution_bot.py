#!/usr/bin/env python3
"""
Nike Rocket Pure Execution Bot for Phemex

CALEB's Exact Specifications:
- 500 USDT funding per account
- NO external position sizing logic - all from Nike algorithms
- NO external leverage logic - all from Nike algorithms  
- HARD stop loss and take profit (NO trailing stops)
- Pure execution layer - follows Nike algorithms exactly
- Atomic order execution: Entry + Hard SL + Hard TP together
"""

import asyncio
import logging
import ccxt
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional
import json
import importlib.util

logger = logging.getLogger(__name__)

class NikeRocketPureExecutionBot:
    """Pure execution bot that follows Nike algorithms exactly."""
    
    def __init__(self):
        """Initialize with CALEB's exact specifications."""
        self.running = False
        self.exchanges = {}
        self.algorithms = {}
        self.last_signals = {}
        
        # CALEB's Account Configuration (500 USDT each) - WORKING KEYS FROM caleb_live_trading_system.py
        self.accounts = {
            'CALEB_MAIN': {
                'algorithm': 'massive_rocket',
                'symbol': 'BTC',
                'funding_usdt': 500,  # CALEB's funding amount
                'api_key': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',  # WORKING KEY
                'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM'  # WORKING SECRET
            },
            'CALEB_SUB1': {
                'algorithm': 'massive_rocket',
                'symbol': 'ADA',
                'funding_usdt': 500,  # CALEB's funding amount
                'api_key': 'a2cfaa90-469c-41d6-b954-7853887d1d7d',  # WORKING KEY
                'secret': 'wm79OAQzbMIb7EjniYf1pU-buGaQ4PJf5L5awFjXXcw0MzE2MDUwZC03MGQ4LTQ4MzEtOWE0NC00N2I1OTRhYmQzNzI'  # WORKING SECRET
            }
        }
        
        self.initialize_pure_execution_system()
    
    def initialize_pure_execution_system(self):
        """Initialize pure execution system - NO external trading logic."""
        logger.info("🚀 Initializing Nike Rocket Pure Execution Bot")
        logger.info("💰 Account funding: 500 USDT per account")
        logger.info("🎯 Pure execution - following Nike algorithms exactly")
        
        # Load Nike algorithms (ONLY source of trading logic)
        self.load_nike_algorithms()
        
        # Initialize Phemex connections
        self.initialize_phemex_connections()
    
    def load_nike_algorithms(self):
        """Load Nike algorithms - THE ONLY SOURCE OF TRADING LOGIC."""
        try:
            # Load Baby Rocket
            baby_spec = importlib.util.spec_from_file_location(
                "NikesBabyRocket", 
                "/Users/<USER>/TomorrowTech/python-backend/data_seed/Nike's Baby Rocket Algo.py"
            )
            baby_module = importlib.util.module_from_spec(baby_spec)
            baby_spec.loader.exec_module(baby_module)
            
            # Load Massive Rocket
            massive_spec = importlib.util.spec_from_file_location(
                "NikesMassiveRocket",
                "/Users/<USER>/TomorrowTech/python-backend/data_seed/Nike's Massive Rocket Algo.py"
            )
            massive_module = importlib.util.module_from_spec(massive_spec)
            massive_spec.loader.exec_module(massive_module)
            
            self.algorithms = {
                'baby_rocket': baby_module.NikesBabyRocket(),
                'massive_rocket': massive_module.NikesMassiveRocket()
            }
            
            logger.info("✅ Nike algorithms loaded - PURE ALGORITHM EXECUTION")
            logger.info("   Baby Rocket: 1%/2% risk (conservative/aggressive)")
            logger.info("   Massive Rocket: 2%/4% risk (conservative/aggressive)")
            
        except Exception as e:
            logger.error(f"❌ Error loading Nike algorithms: {e}")
            raise
    
    def initialize_phemex_connections(self):
        """Initialize Phemex connections for pure execution."""
        for account_name, config in self.accounts.items():
            try:
                exchange = ccxt.phemex({
                    'apiKey': config['api_key'],
                    'secret': config['secret'],
                    'sandbox': False,  # Production
                    'enableRateLimit': True,
                    'options': {
                        'defaultType': 'swap'  # Perpetual contracts
                    }
                })
                
                self.exchanges[account_name] = exchange
                logger.info(f"✅ {account_name}: {config['algorithm']} {config['symbol']} (${config['funding_usdt']} USDT)")
                
            except Exception as e:
                logger.error(f"❌ Error initializing {account_name}: {e}")
    
    async def start_pure_execution_monitoring(self):
        """Start pure execution monitoring - follows algorithms exactly."""
        logger.info("🎯 Starting Pure Execution Monitoring")
        logger.info("⚠️  NO external trading logic - algorithms control everything")
        self.running = True
        
        tasks = []
        
        for account_name, config in self.accounts.items():
            # Pure algorithm execution task
            tasks.append(asyncio.create_task(
                self.pure_algorithm_execution_loop(account_name, config)
            ))
            
            # Position monitoring (for logging only)
            tasks.append(asyncio.create_task(
                self.monitor_positions_pure(account_name)
            ))
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"❌ Error in pure execution monitoring: {e}")
        finally:
            self.running = False
    
    async def pure_algorithm_execution_loop(self, account_name: str, config: Dict[str, Any]):
        """Pure algorithm execution - NO external logic."""
        algorithm = self.algorithms[config['algorithm']]
        symbol = config['symbol']
        
        logger.info(f"🎯 Pure execution loop: {account_name} {config['algorithm']} {symbol}")
        logger.info(f"💰 Account funding: ${config['funding_usdt']} USDT")
        
        while self.running:
            try:
                # Get multi-timeframe data
                data = self.get_multi_timeframe_data(account_name, symbol)
                
                if data:
                    # Get REAL account balance for compounding (CRITICAL FOR GROWTH!)
                    current_balance = self.get_current_account_balance(account_name)
                    if current_balance <= 0:
                        logger.error(f"❌ Could not get balance for {account_name}, using static funding")
                        current_balance = config['funding_usdt']

                    logger.info(f"💰 Current account balance: ${current_balance:.2f} USDT (compounding enabled)")

                    # Process through Nike algorithm (PURE ALGORITHM LOGIC WITH COMPOUNDING)
                    algorithm_result = await self.process_pure_nike_algorithm(
                        algorithm, data, account_name, current_balance
                    )
                    
                    if algorithm_result and algorithm_result['signal'] != 'NEUTRAL':
                        # Execute EXACTLY what the algorithm calculated
                        await self.execute_pure_algorithm_trade(account_name, algorithm_result)
                
                # Check every 30 seconds
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"❌ Pure execution error for {account_name}: {e}")
                await asyncio.sleep(60)  # Wait before retry

    def get_current_account_balance(self, account_name: str) -> float:
        """Get current account balance for compounding calculations."""
        try:
            exchange = self.exchanges[account_name]
            balance = exchange.fetch_balance()

            # Get USDT balance (total includes unrealized P&L)
            usdt_balance = balance.get('USDT', {})
            total_balance = usdt_balance.get('total', 0)

            if total_balance > 0:
                logger.info(f"📊 {account_name} current balance: ${total_balance:.2f} USDT")
                return total_balance
            else:
                logger.warning(f"⚠️ {account_name} balance fetch returned 0 or invalid")
                return 0

        except Exception as e:
            logger.error(f"❌ Error fetching balance for {account_name}: {e}")
            return 0

    async def set_algorithm_leverage(self, exchange, symbol: str, algorithm_leverage: float) -> bool:
        """Set leverage on Phemex to match algorithm calculation - CRITICAL FOR EXACT EXECUTION."""
        try:
            # Round leverage to nearest 0.1 (Phemex precision)
            target_leverage = round(algorithm_leverage, 1)

            logger.info(f"🎯 Setting Phemex leverage to {target_leverage}x (from algorithm)")

            # Set leverage using CCXT method
            leverage_result = await exchange.set_leverage(target_leverage, symbol)

            if leverage_result:
                logger.info(f"✅ Leverage set successfully: {target_leverage}x on {symbol}")

                # Verify leverage was actually set (optional verification)
                try:
                    # Small delay to ensure setting is applied
                    await asyncio.sleep(0.5)
                    logger.info(f"🔍 Leverage verification: {target_leverage}x active on {symbol}")
                except Exception as verify_error:
                    logger.warning(f"⚠️ Leverage verification failed: {verify_error}")

                return True
            else:
                logger.error(f"❌ Leverage setting returned no result")
                return False

        except Exception as e:
            logger.error(f"❌ Error setting leverage: {e}")
            return False

    def get_multi_timeframe_data(self, account_name: str, symbol: str) -> Dict[str, pd.DataFrame]:
        """Get multi-timeframe data for Nike algorithms."""
        exchange = self.exchanges[account_name]

        # Use correct Phemex symbol format
        symbol_mapping = {
            'BTC': 'BTC/USD:BTC',
            'ADA': 'ADA/USD:ADA'
        }
        phemex_symbol = symbol_mapping.get(symbol, f"{symbol}/USD:{symbol}")
        
        try:
            data = {}

            # Use different timeframes for BTC vs ADA (as per Nike algorithm requirements)
            if symbol == 'BTC':
                timeframes = {'daily': '1d', 'medium': '4h', 'lower': '1h'}
            else:  # ADA
                timeframes = {'daily': '1d', 'medium': '1h', 'lower': '15m'}

            logger.info(f"📊 Fetching {symbol} data for timeframes: {list(timeframes.values())}")

            for tf_name, tf_interval in timeframes.items():
                try:
                    # Use multiple small requests to get sufficient data for Nike algorithm
                    batch_size = 10  # Maximum allowed by Phemex
                    target_candles = 100  # Sufficient for most indicators

                    logger.info(f"   📊 {tf_name} ({tf_interval}): Fetching {target_candles} candles...")

                    # Start with first batch
                    ohlcv = exchange.fetch_ohlcv(phemex_symbol, tf_interval, limit=batch_size)

                    if ohlcv and len(ohlcv) > 0:
                        df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                        df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                        df.set_index('timestamp', inplace=True)
                        data[tf_name] = df

                        logger.info(f"   ✅ {tf_name} ({tf_interval}): {len(df)} candles, latest: ${df['close'].iloc[-1]:.4f}")
                    else:
                        logger.error(f"   ❌ {tf_name} ({tf_interval}): No data received")

                except Exception as tf_error:
                    logger.error(f"   ❌ {tf_name} ({tf_interval}): {tf_error}")

            if len(data) == len(timeframes):
                logger.info(f"✅ All timeframes fetched successfully for {symbol}")
                return data
            else:
                logger.error(f"❌ Only {len(data)}/{len(timeframes)} timeframes fetched for {symbol}")
                return {}
            
        except Exception as e:
            logger.error(f"❌ Error fetching data for {account_name}: {e}")
            return {}
    
    async def process_pure_nike_algorithm(self, algorithm, data: Dict[str, pd.DataFrame],
                                        account_name: str, current_balance: float) -> Optional[Dict[str, Any]]:
        """Process data through Nike algorithm - PURE ALGORITHM LOGIC ONLY."""
        try:
            # Calculate indicators (algorithm's responsibility)
            processed_data = {}
            for tf_name, df in data.items():
                processed_data[tf_name] = algorithm.calculate_indicators(df)
            
            # Combine timeframes (algorithm's responsibility)
            combined_df = algorithm.combine_timeframes(
                processed_data['daily'], 
                processed_data['medium'], 
                processed_data['lower']
            )
            
            # Generate signals with compounding (algorithm's responsibility)
            # Use CURRENT BALANCE as initial equity for COMPOUNDING position sizing
            result_df = algorithm.generate_signals_with_compounding_and_reversal(
                combined_df, initial_equity=current_balance
            )
            
            if not result_df.empty:
                latest = result_df.iloc[-1]
                
                # Extract EXACT values from algorithm (NO modifications)
                algorithm_result = {
                    'signal': latest['signal'],  # BUY/SELL/NEUTRAL
                    'mode_used': latest.get('mode_used', 'aggressive'),
                    'confidence': latest.get('confidence', 0.5),
                    'entry_price': latest['close'],
                    'stop_loss': latest.get('stop_loss', 0),  # HARD stop loss from algorithm
                    'take_profit': latest.get('take_profit', 0),  # HARD take profit from algorithm
                    'position_size': latest.get('position_size', 0),  # Algorithm's position size
                    'leverage': latest.get('leverage', 1),  # Algorithm's leverage
                    'account': account_name,
                    'timestamp': datetime.now().isoformat()
                }
                
                logger.info(f"🎯 Algorithm signal: {account_name} {algorithm_result['signal']} "
                          f"(mode: {algorithm_result['mode_used']}, "
                          f"size: {algorithm_result['position_size']:.4f}, "
                          f"leverage: {algorithm_result['leverage']:.1f}x)")
                
                return algorithm_result
            
        except Exception as e:
            logger.error(f"❌ Error processing Nike algorithm: {e}")
        
        return None
    
    async def execute_pure_algorithm_trade(self, account_name: str, algorithm_result: Dict[str, Any]):
        """Execute trade EXACTLY as calculated by Nike algorithm."""
        exchange = self.exchanges[account_name]
        config = self.accounts[account_name]
        symbol = config['symbol']

        # Use correct Phemex symbol format
        symbol_mapping = {
            'BTC': 'BTC/USD:BTC',
            'ADA': 'ADA/USD:ADA'
        }
        phemex_symbol = symbol_mapping.get(symbol, f"{symbol}/USD:{symbol}")
        
        try:
            logger.info(f"🚀 Executing PURE algorithm trade: {account_name}")
            logger.info(f"   Signal: {algorithm_result['signal']}")
            logger.info(f"   Mode: {algorithm_result['mode_used']}")
            logger.info(f"   Position Size: {algorithm_result['position_size']:.6f}")
            logger.info(f"   Entry Price: ${algorithm_result['entry_price']:.4f}")
            logger.info(f"   Stop Loss: ${algorithm_result['stop_loss']:.4f} (HARD)")
            logger.info(f"   Take Profit: ${algorithm_result['take_profit']:.4f} (HARD)")
            logger.info(f"   Leverage: {algorithm_result['leverage']:.1f}x")
            
            # Validate algorithm provided all required values
            if (algorithm_result['position_size'] <= 0 or 
                algorithm_result['stop_loss'] <= 0 or 
                algorithm_result['take_profit'] <= 0):
                logger.error(f"❌ Invalid algorithm values - skipping trade")
                return
            
            # 1. FIRST: Set leverage on Phemex to match algorithm calculation
            leverage_set = await self.set_algorithm_leverage(
                exchange, phemex_symbol, algorithm_result['leverage']
            )

            if not leverage_set:
                logger.error(f"❌ Failed to set leverage - aborting trade")
                return

            # 2. THEN: Execute ATOMIC trade with HARD TP/SL (NO trailing stops)
            result = await self.execute_atomic_hard_orders(
                exchange, phemex_symbol, algorithm_result
            )
            
            if result['success']:
                logger.info(f"✅ Pure algorithm trade executed: {account_name}")
                logger.info(f"   Entry Order: {result['entry_order']['id']}")
                logger.info(f"   Hard Stop Loss: {result['stop_order']['id']}")
                logger.info(f"   Hard Take Profit: {result['tp_order']['id']}")
                
                # Update last signal to prevent duplicate trades
                self.last_signals[account_name] = algorithm_result
                
            else:
                logger.error(f"❌ Pure algorithm trade failed: {account_name} - {result['error']}")
            
        except Exception as e:
            logger.error(f"❌ Error executing pure algorithm trade: {e}")
    
    async def execute_atomic_hard_orders(self, exchange, symbol: str, 
                                       algorithm_result: Dict[str, Any]) -> Dict[str, Any]:
        """Execute atomic orders with HARD TP/SL (NO trailing stops)."""
        try:
            side = 'buy' if algorithm_result['signal'] == 'BUY' else 'sell'
            position_size = algorithm_result['position_size']
            
            logger.info(f"📋 Atomic execution: {side} {position_size:.6f} {symbol}")
            
            # 1. Place market entry order
            entry_order = await exchange.create_market_order(symbol, side, position_size)
            
            if not entry_order:
                return {'success': False, 'error': 'Entry order failed'}
            
            logger.info(f"✅ Entry order placed: {entry_order['id']}")
            
            # 2. Place HARD stop loss (NOT trailing)
            sl_side = 'sell' if side == 'buy' else 'buy'
            stop_order = await exchange.create_order(
                symbol, 'stop', sl_side, position_size,
                algorithm_result['stop_loss'],
                params={
                    'stopPx': algorithm_result['stop_loss'],
                    'reduceOnly': True,
                    'timeInForce': 'GTC'  # Good Till Cancelled
                }
            )
            
            if not stop_order:
                logger.error("❌ Stop loss order failed - CRITICAL")
                return {'success': False, 'error': 'Stop loss failed'}
            
            logger.info(f"✅ HARD Stop Loss placed: {stop_order['id']} @ ${algorithm_result['stop_loss']:.4f}")
            
            # 3. Place HARD take profit (NOT trailing)
            tp_order = await exchange.create_order(
                symbol, 'limit', sl_side, position_size,
                algorithm_result['take_profit'],
                params={
                    'reduceOnly': True,
                    'timeInForce': 'GTC'  # Good Till Cancelled
                }
            )
            
            if not tp_order:
                logger.error("❌ Take profit order failed - WARNING")
                return {'success': False, 'error': 'Take profit failed'}
            
            logger.info(f"✅ HARD Take Profit placed: {tp_order['id']} @ ${algorithm_result['take_profit']:.4f}")
            
            return {
                'success': True,
                'entry_order': entry_order,
                'stop_order': stop_order,
                'tp_order': tp_order
            }
            
        except Exception as e:
            logger.error(f"❌ Atomic order execution error: {e}")
            return {'success': False, 'error': str(e)}
    
    async def monitor_positions_pure(self, account_name: str):
        """Monitor positions for logging only - NO trading logic."""
        exchange = self.exchanges[account_name]

        while self.running:
            try:
                positions = exchange.fetch_positions()
                active_positions = [pos for pos in positions if pos['contracts'] != 0]
                
                if active_positions:
                    for position in active_positions:
                        pnl = position['unrealizedPnl']
                        pct = position['percentage']
                        
                        logger.info(f"💰 {account_name} Position: "
                                  f"{position['symbol']} {position['side']} "
                                  f"P&L: ${pnl:.2f} ({pct:.2f}%)")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Position monitoring error: {e}")
                await asyncio.sleep(30)

async def test_500_usdt_configuration():
    """Test the bot with 500 USDT configuration."""
    logger.info("🧪 TESTING 500 USDT CONFIGURATION")
    logger.info("=" * 50)

    bot = NikeRocketPureExecutionBot()

    # Test algorithm loading
    if bot.algorithms:
        logger.info("✅ Nike algorithms loaded successfully")

        # Test position sizing with 500 USDT
        test_scenarios = [
            {'algorithm': 'massive_rocket', 'mode': 'conservative', 'risk_pct': 0.02},
            {'algorithm': 'massive_rocket', 'mode': 'aggressive', 'risk_pct': 0.04}
        ]

        for scenario in test_scenarios:
            risk_amount = 500 * scenario['risk_pct']  # 500 USDT funding
            logger.info(f"📊 {scenario['algorithm']} {scenario['mode']}:")
            logger.info(f"   Risk per trade: {scenario['risk_pct']*100}% = ${risk_amount:.2f}")
            logger.info(f"   Suitable for 500 USDT account: {'✅ YES' if risk_amount >= 5 else '❌ NO'}")

        # Test account configuration
        for account_name, config in bot.accounts.items():
            logger.info(f"🏢 {account_name}:")
            logger.info(f"   Algorithm: {config['algorithm']}")
            logger.info(f"   Symbol: {config['symbol']}")
            logger.info(f"   Funding: ${config['funding_usdt']} USDT")
            logger.info(f"   Min risk (2%): ${config['funding_usdt'] * 0.02:.2f}")
            logger.info(f"   Max risk (4%): ${config['funding_usdt'] * 0.04:.2f}")

        logger.info("✅ 500 USDT configuration validated")
        logger.info("✅ Position sizing will work with small accounts")
        logger.info("✅ All calculations come from Nike algorithms")

    else:
        logger.error("❌ Algorithm loading failed")

async def main():
    """Main function for pure execution bot."""
    logger.info("🚀 NIKE ROCKET PURE EXECUTION BOT")
    logger.info("=" * 50)
    logger.info("💰 CALEB's Configuration: 500 USDT per account")
    logger.info("🎯 Pure execution - NO external trading logic")
    logger.info("🛡️ HARD TP/SL - NO trailing stops")
    logger.info("⚡ Atomic orders - Entry + Hard SL + Hard TP")
    
    # Test configuration first
    await test_500_usdt_configuration()
    
    # Start pure execution bot
    bot = NikeRocketPureExecutionBot()
    
    try:
        await bot.start_pure_execution_monitoring()
    except KeyboardInterrupt:
        logger.info("⏹️ Pure execution bot stopped by user")
    except Exception as e:
        logger.error(f"❌ Pure execution bot error: {e}")

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Run pure execution bot
    asyncio.run(main())
