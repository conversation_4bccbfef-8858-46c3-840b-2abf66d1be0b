# services/caleb_phemex_service.py - Modified for CALEB account (no leverage)

import logging
import os
import ccxt
from dotenv import load_dotenv
from services.phemex_service import PhemexService as BasePhemexService

logger = logging.getLogger(__name__)

class CalebPhemexService(BasePhemexService):
    """
    Modified PhemexService for CALEB account with 5x leverage.
    """

    def __init__(self):
        # Load CALEB-specific environment variables
        load_dotenv('.env.caleb')
        logger.info("Loaded CALEB environment variables from .env.caleb")

        super().__init__()

        # Override default leverage to 5x (as per config)
        self.default_leverage = 5

        logger.info("Initialized CALEB Phemex service with 5x LEVERAGE")

    async def place_order(self, symbol, side, order_type, quantity, price=None,
                         time_in_force="GoodTillCancel", reduce_only=False,
                         close_on_trigger=False):
        """
        Override place_order to set leverage to 1x before placing the order
        """
        try:
            # Map our internal symbol to CCXT format if needed
            if symbol in self.symbol_mapping:
                ccxt_symbol = self.symbol_mapping[symbol]
            else:
                ccxt_symbol = symbol

            # Set leverage to 5x (as per config)
            await self.set_leverage(symbol, 5)

            # Call the parent method to place the order
            return await super().place_order(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                time_in_force=time_in_force,
                reduce_only=reduce_only,
                close_on_trigger=close_on_trigger
            )
        except Exception as e:
            logger.error(f"Error in CalebPhemexService.place_order: {str(e)}", exc_info=True)
            return {"error": True, "message": f"Exception: {str(e)}"}
