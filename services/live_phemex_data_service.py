#!/usr/bin/env python3
"""
Live Phemex Data Service
Fetches real-time market data directly from Phemex for TITAN algorithms.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import ccxt

logger = logging.getLogger(__name__)

class LivePhemexDataService:
    """Service for fetching live market data from Phemex."""
    
    def __init__(self):
        """Initialize the live data service."""
        self.exchange = ccxt.phemex({
            'enableRateLimit': True,
            'options': {
                'defaultType': 'swap',  # Perpetual contracts
            }
        })
        
    async def get_live_ohlcv(self, symbol: str, timeframe: str, limit: int = 1000) -> pd.DataFrame:
        """
        Get live OHLCV data from Phemex.
        
        Args:
            symbol: Symbol (BTC, ADA)
            timeframe: Timeframe (1m, 5m, 15m, 1h, 4h, 1d)
            limit: Number of candles to fetch
            
        Returns:
            DataFrame with live OHLCV data
        """
        try:
            # Convert symbol to Phemex format
            phemex_symbol = f"{symbol}/USDT:USDT"
            
            logger.info(f"Fetching live {symbol} {timeframe} data ({limit} candles)")
            
            # Fetch OHLCV data
            ohlcv = self.exchange.fetch_ohlcv(
                symbol=phemex_symbol,
                timeframe=timeframe,
                limit=limit
            )
            
            if not ohlcv:
                logger.error(f"No OHLCV data received for {symbol}")
                return pd.DataFrame()
            
            # Convert to DataFrame
            df = pd.DataFrame(ohlcv, columns=['time', 'open', 'high', 'low', 'close', 'volume'])
            
            # Convert timestamp to datetime
            df['datetime'] = pd.to_datetime(df['time'], unit='ms')
            
            # Convert price columns to float
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            logger.info(f"✅ Fetched {len(df)} live {symbol} {timeframe} candles")
            logger.info(f"   Latest price: ${df['close'].iloc[-1]:.4f}")
            logger.info(f"   Latest time: {df['datetime'].iloc[-1]}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error fetching live {symbol} data: {str(e)}")
            return pd.DataFrame()
    
    async def get_multi_timeframe_data(self, symbol: str, timeframes: list, limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """Get live data for multiple timeframes."""
        try:
            data = {}
            
            for timeframe in timeframes:
                df = await self.get_live_ohlcv(symbol, timeframe, limit)
                if not df.empty:
                    data[timeframe] = df
                else:
                    logger.error(f"Failed to get {symbol} {timeframe} data")
                    return {}
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting multi-timeframe data: {str(e)}")
            return {}
    
    def get_current_price(self, symbol: str) -> float:
        """Get current market price."""
        try:
            phemex_symbol = f"{symbol}/USDT:USDT"
            ticker = self.exchange.fetch_ticker(phemex_symbol)
            return ticker['last']
            
        except Exception as e:
            logger.error(f"Error getting current price for {symbol}: {str(e)}")
            return 0.0
