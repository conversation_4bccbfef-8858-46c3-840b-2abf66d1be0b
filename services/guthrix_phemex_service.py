# services/guthrix_phemex_service.py - Modified for GUTHRIX account

import logging
import os
import ccxt
from dotenv import load_dotenv
from services.phemex_service import PhemexService as BasePhemexService

logger = logging.getLogger(__name__)

class GuthrixPhemexService(BasePhemexService):
    """
    Modified PhemexService for GUTHRIX account with 5x leverage.
    """

    def __init__(self):
        # Load GUTHRIX-specific environment variables
        load_dotenv('.env.guthrix')
        logger.info("Loaded GUTHRIX environment variables from .env.guthrix")

        super().__init__()
        
        # Override default leverage to 5x
        self.default_leverage = 5
        
        logger.info("Initialized GUTHRIX Phemex service with 5x LEVERAGE")
    
    async def place_order(self, symbol, side, amount, order_type='market', price=None, stop_loss=None, take_profit=None):
        """
        Place an order on Phemex with GUTHRIX-specific settings
        """
        try:
            logger.info(f"GUTHRIX placing {side} order for {amount} {symbol} (5x leverage)")
            
            # Set leverage for this symbol
            await self.set_leverage(symbol, self.default_leverage)
            
            # Call parent method
            result = await super().place_order(symbol, side, amount, order_type, price, stop_loss, take_profit)
            
            if result.get("success"):
                logger.info(f"GUTHRIX order placed successfully: {result}")
            else:
                logger.error(f"GUTHRIX order failed: {result}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in GuthrixPhemexService.place_order: {str(e)}", exc_info=True)
            return {"error": True, "message": f"Exception: {str(e)}"}
