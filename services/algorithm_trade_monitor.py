#!/usr/bin/env python3
"""
Algorithm Trade Monitor
Ensures perfect synchronization between algorithm signals and Phemex trades.
Monitors algorithm exits and immediately closes Phemex positions.
"""

import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
from services.personal_phemex_service import PersonalPhemexService
from services.live_phemex_data_service import LivePhemexDataService

logger = logging.getLogger(__name__)

class AlgorithmTradeMonitor:
    """Monitor algorithm trades and sync with Phemex positions."""
    
    def __init__(self):
        """Initialize the trade monitor."""
        self.phemex_service = PersonalPhemexService()
        self.data_service = LivePhemexDataService()
        
        # Track active algorithm positions
        self.active_algo_positions = {}
        self.active_phemex_positions = {}
        
        # Track exit signals
        self.pending_exits = []
        
    async def start_position_monitoring(self, symbol: str, algo_signal: Dict[str, Any], phemex_order: Dict[str, Any]):
        """Start monitoring a new position."""
        try:
            position_id = f"{symbol}_{datetime.now().timestamp()}"
            
            # Track algorithm position
            self.active_algo_positions[position_id] = {
                "symbol": symbol,
                "algo_signal": algo_signal,
                "phemex_order": phemex_order,
                "entry_time": datetime.now(),
                "status": "ACTIVE",
                "last_check": datetime.now()
            }
            
            logger.info(f"🔍 Started monitoring {symbol} position: {position_id}")
            logger.info(f"   Algorithm: {algo_signal.get('action', 'Unknown')}")
            logger.info(f"   Phemex Order: {phemex_order.get('order_id', 'Unknown')}")
            
            return position_id
            
        except Exception as e:
            logger.error(f"Error starting position monitoring: {str(e)}")
            return None
    
    async def check_algorithm_exits(self, symbol: str, latest_algo_result: Dict[str, Any]) -> bool:
        """Check if algorithm generated an exit signal."""
        try:
            # Look for exit signals in algorithm result
            exit_signal = latest_algo_result.get('exit_signal', None)
            current_signal = latest_algo_result.get('signal', 'HOLD')
            
            # Algorithm exit conditions
            exit_conditions = [
                exit_signal == 'TRAILING_STOP',
                exit_signal == 'STOP_LOSS',
                exit_signal == 'TAKE_PROFIT',
                current_signal == 'CLOSE',
                current_signal == 'EXIT'
            ]
            
            if any(exit_conditions):
                logger.info(f"🚨 {symbol} ALGORITHM EXIT DETECTED!")
                logger.info(f"   Exit Signal: {exit_signal}")
                logger.info(f"   Current Signal: {current_signal}")
                logger.info(f"   Price: ${latest_algo_result.get('close', 0):.4f}")
                
                # Add to pending exits
                self.pending_exits.append({
                    "symbol": symbol,
                    "exit_reason": exit_signal or current_signal,
                    "exit_price": latest_algo_result.get('close', 0),
                    "timestamp": datetime.now()
                })
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking algorithm exits: {str(e)}")
            return False
    
    async def execute_emergency_exit(self, symbol: str, exit_reason: str) -> bool:
        """Immediately close Phemex position when algorithm exits."""
        try:
            logger.info(f"⚡ EXECUTING EMERGENCY EXIT for {symbol}")
            logger.info(f"   Reason: {exit_reason}")
            
            # Get current Phemex positions
            positions = self.phemex_service.get_open_positions()
            
            phemex_symbol = f"{symbol}/USDT:USDT"
            target_position = None
            
            for pos in positions.get('positions', []):
                if pos['symbol'] == phemex_symbol and pos['size'] != 0:
                    target_position = pos
                    break
            
            if not target_position:
                logger.warning(f"⚠️ No active {symbol} position found on Phemex")
                return True  # No position to close
            
            # Close position immediately with market order
            position_size = abs(target_position['size'])
            close_side = 'sell' if target_position['side'] == 'long' else 'buy'
            
            logger.info(f"🔥 CLOSING {symbol} POSITION:")
            logger.info(f"   Size: {position_size}")
            logger.info(f"   Side: {close_side}")
            logger.info(f"   Current PnL: ${target_position.get('unrealizedPnl', 0):.2f}")
            
            # Place market order to close position
            close_result = self.phemex_service.place_order(
                symbol=symbol,
                side=close_side,
                amount=position_size,
                order_type="market"
            )
            
            if close_result.get('success'):
                logger.info(f"✅ {symbol} POSITION CLOSED SUCCESSFULLY!")
                logger.info(f"   Close Order ID: {close_result['order_id']}")
                
                # Cancel any pending stop loss / take profit orders
                await self.cancel_pending_orders(symbol)
                
                return True
            else:
                logger.error(f"❌ FAILED TO CLOSE {symbol} POSITION: {close_result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing emergency exit: {str(e)}")
            return False
    
    async def cancel_pending_orders(self, symbol: str):
        """Cancel all pending orders for a symbol."""
        try:
            # Get open orders
            open_orders = self.phemex_service.exchange.fetch_open_orders(f"{symbol}/USDT:USDT")
            
            for order in open_orders:
                try:
                    cancel_result = self.phemex_service.exchange.cancel_order(order['id'], f"{symbol}/USDT:USDT")
                    logger.info(f"✅ Cancelled {symbol} order: {order['id']}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to cancel order {order['id']}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error cancelling pending orders: {str(e)}")
    
    async def run_continuous_monitoring(self, symbols: List[str], check_interval: int = 30):
        """Run continuous monitoring for algorithm exits."""
        logger.info(f"🔍 Starting continuous trade monitoring")
        logger.info(f"   Symbols: {symbols}")
        logger.info(f"   Check Interval: {check_interval} seconds")
        
        while True:
            try:
                # Process pending exits first
                if self.pending_exits:
                    logger.info(f"⚡ Processing {len(self.pending_exits)} pending exits")
                    
                    for exit_signal in self.pending_exits.copy():
                        success = await self.execute_emergency_exit(
                            exit_signal['symbol'], 
                            exit_signal['exit_reason']
                        )
                        
                        if success:
                            self.pending_exits.remove(exit_signal)
                            logger.info(f"✅ Processed exit for {exit_signal['symbol']}")
                
                # Check for new algorithm exits
                for symbol in symbols:
                    # This would be called by the main trading bot
                    # when it detects algorithm exits
                    pass
                
                # Wait before next check
                await asyncio.sleep(check_interval)
                
            except KeyboardInterrupt:
                logger.info("🛑 Trade monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in continuous monitoring: {str(e)}")
                await asyncio.sleep(10)  # Wait before retrying
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status."""
        return {
            "active_positions": len(self.active_algo_positions),
            "pending_exits": len(self.pending_exits),
            "last_check": datetime.now().isoformat(),
            "positions": list(self.active_algo_positions.keys()),
            "exits": [exit['symbol'] for exit in self.pending_exits]
        }

class TradeExecutionEngine:
    """Complete trade execution engine with algorithm sync."""
    
    def __init__(self):
        """Initialize the execution engine."""
        self.monitor = AlgorithmTradeMonitor()
        self.phemex_service = PersonalPhemexService()
        
    async def execute_algorithm_trade(self, symbol: str, algo_signal: Dict[str, Any]) -> bool:
        """Execute trade and start monitoring."""
        try:
            logger.info(f"🚀 Executing algorithm trade for {symbol}")
            
            # Place Phemex order
            phemex_result = self.phemex_service.place_order(
                symbol=symbol,
                side=algo_signal['action'].lower(),
                amount=algo_signal['position_size'],
                order_type="market"
            )
            
            if phemex_result.get('success'):
                # Start monitoring
                position_id = await self.monitor.start_position_monitoring(
                    symbol, algo_signal, phemex_result
                )
                
                logger.info(f"✅ {symbol} trade executed and monitoring started")
                return True
            else:
                logger.error(f"❌ {symbol} trade execution failed")
                return False
                
        except Exception as e:
            logger.error(f"Error executing algorithm trade: {str(e)}")
            return False
    
    async def check_and_sync_positions(self, symbol: str, latest_algo_data: Dict[str, Any]):
        """Check algorithm state and sync with Phemex positions."""
        try:
            # Check for algorithm exits
            exit_detected = await self.monitor.check_algorithm_exits(symbol, latest_algo_data)
            
            if exit_detected:
                logger.info(f"🚨 {symbol} algorithm exit detected - syncing with Phemex")
                await self.monitor.execute_emergency_exit(symbol, "ALGORITHM_EXIT")
            
        except Exception as e:
            logger.error(f"Error syncing positions: {str(e)}")

# Example usage
async def main():
    """Example of how to use the trade monitor."""
    monitor = AlgorithmTradeMonitor()
    
    # Start monitoring
    await monitor.run_continuous_monitoring(['BTC', 'ADA'], check_interval=30)

if __name__ == "__main__":
    asyncio.run(main())
