#!/usr/bin/env python3
"""
TIER 1: AXON Signal Service (Information Only)
Maintains original backtested thresholds for pure analysis and monitoring.
NO trade execution - only sends signals and analysis to AXON AI frontend.
"""

import os
import sys
import json
import logging
import asyncio
import requests
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/axon_signal_service.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AxonSignalService:
    """
    TIER 1: Pure signal generation and analysis service for AXON AI.
    Uses ORIGINAL backtested thresholds for consistency with backtesting results.
    NO trade execution - information only.
    """
    
    def __init__(self):
        """Initialize the AXON signal service with original backtested parameters."""
        logger.info("🔍 Initializing AXON Signal Service (TIER 1 - Information Only)")
        
        # Initialize models with ORIGINAL backtested thresholds
        # CRITICAL: These must match backtesting parameters exactly
        self.btc_model = TITAN2KModel(aggressive_mode=True)  # 0.4 threshold
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)  # 0.4 threshold
        
        # Market data service
        self.market_data = MarketDataService()
        
        # AXON AI configuration
        self.axon_api_url = "https://axonai-production.up.railway.app/api/v1"
        
        # Restore original thresholds (in case they were modified)
        self._restore_original_thresholds()
        
        logger.info("✅ AXON Signal Service initialized with ORIGINAL backtested thresholds")
        logger.info(f"   BTC Model Threshold: {self.btc_model.trend_strength_threshold}")
        logger.info(f"   ADA Model Threshold: {self.ada_model.trend_strength_threshold}")
    
    def _restore_original_thresholds(self):
        """Ensure models use original backtested thresholds."""
        # Original TITAN2K thresholds from backtesting
        self.btc_model.trend_strength_threshold = 0.4  # Aggressive mode original
        self.ada_model.trend_strength_threshold = 0.4  # Aggressive mode original
        
        logger.info("🔧 Restored original backtested thresholds:")
        logger.info(f"   BTC: {self.btc_model.trend_strength_threshold} (original backtested)")
        logger.info(f"   ADA: {self.ada_model.trend_strength_threshold} (original backtested)")
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get multi-timeframe market data for analysis."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")
            
            if symbol == "BTC":
                # BTC uses daily, 4h, and 1h data (original TITAN model)
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                medium_df = await self.market_data.get_historical_data(symbol, "4h", start_date_str, end_date_str)
                lower_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                
                return {
                    "daily": daily_df,
                    "medium": medium_df,
                    "lower": lower_df
                }
            else:  # ADA
                # ADA uses daily, 1h, and 15m data (trend-tuned model)
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                hourly_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                minute15_df = await self.market_data.get_historical_data(symbol, "15m", start_date_str, end_date_str)
                
                return {
                    "daily": daily_df,
                    "hourly": hourly_df,
                    "minute15": minute15_df
                }
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return None
    
    async def analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Analyze symbol using original backtested algorithms.
        Returns detailed analysis for AXON AI frontend.
        """
        try:
            logger.info(f"🔍 Analyzing {symbol} with original backtested algorithm")
            
            # Get market data
            data = await self.get_market_data(symbol)
            if not data:
                return {"error": "Failed to get market data"}
            
            # Process with appropriate model
            if symbol == "BTC":
                # Use original TITAN model for BTC
                result_df = self.btc_model.process_data(data["daily"], data["medium"], data["lower"])
                model_name = "TITAN2K (Original)"
            else:  # ADA
                # Use trend-tuned model for ADA
                result_df = self.ada_model.process_data(data["daily"], data["hourly"], data["minute15"])
                model_name = "TITAN2K Trend-Tuned"
            
            # Get latest analysis
            if result_df is None or result_df.empty:
                return {"error": "No analysis data generated"}
            
            latest = result_df.iloc[-1]
            current_price = latest.get('close', 0)
            signal = latest.get('signal', 'HOLD')
            confidence = latest.get('confidence', 0)
            combined_trend = latest.get('combined_trend', 0)
            
            # Create detailed analysis
            analysis = {
                "symbol": symbol,
                "model": model_name,
                "timestamp": datetime.now().isoformat(),
                "price": current_price,
                "signal": signal,
                "confidence": confidence,
                "combined_trend": combined_trend,
                "threshold": self.btc_model.trend_strength_threshold if symbol == "BTC" else self.ada_model.trend_strength_threshold,
                "backtested_parameters": True,  # Flag indicating original parameters
                "tier": "AXON_SIGNAL_SERVICE",
                "purpose": "INFORMATION_ONLY"
            }
            
            # Add detailed reasoning
            if signal in ["BUY", "SELL"]:
                analysis["reasoning"] = f"Signal generated: {signal} with {confidence:.1%} confidence. Combined trend ({combined_trend:.3f}) exceeds threshold ({analysis['threshold']:.3f}). Using original backtested parameters."
                analysis["status"] = "SIGNAL_GENERATED"
            else:
                analysis["reasoning"] = f"No signal: Combined trend ({combined_trend:.3f}) below threshold ({analysis['threshold']:.3f}). Market conditions not favorable for entry. Using original backtested parameters."
                analysis["status"] = "NO_SIGNAL"
            
            logger.info(f"📊 {symbol} Analysis: {analysis['status']} - {analysis['reasoning']}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {str(e)}")
            return {"error": str(e)}
    
    async def send_to_axon(self, analysis: Dict[str, Any]) -> bool:
        """Send analysis to AXON AI frontend."""
        try:
            # Format for AXON AI API (corrected field names)
            axon_data = {
                "bot_name": f"axon_{analysis['symbol'].lower()}",
                "symbol": analysis["symbol"],
                "signal_type": analysis.get("signal", "NEUTRAL"),
                "confidence": analysis.get("confidence", 0),
                "confidence_level": analysis.get("confidence", 0),
                "price": analysis.get("price", 0),
                "reasoning": analysis.get("reasoning", ""),
                "market_condition": analysis.get("status", "ANALYZING"),
                "timestamp": int(datetime.fromisoformat(analysis.get("timestamp", "")).timestamp()),
                "status": analysis.get("status", "ANALYZING"),
                "next_action": "WAIT" if analysis.get("signal", "HOLD") == "HOLD" else analysis.get("signal", "WAIT"),
                "risk_assessment": "LOW",
                "model_info": {
                    "name": analysis.get("model", ""),
                    "threshold": analysis.get("threshold", 0),
                    "combined_trend": analysis.get("combined_trend", 0),
                    "backtested_parameters": True,
                    "tier": "AXON_SIGNAL_SERVICE"
                }
            }
            
            # Send to AXON AI
            response = requests.post(
                f"{self.axon_api_url}/signals/bot-analysis",
                json=axon_data,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Successfully sent {analysis['symbol']} analysis to AXON AI")
                return True
            else:
                logger.warning(f"⚠️ AXON AI response: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending to AXON AI: {str(e)}")
            return False
    
    async def run_analysis_cycle(self):
        """Run one complete analysis cycle for both BTC and ADA."""
        logger.info("🔄 Starting AXON Signal Service analysis cycle")
        
        # Analyze BTC
        btc_analysis = await self.analyze_symbol("BTC")
        if "error" not in btc_analysis:
            await self.send_to_axon(btc_analysis)
        
        # Analyze ADA
        ada_analysis = await self.analyze_symbol("ADA")
        if "error" not in ada_analysis:
            await self.send_to_axon(ada_analysis)
        
        logger.info("✅ AXON Signal Service analysis cycle completed")
    
    async def run(self, interval_minutes: int = 5):
        """
        Run the AXON Signal Service continuously.
        
        Args:
            interval_minutes: Minutes between analysis cycles
        """
        logger.info(f"🚀 Starting AXON Signal Service (TIER 1)")
        logger.info(f"   Purpose: Information Only - NO trade execution")
        logger.info(f"   Interval: {interval_minutes} minutes")
        logger.info(f"   Thresholds: Original backtested parameters")
        
        while True:
            try:
                await self.run_analysis_cycle()
                
                # Wait for next cycle
                logger.info(f"⏳ Waiting {interval_minutes} minutes until next analysis")
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in AXON Signal Service: {str(e)}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

async def main():
    """Main function to run the AXON Signal Service."""
    import argparse
    
    parser = argparse.ArgumentParser(description="AXON Signal Service (TIER 1 - Information Only)")
    parser.add_argument("--interval", type=int, default=5, help="Analysis interval in minutes")
    args = parser.parse_args()
    
    # Create and run service
    service = AxonSignalService()
    await service.run(args.interval)

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run service
    asyncio.run(main())
