#!/usr/bin/env python3
"""
Force Signal Generation Test

This script temporarily overrides market conditions to force signal generation
for testing purposes. NO REAL TRADES ARE PLACED - this is a dry run test.

Usage: python3 force_signal_test.py
"""

import os
import sys
import asyncio
import logging
import pandas as pd
from datetime import datetime, timedelta

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_bullish_test_data(symbol='BTC', periods=100):
    """Create artificial bullish market data to force BUY signals"""
    
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=periods)
    dates = pd.date_range(start=start_date, end=end_date, periods=periods)
    
    # Create bullish price action
    base_price = 100000 if symbol == 'BTC' else 0.65  # Starting prices
    
    data = []
    for i, date in enumerate(dates):
        # Create upward trending prices with some volatility
        trend_factor = 1 + (i * 0.002)  # 0.2% growth per period
        volatility = 1 + (0.02 * (0.5 - abs(0.5 - (i % 10) / 10)))  # Some volatility
        
        price = base_price * trend_factor * volatility
        
        # OHLC data
        open_price = price * 0.999
        high_price = price * 1.002
        low_price = price * 0.998
        close_price = price
        volume = 1000000 + (i * 10000)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    return pd.DataFrame(data)

def create_bearish_test_data(symbol='ADA', periods=100):
    """Create artificial bearish market data to force SELL signals"""
    
    # Create date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=periods)
    dates = pd.date_range(start=start_date, end=end_date, periods=periods)
    
    # Create bearish price action
    base_price = 100000 if symbol == 'BTC' else 0.75  # Starting prices
    
    data = []
    for i, date in enumerate(dates):
        # Create downward trending prices with some volatility
        trend_factor = 1 - (i * 0.001)  # 0.1% decline per period
        volatility = 1 + (0.015 * (0.5 - abs(0.5 - (i % 8) / 8)))  # Some volatility
        
        price = base_price * trend_factor * volatility
        
        # OHLC data
        open_price = price * 1.001
        high_price = price * 1.001
        low_price = price * 0.998
        close_price = price
        volume = 800000 + (i * 8000)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    return pd.DataFrame(data)

async def test_titan2k_signals():
    """Test TITAN2K model with forced bullish data"""
    print("\n" + "="*60)
    print("🧪 TESTING TITAN2K MODEL (BTC)")
    print("="*60)
    
    try:
        # Create bullish test data
        print("📊 Creating artificial bullish market data...")
        test_data_1h = create_bullish_test_data('BTC', 168)  # 1 week of hourly data
        test_data_4h = create_bullish_test_data('BTC', 42)   # 1 week of 4h data
        test_data_1d = create_bullish_test_data('BTC', 30)   # 1 month of daily data
        
        # Initialize TITAN2K model
        model = TITAN2KModel()
        
        # Test signal generation
        print("🔍 Generating signals with artificial data...")
        
        # Process with artificial data
        signals_1h = model.generate_signals(test_data_1h, '1h')
        signals_4h = model.generate_signals(test_data_4h, '4h')
        signals_1d = model.generate_signals(test_data_1d, '1d')
        
        print(f"\n📈 SIGNAL RESULTS:")
        print(f"   1h timeframe: {signals_1h.get('signal', 'NONE')}")
        print(f"   4h timeframe: {signals_4h.get('signal', 'NONE')}")
        print(f"   1d timeframe: {signals_1d.get('signal', 'NONE')}")
        
        # Check for BUY signals
        buy_signals = [s for s in [signals_1h, signals_4h, signals_1d] if s.get('signal') == 'BUY']
        
        if buy_signals:
            print(f"✅ SUCCESS: Generated {len(buy_signals)} BUY signal(s)")
            print(f"   This confirms TITAN2K model is working correctly")
            
            # Show signal details
            for i, signal in enumerate(buy_signals):
                print(f"\n   Signal {i+1} Details:")
                print(f"     Action: {signal.get('signal')}")
                print(f"     Confidence: {signal.get('confidence', 'N/A')}")
                print(f"     Entry Price: ${signal.get('entry_price', 0):,.2f}")
                print(f"     Stop Loss: ${signal.get('stop_loss', 0):,.2f}")
                print(f"     Take Profit: ${signal.get('take_profit', 0):,.2f}")
        else:
            print(f"⚠️  WARNING: No BUY signals generated")
            print(f"   Model may be too conservative or need parameter adjustment")
        
        return len(buy_signals) > 0
        
    except Exception as e:
        print(f"❌ ERROR testing TITAN2K: {str(e)}")
        logger.error(f"TITAN2K test error: {str(e)}", exc_info=True)
        return False

async def test_trend_tuned_signals():
    """Test Trend-Tuned model with forced bearish data"""
    print("\n" + "="*60)
    print("🧪 TESTING TREND-TUNED MODEL (ADA)")
    print("="*60)
    
    try:
        # Create bearish test data for SELL signals
        print("📊 Creating artificial bearish market data...")
        test_data_1h = create_bearish_test_data('ADA', 168)  # 1 week of hourly data
        test_data_15m = create_bearish_test_data('ADA', 672) # 1 week of 15m data
        test_data_1d = create_bearish_test_data('ADA', 30)   # 1 month of daily data
        
        # Initialize Trend-Tuned model
        model = TITAN2KTrendTuned()
        
        # Test signal generation
        print("🔍 Generating signals with artificial data...")
        
        # Process with artificial data
        signals_1h = model.generate_signals(test_data_1h, '1h')
        signals_15m = model.generate_signals(test_data_15m, '15m')
        signals_1d = model.generate_signals(test_data_1d, '1d')
        
        print(f"\n📉 SIGNAL RESULTS:")
        print(f"   1h timeframe: {signals_1h.get('signal', 'NONE')}")
        print(f"   15m timeframe: {signals_15m.get('signal', 'NONE')}")
        print(f"   1d timeframe: {signals_1d.get('signal', 'NONE')}")
        
        # Check for SELL signals
        sell_signals = [s for s in [signals_1h, signals_15m, signals_1d] if s.get('signal') == 'SELL']
        
        if sell_signals:
            print(f"✅ SUCCESS: Generated {len(sell_signals)} SELL signal(s)")
            print(f"   This confirms Trend-Tuned model is working correctly")
            
            # Show signal details
            for i, signal in enumerate(sell_signals):
                print(f"\n   Signal {i+1} Details:")
                print(f"     Action: {signal.get('signal')}")
                print(f"     Confidence: {signal.get('confidence', 'N/A')}")
                print(f"     Entry Price: ${signal.get('entry_price', 0):.4f}")
                print(f"     Stop Loss: ${signal.get('stop_loss', 0):.4f}")
                print(f"     Take Profit: ${signal.get('take_profit', 0):.4f}")
        else:
            print(f"⚠️  WARNING: No SELL signals generated")
            print(f"   Model may be too conservative or need parameter adjustment")
        
        return len(sell_signals) > 0
        
    except Exception as e:
        print(f"❌ ERROR testing Trend-Tuned: {str(e)}")
        logger.error(f"Trend-Tuned test error: {str(e)}", exc_info=True)
        return False

async def main():
    """Main test function"""
    print("🧪 FORCE SIGNAL GENERATION TEST")
    print("This test verifies that trading algorithms can generate signals")
    print("when presented with favorable market conditions.")
    print("\n⚠️  NOTE: This is a DRY RUN - no real trades will be placed")
    
    # Test both models
    titan2k_ok = await test_titan2k_signals()
    trend_tuned_ok = await test_trend_tuned_signals()
    
    # Summary
    print("\n" + "="*60)
    print("📋 SIGNAL GENERATION TEST SUMMARY")
    print("="*60)
    
    if titan2k_ok:
        print("✅ TITAN2K Model: Signal generation working")
    else:
        print("❌ TITAN2K Model: No signals generated")
    
    if trend_tuned_ok:
        print("✅ Trend-Tuned Model: Signal generation working")
    else:
        print("❌ Trend-Tuned Model: No signals generated")
    
    if titan2k_ok and trend_tuned_ok:
        print("\n🎉 ALL MODELS WORKING!")
        print("✅ Algorithms can generate signals when conditions are right")
        print("✅ Current 'no signals' status is due to market conditions")
    elif titan2k_ok or trend_tuned_ok:
        print("\n⚠️  PARTIAL SUCCESS")
        print("✅ At least one model is working correctly")
        print("❌ Check the failing model for issues")
    else:
        print("\n🚨 ALL TESTS FAILED!")
        print("❌ Neither model generated signals with favorable data")
        print("❌ Check model implementations and parameters")
    
    print("\n📝 Interpretation:")
    print("• If models generate signals with test data = algorithms work correctly")
    print("• If no signals in live trading = market conditions not favorable")
    print("• This is NORMAL and shows conservative risk management")

if __name__ == "__main__":
    asyncio.run(main())
