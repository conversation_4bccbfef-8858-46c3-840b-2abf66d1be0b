#!/usr/bin/env python3
"""
Check for open positions on Phemex that might be tying up margin
"""

import asyncio
from services.caleb_phemex_service import CalebPhemexService
from services.guthrix_phemex_service import GuthrixPhemexService

async def check_positions():
    """Check for open positions and margin usage"""
    
    print("🔍 CHECKING OPEN POSITIONS AND MARGIN")
    print("="*50)
    
    # Check CALEB account
    try:
        print("🔍 CALEB Account:")
        caleb_service = CalebPhemexService()
        
        # Get positions
        positions = caleb_service.exchange.fetch_positions()
        open_positions = [pos for pos in positions if pos['contracts'] > 0]
        
        print(f"  Open Positions: {len(open_positions)}")
        for pos in open_positions:
            print(f"    {pos['symbol']}: {pos['contracts']} contracts, PnL: ${pos['unrealizedPnl']:.2f}")
        
        # Get balance details
        balance = caleb_service.exchange.fetch_balance()
        print(f"  Total Balance: ${balance['total']['USDT']:.2f}")
        print(f"  Free Balance: ${balance['free']['USDT']:.2f}")
        print(f"  Used Balance: ${balance['used']['USDT']:.2f}")
        
        # Try a very small test order
        print("  Testing minimal order size...")
        try:
            ticker = caleb_service.exchange.fetch_ticker('ADA/USDT:USDT')
            ada_price = ticker['last']
            
            # Try 5 ADA (~$3.50)
            test_quantity = 5
            print(f"    Test: {test_quantity} ADA at ${ada_price:.4f} = ${test_quantity * ada_price:.2f}")
            
            # Check minimum order size
            markets = caleb_service.exchange.load_markets()
            ada_market = markets['ADA/USDT:USDT']
            min_amount = ada_market['limits']['amount']['min']
            print(f"    Minimum order size: {min_amount} ADA")
            
            if test_quantity < min_amount:
                print(f"    ⚠️  Test quantity too small, using minimum: {min_amount} ADA")
                test_quantity = min_amount
            
        except Exception as e:
            print(f"    ❌ Error testing order: {str(e)}")
            
    except Exception as e:
        print(f"  ❌ Error checking CALEB: {str(e)}")
    
    print("\n" + "="*50)
    
    # Check GUTHRIX account
    try:
        print("🔍 GUTHRIX Account:")
        guthrix_service = GuthrixPhemexService()
        
        # Get positions
        positions = guthrix_service.exchange.fetch_positions()
        open_positions = [pos for pos in positions if pos['contracts'] > 0]
        
        print(f"  Open Positions: {len(open_positions)}")
        for pos in open_positions:
            print(f"    {pos['symbol']}: {pos['contracts']} contracts, PnL: ${pos['unrealizedPnl']:.2f}")
        
        # Get balance details
        balance = guthrix_service.exchange.fetch_balance()
        print(f"  Total Balance: ${balance['total']['USDT']:.2f}")
        print(f"  Free Balance: ${balance['free']['USDT']:.2f}")
        print(f"  Used Balance: ${balance['used']['USDT']:.2f}")
        
    except Exception as e:
        print(f"  ❌ Error checking GUTHRIX: {str(e)}")
    
    print("\n" + "="*50)
    print("💡 SOLUTIONS:")
    print("1. Close any open positions to free up margin")
    print("2. Reduce leverage from 5x to 2x or 1x")
    print("3. Use even smaller position sizes")
    print("4. Check if accounts are in cross-margin or isolated margin mode")

if __name__ == "__main__":
    asyncio.run(check_positions())
