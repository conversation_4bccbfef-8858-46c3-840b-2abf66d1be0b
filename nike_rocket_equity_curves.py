import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import os
import sys

def load_existing_nike_results():
    """Load existing Nike Rocket backtest results"""
    results = {}

    # Load existing CSV files
    files = {
        'massive_btc': 'NIKE_ROCKET_equity_curve_BTC_Nikes_Massive_Rocket_20250715_113138.csv',
        'baby_btc': 'NIKE_ROCKET_equity_curve_BTC_Nikes_Baby_Rocket_20250715_113138.csv',
        'massive_ada': 'NIKE_ROCKET_equity_curve_ADA_Nikes_Massive_Rocket_20250715_113138.csv',
        'baby_ada': 'NIKE_ROCKET_equity_curve_ADA_Nikes_Baby_Rocket_20250715_113138.csv'
    }

    for key, filename in files.items():
        if os.path.exists(filename):
            df = pd.read_csv(filename)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            results[key] = df
            print(f"✅ Loaded {filename}: {len(df)} rows")
        else:
            print(f"⚠️  File not found: {filename}")

    return results

def load_data(symbol, timeframes):
    """Load historical data for backtesting"""
    data = {}
    
    # For this example, we'll use the data_seed infrastructure
    data_seed_path = "/Users/<USER>/TomorrowTech/python-backend/data_seed"
    
    for tf in timeframes:
        if tf == '1d':
            file_path = f"{data_seed_path}/{symbol.lower()}_daily.csv"
        elif tf == '4h':
            file_path = f"{data_seed_path}/{symbol.lower()}_4h.csv"
        elif tf == '1h':
            file_path = f"{data_seed_path}/{symbol.lower()}_1h.csv"
        elif tf == '15m':
            file_path = f"{data_seed_path}/{symbol.lower()}_15m.csv"
        
        if os.path.exists(file_path):
            df = pd.read_csv(file_path)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            data[tf] = df
        else:
            print(f"⚠️  Data file not found: {file_path}")
    
    return data

def run_comprehensive_backtest(algorithm, symbol, data, initial_balance=10000):
    """Run comprehensive backtest with proper position tracking"""
    balance = initial_balance
    equity_curve = []
    trades = []
    current_position = None
    
    # Get the main timeframe data for iteration
    main_tf = list(data.keys())[0]
    main_data = data[main_tf]
    
    print(f"📊 Running {algorithm.__class__.__name__} backtest for {symbol}")
    print(f"   Data range: {main_data.index[0]} to {main_data.index[-1]}")
    print(f"   Total candles: {len(main_data)}")
    
    for i, (timestamp, row) in enumerate(main_data.iterrows()):
        # Prepare current data for algorithm (last 200 candles)
        current_data = {}
        for tf, df in data.items():
            current_data[tf] = df[df.index <= timestamp].tail(200)
        
        # Skip if insufficient data
        if any(len(df) < 50 for df in current_data.values()):
            equity_curve.append({
                'timestamp': timestamp,
                'balance': balance,
                'signal': 'INSUFFICIENT_DATA'
            })
            continue
        
        # Generate signal
        try:
            signal = algorithm.generate_signal(current_data, balance)
        except Exception as e:
            signal = None
            print(f"⚠️  Signal generation error at {timestamp}: {e}")
        
        # Close existing position if stop loss or take profit hit
        if current_position:
            current_price = row['close']
            position_pnl = 0
            
            if current_position['side'] == 'LONG':
                position_pnl = (current_price - current_position['entry_price']) * current_position['size']
                # Check stop loss
                if current_price <= current_position['stop_loss']:
                    balance += current_position['size'] * current_position['stop_loss']
                    trades.append({
                        'timestamp': timestamp,
                        'action': 'CLOSE_LONG_SL',
                        'price': current_position['stop_loss'],
                        'size': current_position['size'],
                        'pnl': (current_position['stop_loss'] - current_position['entry_price']) * current_position['size'],
                        'balance_after': balance
                    })
                    current_position = None
                # Check take profit
                elif current_price >= current_position['take_profit']:
                    balance += current_position['size'] * current_position['take_profit']
                    trades.append({
                        'timestamp': timestamp,
                        'action': 'CLOSE_LONG_TP',
                        'price': current_position['take_profit'],
                        'size': current_position['size'],
                        'pnl': (current_position['take_profit'] - current_position['entry_price']) * current_position['size'],
                        'balance_after': balance
                    })
                    current_position = None
            
            elif current_position['side'] == 'SHORT':
                position_pnl = (current_position['entry_price'] - current_price) * current_position['size']
                # Check stop loss
                if current_price >= current_position['stop_loss']:
                    balance += current_position['size'] * current_position['stop_loss']
                    trades.append({
                        'timestamp': timestamp,
                        'action': 'CLOSE_SHORT_SL',
                        'price': current_position['stop_loss'],
                        'size': current_position['size'],
                        'pnl': (current_position['entry_price'] - current_position['stop_loss']) * current_position['size'],
                        'balance_after': balance
                    })
                    current_position = None
                # Check take profit
                elif current_price <= current_position['take_profit']:
                    balance += current_position['size'] * current_position['take_profit']
                    trades.append({
                        'timestamp': timestamp,
                        'action': 'CLOSE_SHORT_TP',
                        'price': current_position['take_profit'],
                        'size': current_position['size'],
                        'pnl': (current_position['entry_price'] - current_position['take_profit']) * current_position['size'],
                        'balance_after': balance
                    })
                    current_position = None
        
        # Open new position based on signal
        if signal and signal['action'] in ['BUY', 'SELL'] and not current_position:
            current_price = row['close']
            risk_percent = signal.get('risk_percent', 2.0)
            risk_amount = balance * (risk_percent / 100)
            
            if signal['action'] == 'BUY':
                stop_loss = signal.get('stop_loss', current_price * 0.98)
                take_profit = signal.get('take_profit', current_price * 1.06)
                stop_distance = abs(current_price - stop_loss)
                
                if stop_distance > 0:
                    position_size = risk_amount / stop_distance
                    balance -= position_size * current_price  # Deduct position cost
                    
                    current_position = {
                        'side': 'LONG',
                        'entry_price': current_price,
                        'size': position_size,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'timestamp': timestamp
                    }
                    
                    trades.append({
                        'timestamp': timestamp,
                        'action': 'OPEN_LONG',
                        'price': current_price,
                        'size': position_size,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'balance_after': balance
                    })
            
            elif signal['action'] == 'SELL':
                stop_loss = signal.get('stop_loss', current_price * 1.02)
                take_profit = signal.get('take_profit', current_price * 0.94)
                stop_distance = abs(current_price - stop_loss)
                
                if stop_distance > 0:
                    position_size = risk_amount / stop_distance
                    balance += position_size * current_price  # Add short proceeds
                    
                    current_position = {
                        'side': 'SHORT',
                        'entry_price': current_price,
                        'size': position_size,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'timestamp': timestamp
                    }
                    
                    trades.append({
                        'timestamp': timestamp,
                        'action': 'OPEN_SHORT',
                        'price': current_price,
                        'size': position_size,
                        'stop_loss': stop_loss,
                        'take_profit': take_profit,
                        'balance_after': balance
                    })
        
        # Calculate current equity (including unrealized PnL)
        current_equity = balance
        if current_position:
            current_price = row['close']
            if current_position['side'] == 'LONG':
                unrealized_pnl = (current_price - current_position['entry_price']) * current_position['size']
            else:  # SHORT
                unrealized_pnl = (current_position['entry_price'] - current_price) * current_position['size']
            current_equity += unrealized_pnl
        
        # Record equity point
        equity_curve.append({
            'timestamp': timestamp,
            'balance': current_equity,
            'signal': signal['action'] if signal else 'NEUTRAL',
            'position': current_position['side'] if current_position else None
        })
        
        # Progress indicator
        if i % 1000 == 0:
            print(f"   Progress: {i}/{len(main_data)} ({i/len(main_data)*100:.1f}%)")
    
    print(f"✅ Backtest completed: {len(trades)} trades, Final balance: ${current_equity:,.2f}")
    return pd.DataFrame(equity_curve), trades

def calculate_drawdown(equity_series):
    """Calculate drawdown series"""
    peak = equity_series.expanding().max()
    drawdown = (equity_series - peak) / peak * 100
    return drawdown

def create_beautiful_equity_curves():
    """Create the beautiful equity curves comparison like the image you showed"""

    print("🚀 NIKE ROCKET ALGORITHMS - EQUITY CURVES COMPARISON")
    print("=" * 60)

    # Load existing Nike Rocket results
    results = load_existing_nike_results()

    if not results:
        print("❌ Could not load Nike Rocket results")
        return

    # Extract the equity data
    massive_btc_equity = results.get('massive_btc')
    baby_btc_equity = results.get('baby_btc')
    massive_ada_equity = results.get('massive_ada')
    baby_ada_equity = results.get('baby_ada')
    
    # Create the beautiful visualization exactly like your image
    plt.style.use('dark_background')
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    fig.patch.set_facecolor('black')
    
    # Main equity curves (log scale) - EXACTLY like your image
    ax1.set_yscale('log')
    
    # Plot equity curves with the exact colors from your image
    if baby_btc_equity is not None and 'timestamp' in baby_btc_equity.columns:
        balance_col = 'balance' if 'balance' in baby_btc_equity.columns else 'equity'
        ax1.plot(baby_btc_equity['timestamp'], baby_btc_equity[balance_col],
                 color='#00FF88', linewidth=2.5, label='Baby Rocket BTC', alpha=0.9)

    if massive_btc_equity is not None and 'timestamp' in massive_btc_equity.columns:
        balance_col = 'balance' if 'balance' in massive_btc_equity.columns else 'equity'
        ax1.plot(massive_btc_equity['timestamp'], massive_btc_equity[balance_col],
                 color='#0088FF', linewidth=2.5, label='Massive Rocket BTC', alpha=0.9)
    
    # Styling exactly like your image
    ax1.set_title('Nike Rocket Algorithms - Equity Curves Comparison (Log Scale)', 
                  fontsize=18, fontweight='bold', color='white', pad=20)
    ax1.set_ylabel('Equity ($)', fontsize=14, fontweight='bold', color='white')
    ax1.grid(True, alpha=0.2, color='gray')
    ax1.legend(loc='upper left', fontsize=12, facecolor='black', edgecolor='gray')
    
    # Set background and styling
    ax1.set_facecolor('black')
    ax1.tick_params(colors='white')
    ax1.spines['bottom'].set_color('gray')
    ax1.spines['top'].set_color('gray')
    ax1.spines['right'].set_color('gray')
    ax1.spines['left'].set_color('gray')
    
    # Format y-axis exactly like your image
    ax1.set_ylim(10000, 100000000)  # 10k to 100B
    ax1.set_yticks([10000, 100000, 1000000, 10000000, 100000000])
    ax1.set_yticklabels(['10k', '100k', '1M', '10M', '100M'])
    
    # Calculate and plot drawdowns
    if baby_btc_equity is not None and 'timestamp' in baby_btc_equity.columns:
        balance_col = 'balance' if 'balance' in baby_btc_equity.columns else 'equity'
        baby_btc_dd = calculate_drawdown(baby_btc_equity[balance_col])
        ax2.fill_between(baby_btc_equity['timestamp'], baby_btc_dd, 0,
                         color='#00FF88', alpha=0.4, label='Baby Rocket BTC')

    if massive_btc_equity is not None and 'timestamp' in massive_btc_equity.columns:
        balance_col = 'balance' if 'balance' in massive_btc_equity.columns else 'equity'
        massive_btc_dd = calculate_drawdown(massive_btc_equity[balance_col])
        ax2.fill_between(massive_btc_equity['timestamp'], massive_btc_dd, 0,
                         color='#0088FF', alpha=0.4, label='Massive Rocket BTC')
    
    ax2.set_title('Drawdown Analysis', fontsize=16, fontweight='bold', color='white')
    ax2.set_xlabel('Date', fontsize=14, fontweight='bold', color='white')
    ax2.set_ylabel('Drawdown (%)', fontsize=14, fontweight='bold', color='white')
    ax2.grid(True, alpha=0.2, color='gray')
    ax2.legend(loc='lower right', fontsize=12, facecolor='black', edgecolor='gray')
    ax2.set_facecolor('black')
    ax2.tick_params(colors='white')
    ax2.spines['bottom'].set_color('gray')
    ax2.spines['top'].set_color('gray')
    ax2.spines['right'].set_color('gray')
    ax2.spines['left'].set_color('gray')
    
    # Format x-axis
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, color='white')
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the chart
    output_file = 'nike_rocket_equity_curves_comparison.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                facecolor='black', edgecolor='none')
    
    print(f"✅ Equity curves chart saved: {output_file}")
    
    # Display performance statistics
    print("\n📊 PERFORMANCE SUMMARY")
    print("=" * 60)

    algorithms = [
        ('Baby Rocket BTC', baby_btc_equity),
        ('Massive Rocket BTC', massive_btc_equity),
        ('Baby Rocket ADA', baby_ada_equity),
        ('Massive Rocket ADA', massive_ada_equity)
    ]

    for name, equity in algorithms:
        if equity is not None and len(equity) > 0:
            balance_col = 'balance' if 'balance' in equity.columns else 'equity'
            if balance_col in equity.columns:
                initial = equity[balance_col].iloc[0]
                final = equity[balance_col].iloc[-1]
                total_return = ((final - initial) / initial) * 100
                max_dd = calculate_drawdown(equity[balance_col]).min()

                print(f"\n🚀 {name}:")
                print(f"   Initial Balance: ${initial:,.2f}")
                print(f"   Final Balance: ${final:,.2f}")
                print(f"   Total Return: {total_return:,.2f}%")
                print(f"   Max Drawdown: {max_dd:.2f}%")
                print(f"   Data Points: {len(equity)}")
            else:
                print(f"\n⚠️  {name}: No balance/equity column found")
    
    plt.show()

if __name__ == "__main__":
    create_beautiful_equity_curves()
