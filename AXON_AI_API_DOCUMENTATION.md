# 🤖 AXON AI Trading Bot API - Complete Documentation

## Overview
The AXON AI Trading Bot API provides comprehensive endpoints for trading signal management, bot analysis, and real-time market data integration. This API is designed for quantitative trading agents and automated trading systems.

## Base URL
```
Production: https://axonai-production.up.railway.app/api/v1
```

## Authentication
Currently, the API does not require authentication for signal submission. All endpoints are publicly accessible.

## Content Type
All requests should use `Content-Type: application/json`

---

## 📡 Core Trading Signal Endpoints

### 1. Submit Trading Signal (TradingView Webhook Compatible)
**POST** `/signals/tradingview-webhook`

Submit a trading signal in TradingView webhook format.

#### Request Body
```json
{
  "symbol": "BTC",
  "type": "BUY",
  "price": 104539.00,
  "target": 146354.60,
  "stop_loss": 90949.23,
  "stop_loss_percent": 13.0,
  "take_profit_percent": 40.0,
  "confidence": "High",
  "strategy": "TITAN2K Multi-Timeframe",
  "timeframe": "1h",
  "exchange": "Binance",
  "notes": "Strong bullish alignment across all timeframes"
}
```

#### Response
```json
{
  "status": "success",
  "message": "Signal received for BTC",
  "signal_id": "BTC-1749233400000"
}
```

### 2. Submit Signal with Bot Analysis (RECOMMENDED)
**POST** `/signals/bot-analysis-with-signal`

Submit both a trading signal AND the bot's reasoning/analysis in one request.

#### Request Body
```json
{
  "signal": {
    "symbol": "BTC",
    "type": "BUY",
    "price": 104539.00,
    "target": 146354.60,
    "stop_loss": 90949.23,
    "confidence": "High",
    "strategy": "TITAN2K Multi-Timeframe",
    "timeframe": "1h",
    "notes": "Strong bullish alignment across all timeframes"
  },
  "analysis": {
    "bot_name": "titan2k",
    "timestamp": 1749233400000,
    "status": "active",
    "market_condition": "Strong bullish momentum with multi-timeframe alignment",
    "reasoning": "All EMAs aligned bullishly (EMA9>21>50>100>200), MACD histogram positive across 1H/4H/daily, volatility at 78% - optimal for entry.",
    "confidence_level": 89.5,
    "next_action": "Monitoring for trailing stop adjustment as position moves in favor",
    "technical_summary": {
      "rsi_4h": 65.2,
      "macd_signal": "bullish",
      "volume_ratio": 1.45,
      "volatility_percentile": 78.0,
      "support_level": "$42,150",
      "resistance_level": "$44,800"
    },
    "risk_assessment": "Medium",
    "timeframe": "1h-4h-daily",
    "symbols_monitored": ["BTC"]
  }
}
```

#### Response
```json
{
  "status": "success",
  "message": "Signal and analysis received from titan2k",
  "signal_id": "BTC-1749233400000",
  "bot_name": "titan2k",
  "timestamp": 1749233400000,
  "signal_response": {
    "status": "success",
    "message": "Signal received for BTC",
    "signal_id": "BTC-1749233400000"
  }
}
```

### 3. Add Single Signal (Simplified)
**POST** `/signals/add-signal`

Add a single trading signal with automatic target/stop-loss calculation.

#### Request Body
```json
{
  "symbol": "ADA",
  "type": "SELL",
  "price": 0.663231,
  "stop_loss_percent": 13.0,
  "take_profit_percent": 40.0,
  "confidence": "Medium",
  "strategy": "TITAN Trend Tuned",
  "timeframe": "4h"
}
```

---

## 🤖 Bot Analysis Endpoints

### 1. Get Current Bot Analysis
**GET** `/signals/bot-analysis`

Retrieve current analysis and reasoning for all active bots.

#### Response
```json
{
  "analyses": [
    {
      "bot_name": "titan2k",
      "timestamp": 1749233400000,
      "status": "analyzing",
      "market_condition": "Multi-timeframe alignment insufficient",
      "reasoning": "BTC Daily EMA alignment incomplete: EMA9>21>50>100>200 sequence not established.",
      "confidence_level": 67.0,
      "next_action": "Waiting for combined trend strength >0.4 threshold",
      "technical_summary": {
        "rsi_4h": 58,
        "macd_signal": "bearish",
        "volume_ratio": 1.03,
        "volatility_percentile": 53,
        "support_level": "$42,150",
        "resistance_level": "$44,800",
        "current_price": "$43,250"
      },
      "risk_assessment": "Medium",
      "timeframe": "1h-4h-daily",
      "symbols_monitored": ["BTC"]
    },
    {
      "bot_name": "titan_trend_tuned",
      "timestamp": 1749233400000,
      "status": "dormant",
      "market_condition": "ADA Enhanced trend filtering active",
      "reasoning": "ADA Daily timeframe weight increased to 60% but daily trend strength insufficient.",
      "confidence_level": 74.0,
      "next_action": "Monitoring EMA50 ROC for trend regime change",
      "technical_summary": {
        "rsi_4h": 62,
        "macd_signal": "bullish",
        "volume_ratio": 1.07,
        "volatility_percentile": 57,
        "support_level": "$0.85",
        "resistance_level": "$1.12",
        "current_price": "$0.94"
      },
      "risk_assessment": "Low",
      "timeframe": "1h-4h-daily",
      "symbols_monitored": ["ADA"]
    }
  ],
  "last_updated": 1749233400000
}
```

### 2. Get Live Bot Analysis
**GET** `/signals/bot-analysis-live`

Get live bot analyses from actual bot executions (falls back to simulated if none available).

### 3. Update Bot Analysis
**POST** `/signals/bot-analysis`

Update bot analysis when a bot sends its reasoning without a signal.

#### Request Body
```json
{
  "bot_name": "titan2k",
  "timestamp": 1749233400000,
  "status": "analyzing",
  "market_condition": "Waiting for volatility increase",
  "reasoning": "BTC volatility at 12% - below minimum 15% threshold for entry",
  "confidence_level": 45.0,
  "next_action": "Monitoring for volatility percentile entry into 15-90% range",
  "technical_summary": {
    "rsi_4h": 52.1,
    "macd_signal": "neutral",
    "volume_ratio": 0.89
  },
  "risk_assessment": "Low",
  "timeframe": "1h-4h-daily",
  "symbols_monitored": ["BTC"]
}
```

---

## 📊 Signal Management Endpoints

### 1. Clear All Signals
**POST** `/signals/clear-all`

Remove all signals from the database for testing purposes.

#### Response
```json
{
  "status": "success",
  "message": "Successfully cleared ALL signals",
  "count": 15
}
```

### 2. Add Test Signals
**POST** `/signals/add-test-signals`

Add predefined test signals for both TITAN 2K (BTC) and TITAN Trend Tuned (ADA).

#### Response
```json
{
  "status": "success",
  "message": "Successfully added 2 test signals",
  "signals": [
    {
      "symbol": "BTC",
      "type": "buy",
      "strategy": "TITAN2K Multi-Timeframe",
      "price": 43250.50
    },
    {
      "symbol": "ADA", 
      "type": "buy",
      "strategy": "TITAN Trend Tuned",
      "price": 0.94
    }
  ]
}
```

### 3. Get Signal History
**GET** `/signals/history/{symbol}?days=7&signal_type=buy`

Retrieve historical signals for a specific cryptocurrency.

### 4. Get Recent Signals
**GET** `/signals/recent-webhook?hours=24`

Get recent webhook signals with price information.

---

## 🎯 Bot Specifications

### TITAN 2K
- **Primary Symbol**: BTC
- **Strategy**: Multi-timeframe analysis with 2000%+ return strategy
- **Timeframes**: 1h, 4h, daily
- **Key Indicators**: EMA alignment (9>21>50>100>200), MACD, ADX, volatility percentile
- **Entry Conditions**: Combined trend strength >0.4, volatility 15-90%, ADX >25

### TITAN Trend Tuned  
- **Primary Symbol**: ADA
- **Strategy**: Enhanced trend following with bear market filtering
- **Timeframes**: 1h, 4h, daily
- **Key Indicators**: EMA50/200 relationship, market regime detection, enhanced filtering
- **Entry Conditions**: 70%+ confidence for BUY signals, timeframe alignment >70%

---

## 📝 Data Models

### TradingSignal
```typescript
{
  symbol: string;           // "BTC", "ADA", etc.
  type: "BUY" | "SELL";    // Signal direction
  price: number;           // Entry price
  target?: number;         // Take profit target
  stop_loss?: number;      // Stop loss level
  stop_loss_percent?: number;    // Default: 13.0
  take_profit_percent?: number;  // Default: 40.0
  confidence?: string;     // "High", "Medium", "Low"
  strategy?: string;       // Strategy name
  timeframe?: string;      // "1h", "4h", "1d"
  exchange?: string;       // Exchange name
  notes?: string;          // Additional notes
}
```

### BotAnalysis
```typescript
{
  bot_name: string;              // "titan2k", "titan_trend_tuned"
  timestamp: number;             // Unix timestamp in milliseconds
  status: string;                // "active", "dormant", "analyzing"
  market_condition: string;      // Current market assessment
  reasoning: string;             // Detailed reasoning
  confidence_level: number;     // 0-100
  next_action: string;           // What bot is waiting for
  technical_summary: object;    // Technical indicators
  risk_assessment: string;      // "Low", "Medium", "High"
  timeframe: string;            // Analysis timeframe
  symbols_monitored: string[];  // Symbols being watched
}
```

---

## 🚀 Quick Start for Quant Agents

### 1. Send a Simple BUY Signal
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/tradingview-webhook" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTC",
    "type": "BUY",
    "price": 43250.50,
    "confidence": "High",
    "strategy": "TITAN2K"
  }'
```

### 2. Send Signal with Analysis
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/bot-analysis-with-signal" \
  -H "Content-Type: application/json" \
  -d '{
    "signal": {
      "symbol": "BTC",
      "type": "BUY",
      "price": 43250.50
    },
    "analysis": {
      "bot_name": "titan2k",
      "timestamp": '$(date +%s000)',
      "status": "active",
      "market_condition": "Strong bullish momentum",
      "reasoning": "All technical indicators aligned for entry",
      "confidence_level": 85.0,
      "next_action": "Monitoring position",
      "risk_assessment": "Medium",
      "timeframe": "1h-4h-daily",
      "symbols_monitored": ["BTC"]
    }
  }'
```

### 3. Clear All Signals (Testing)
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/clear-all"
```

### 4. Check Bot Analysis
```bash
curl -X GET "https://axonai-production.up.railway.app/api/v1/signals/bot-analysis"
```

---

## ⚠️ Important Notes

1. **Signal Processing**: All signals are stored in the database and displayed in real-time on the AXON AI platform
2. **Bot Names**: Use "titan2k" for BTC signals and "titan_trend_tuned" for ADA signals
3. **Timestamps**: Use Unix timestamps in milliseconds
4. **Price Precision**: Use appropriate decimal places (BTC: 2, ADA: 4)
5. **Rate Limits**: No current rate limits, but avoid excessive requests
6. **Error Handling**: All endpoints return detailed error messages with HTTP status codes

---

## 🔧 Testing Endpoints

Use these endpoints to test your integration:

1. **Add test signals**: `POST /signals/add-test-signals`
2. **Clear all signals**: `POST /signals/clear-all`  
3. **Check current signals**: `GET /signals/recent-webhook`
4. **Verify bot analysis**: `GET /signals/bot-analysis`

---

## 📞 Support

For technical support or questions about the API, please refer to the AXON AI platform documentation or contact the development team.

**Last Updated**: June 6, 2025
**API Version**: v1
**Status**: Production Ready ✅
