2025-06-03 21:26:15,289 - services.market_data_service - INFO - Fetched 695 candles
FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value 'TRAILING_STOP' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.
  df.at[df.index[i], 'exit_signal'] = 'TRAILING_STOP'
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO - 📊 BTC ANALYSIS @ $106,320.00
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO -    🔍 Price too close to SMA20; Low confidence (0.0%); Market consolidating
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO -    📈 Trend: Sideways/Consolidation
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO -    💪 Strength: Weak
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO -    ⚡ Momentum: Bullish (MACD > Signal)
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO -    📊 Volume: Normal
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO -    🎯 Confidence: 0.0%
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO - ❌ NO TRADE: Market consolidating - waiting for clear direction (RSI: 74.3, Trend: Weak)
2025-06-03 21:26:17,256 - __main__ - INFO - Using 3.0% risk for BTC with 5x LEVERAGE
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO - Generating signals for ADA
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO - Getting market data for ADA
2025-06-03 21:26:17,256 - scripts.run_dual_model_signal_sender - INFO - Fetching ADA 1d data from 2025-03-05 to 2025-06-03
2025-06-03 21:26:17,257 - services.market_data_service - INFO - Fetching ADA 1d data from 2025-03-05 to 2025-06-03
2025-06-03 21:26:17,262 - services.market_data_service - INFO - Loaded 27 rows from data_seed
2025-06-03 21:26:17,262 - scripts.run_dual_model_signal_sender - INFO - Fetching ADA 1h data from 2025-03-05 to 2025-06-03
2025-06-03 21:26:17,262 - services.market_data_service - INFO - Fetching ADA 1h data from 2025-03-05 to 2025-06-03
2025-06-03 21:26:17,291 - services.market_data_service - INFO - Loaded 647 rows from data_seed
2025-06-03 21:26:17,291 - scripts.run_dual_model_signal_sender - INFO - Fetching ADA 15m data from 2025-03-05 to 2025-06-03
2025-06-03 21:26:17,291 - services.market_data_service - INFO - Fetching ADA 15m data from 2025-03-05 to 2025-06-03
2025-06-03 21:26:17,377 - services.market_data_service - INFO - Loaded 2424 rows from data_seed
FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value 'TRAILING_STOP' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.
  df.at[df.index[i], 'exit_signal'] = 'TRAILING_STOP'
FutureWarning: Setting an item of incompatible dtype is deprecated and will raise an error in a future version of pandas. Value 'TRAILING_STOP' has dtype incompatible with float64, please explicitly cast to a compatible dtype first.
  df.at[df.index[i], 'exit_signal'] = 'TRAILING_STOP'
2025-06-03 21:26:24,786 - scripts.run_dual_model_signal_sender - INFO - 📊 ADA ANALYSIS @ $0.66
2025-06-03 21:26:24,786 - scripts.run_dual_model_signal_sender - INFO -    🔍 RSI neutral (58.9); MACD momentum weak; Price too close to SMA20; Low confidence (0.0%); Market consolidating
2025-06-03 21:26:24,786 - scripts.run_dual_model_signal_sender - INFO -    📈 Trend: Sideways/Consolidation
2025-06-03 21:26:24,786 - scripts.run_dual_model_signal_sender - INFO -    💪 Strength: Weak
2025-06-03 21:26:24,786 - scripts.run_dual_model_signal_sender - INFO -    ⚡ Momentum: Neutral (MACD ≈ Signal)
2025-06-03 21:26:24,786 - scripts.run_dual_model_signal_sender - INFO -    📊 Volume: Normal
2025-06-03 21:26:24,787 - scripts.run_dual_model_signal_sender - INFO -    🎯 Confidence: 0.0%
2025-06-03 21:26:24,787 - scripts.run_dual_model_signal_sender - INFO - ❌ NO TRADE: Market consolidating - waiting for clear direction (RSI: 58.9, Trend: Weak)
2025-06-03 21:26:24,787 - __main__ - INFO - Using 2.0% risk for ADA with 5x LEVERAGE
2025-06-03 21:26:24,787 - __main__ - INFO - Waiting 5 minutes until next update