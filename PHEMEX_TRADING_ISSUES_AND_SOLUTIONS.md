# 🔧 PHEMEX TRADING ISSUES AND SOLUTIONS

## 📋 CURRENT STATUS
- **Account:** Personal Cashcoldgame Phemex account
- **API Key:** 6e9dd1a7-6e9a-4c79-9e93-15420aa40cb7
- **Balance:** $122.27 USDT in contract wallet
- **Manual Trading:** ✅ WORKING (user can place trades manually)
- **Bot Trading:** ✅ FIXED (position mode resolved)
- **Position Mode:** ✅ OneWay (was Hedged - causing the error)
- **Leverage:** ✅ 10x set successfully for BTC and ADA

## 🎯 SUCCESSFUL MANUAL TRADES (REFERENCE)
**User's successful trades that we need to replicate:**
- **BTC Position:** 0.001 BTC contracts at $105,365.4 (Symbol: BTC/USDT:USDT)
- **ADA Position:** 174.14 ADA contracts at $0.6292 (Symbol: ADA/USDT:USDT)
- **Position Mode:** Unknown (need to determine)
- **Leverage:** 10x (successfully set via API)

## ❌ BOT TRADING ERRORS ENCOUNTERED

### 1. **TE_PLACE_ORDER_INSUFFICIENT_QUOTE_BALANCE**
- **Error Code:** 11106
- **Meaning:** Insufficient balance for the order
- **Cause:** Wrong balance calculation or symbol format
- **Status:** RESOLVED (switched to USDT-settled contracts)

### 2. **TE_CANNOT_COVER_ESTIMATE_ORDER_LOSS**
- **Error Code:** 11082
- **Meaning:** Insufficient margin to cover potential losses
- **Cause:** Position size too large for available margin
- **Status:** PARTIALLY RESOLVED (reduced position sizes)

### 3. **TE_ERR_INCONSISTENT_POS_MODE** ✅ RESOLVED
- **Error Code:** 20004
- **Meaning:** Position mode mismatch between account settings and order
- **Cause:** Account was in "Hedged" mode, bot expected "OneWay" mode
- **Status:** ✅ RESOLVED - Position mode changed to OneWay
- **Solution:** Used `exchange.set_position_mode(hedged=False, symbol='BTC/USDT:USDT')`

## 🔍 TECHNICAL FINDINGS

### ✅ WORKING CONFIGURATIONS:
- **API Connection:** Perfect with new credentials
- **Symbol Format:** BTC/USDT:USDT and ADA/USDT:USDT (USDT-settled perpetuals)
- **Leverage Setting:** 10x successfully applied
- **Balance Access:** $122.80 USDT available in contract wallet
- **Market Data:** Real-time prices working
- **Position Sizing:** Calculations working

### ❌ FAILING CONFIGURATIONS:
- **Position Mode:** Bot expects different mode than account is set to
- **Order Parameters:** Some parameter mismatch preventing execution
- **Contract Specifications:** May need to match exact contract settings

## 🛠️ POSITION MODE INVESTIGATION NEEDED

### **Phemex Position Modes:**
1. **One-Way Mode:** Single position per symbol (long OR short)
2. **Hedge Mode:** Separate long and short positions per symbol
3. **Cross Margin:** Shared margin across all positions
4. **Isolated Margin:** Separate margin per position

### **Current Account Settings (UNKNOWN):**
- Position Mode: ❓ NEED TO CHECK
- Margin Mode: ❓ NEED TO CHECK
- Default Settings: ❓ NEED TO VERIFY

## 🔧 IMMEDIATE FIXES NEEDED

### 1. **Check Account Position Mode**
```python
# Get current position mode for USDT-settled contracts
try:
    # Method 1: Check via positions
    positions = exchange.fetch_positions()
    for pos in positions:
        if pos['symbol'] in ['BTC/USDT:USDT', 'ADA/USDT:USDT']:
            print(f"Position mode info: {pos.get('info', {})}")

    # Method 2: Direct API call to get position mode
    response = exchange.private_get_g_positions({'symbol': 'BTCUSDT'})
    print(f"Position mode response: {response}")

except Exception as e:
    print(f"Error checking position mode: {e}")
```

### 2. **Set Correct Position Mode**
```python
# For USDT-settled contracts, set position mode
try:
    # Set to One-Way mode (most common for beginners)
    result = exchange.set_position_mode(hedged=False, symbol='BTC/USDT:USDT')
    print(f"BTC position mode set: {result}")

    result = exchange.set_position_mode(hedged=False, symbol='ADA/USDT:USDT')
    print(f"ADA position mode set: {result}")

except Exception as e:
    print(f"Error setting position mode: {e}")
```

### 3. **Check and Set Margin Mode**
```python
# Check current margin mode
try:
    # For USDT-settled contracts, check margin mode
    btc_margin = exchange.fetch_margin_mode('BTC/USDT:USDT')
    ada_margin = exchange.fetch_margin_mode('ADA/USDT:USDT')
    print(f"BTC margin mode: {btc_margin}")
    print(f"ADA margin mode: {ada_margin}")

    # Set to Cross margin (recommended for beginners)
    exchange.set_margin_mode('cross', 'BTC/USDT:USDT')
    exchange.set_margin_mode('cross', 'ADA/USDT:USDT')

except Exception as e:
    print(f"Error with margin mode: {e}")
```

### 4. **Match Manual Trade Parameters Exactly**
```python
# Use EXACT parameters from successful manual trades
btc_order_params = {
    'symbol': 'BTC/USDT:USDT',
    'type': 'market',
    'side': 'buy',
    'amount': 0.001,  # Exact amount from manual trade
    'params': {
        'timeInForce': 'IOC',  # Immediate or Cancel
        'reduceOnly': False,
        'postOnly': False
    }
}

ada_order_params = {
    'symbol': 'ADA/USDT:USDT',
    'type': 'market',
    'side': 'buy',
    'amount': 174.14,  # Exact amount from manual trade
    'params': {
        'timeInForce': 'IOC',
        'reduceOnly': False,
        'postOnly': False
    }
}
```

## 📊 DEBUGGING STEPS COMPLETED

### ✅ COMPLETED:
1. **API Authentication:** Working perfectly
2. **Balance Verification:** $122.80 USDT confirmed
3. **Symbol Format:** Corrected to USDT-settled contracts
4. **Leverage Setting:** 10x applied successfully
5. **Position Sizing:** Adjusted to safe levels
6. **Market Data Access:** Real-time prices working
7. **Account Permissions:** Contract trading confirmed

### 🔄 IN PROGRESS:
1. **Position Mode Resolution:** Current critical issue
2. **Order Parameter Matching:** Need exact manual trade replication

### ⏳ TODO:
1. **Determine account position mode**
2. **Set bot to match account mode**
3. **Test with exact manual trade parameters**
4. **Implement position mode switching**
5. **Create robust error handling for all Phemex error codes**

## 🎯 SUCCESS CRITERIA

### **Phase 1: Basic Trading (CURRENT GOAL)**
- ✅ Execute single ADA buy order successfully
- ✅ Execute single BTC buy order successfully
- ✅ Verify positions are created correctly
- ✅ Close positions successfully

### **Phase 2: Algorithm Integration**
- ✅ Connect TITAN algorithms to working trade execution
- ✅ Implement proper risk management
- ✅ Add stop loss and take profit orders
- ✅ Create comprehensive logging

### **Phase 3: Production Deployment**
- ✅ Replicate working setup to CALEB and GUTHRIX accounts
- ✅ Implement monitoring and alerts
- ✅ Add performance tracking
- ✅ Create automated recovery systems

## 🚨 CRITICAL NEXT STEPS

1. **IMMEDIATE:** Resolve TE_ERR_INCONSISTENT_POS_MODE error
2. **URGENT:** Test successful trade execution with correct position mode
3. **HIGH:** Document exact working parameters for replication
4. **MEDIUM:** Implement robust error handling for all scenarios

## 📝 NOTES FOR FUTURE AGENTS

### **Key Context:**
- User has working manual trades but bot fails on position mode
- All API functions work except actual trade execution
- Need to match bot parameters exactly to manual trade settings
- Account has sufficient balance and proper permissions

### **Critical Files:**
- `services/personal_phemex_service.py` - Main trading service
- `.env.personal` - Personal account credentials
- `scripts/test_phemex_functions.py` - Comprehensive testing script

### **Working Credentials:**
- API Key: 6e9dd1a7-6e9a-4c79-9e93-15420aa40cb7
- Secret: qY6u0gH5-00o8Qgi_1BFlCu9oxzt9OunKVYPUi6gbY8wNmQyMDk2Yi1lMWFlLTQ4OTctOWViOC1jMjJlMTIxZDRlOTQ
- IP Whitelist: *************

### **Successful Manual Trades to Replicate:**
- BTC: 0.001 contracts on BTC/USDT:USDT at $105,365.4
- ADA: 174.14 contracts on ADA/USDT:USDT at $0.6292

**🎯 GOAL: Get bot to execute trades with same parameters as successful manual trades!**
