openapi: 3.0.3
info:
  title: AXON AI Trading Bot API
  description: |
    Comprehensive API for trading signal management, bot analysis, and real-time market data integration.
    Designed for quantitative trading agents and automated trading systems.
  version: 1.0.0
  contact:
    name: AXON AI Support
    url: https://axonai.com
  license:
    name: Proprietary
    url: https://axonai.com/license

servers:
  - url: https://axonai-production.up.railway.app/api/v1
    description: Production server

paths:
  /signals/tradingview-webhook:
    post:
      summary: Submit Trading Signal
      description: Submit a trading signal in TradingView webhook format
      tags:
        - Trading Signals
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TradingSignal'
            examples:
              btc_buy:
                summary: BTC Buy Signal
                value:
                  symbol: "BTC"
                  type: "BUY"
                  price: 104539.00
                  target: 146354.60
                  stop_loss: 90949.23
                  confidence: "High"
                  strategy: "TITAN2K Multi-Timeframe"
              ada_sell:
                summary: ADA Sell Signal
                value:
                  symbol: "ADA"
                  type: "SELL"
                  price: 0.663231
                  target: 0.397939
                  stop_loss: 0.749451
                  confidence: "Medium"
                  strategy: "TITAN Trend Tuned"
      responses:
        '200':
          description: Signal received successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignalResponse'
        '400':
          description: Invalid request data
        '500':
          description: Server error

  /signals/bot-analysis-with-signal:
    post:
      summary: Submit Signal with Bot Analysis
      description: Submit both a trading signal AND the bot's reasoning/analysis
      tags:
        - Trading Signals
        - Bot Analysis
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SignalWithAnalysis'
      responses:
        '200':
          description: Signal and analysis received successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SignalAnalysisResponse'

  /signals/bot-analysis:
    get:
      summary: Get Current Bot Analysis
      description: Retrieve current analysis and reasoning for all active bots
      tags:
        - Bot Analysis
      responses:
        '200':
          description: Bot analysis retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MultiBotAnalysis'
    post:
      summary: Update Bot Analysis
      description: Update bot analysis when a bot sends reasoning without a signal
      tags:
        - Bot Analysis
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BotAnalysis'
      responses:
        '200':
          description: Bot analysis updated successfully

  /signals/clear-all:
    post:
      summary: Clear All Signals
      description: Remove all signals from the database for testing purposes
      tags:
        - Signal Management
      responses:
        '200':
          description: All signals cleared successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "success"
                  message:
                    type: string
                    example: "Successfully cleared ALL signals"
                  count:
                    type: integer
                    example: 15

  /signals/add-test-signals:
    post:
      summary: Add Test Signals
      description: Add predefined test signals for both TITAN 2K (BTC) and TITAN Trend Tuned (ADA)
      tags:
        - Signal Management
      responses:
        '200':
          description: Test signals added successfully

components:
  schemas:
    TradingSignal:
      type: object
      required:
        - symbol
        - type
        - price
      properties:
        symbol:
          type: string
          description: Trading symbol
          example: "BTC"
          enum: ["BTC", "ADA", "ETH", "SOL"]
        type:
          type: string
          description: Signal direction
          example: "BUY"
          enum: ["BUY", "SELL"]
        price:
          type: number
          description: Entry price
          example: 104539.00
        target:
          type: number
          description: Take profit target
          example: 146354.60
        stop_loss:
          type: number
          description: Stop loss level
          example: 90949.23
        stop_loss_percent:
          type: number
          description: Stop loss percentage
          example: 13.0
          default: 13.0
        take_profit_percent:
          type: number
          description: Take profit percentage
          example: 40.0
          default: 40.0
        confidence:
          type: string
          description: Signal confidence level
          example: "High"
          enum: ["High", "Medium", "Low"]
        strategy:
          type: string
          description: Strategy name
          example: "TITAN2K Multi-Timeframe"
        timeframe:
          type: string
          description: Chart timeframe
          example: "1h"
        exchange:
          type: string
          description: Exchange name
          example: "Binance"
        notes:
          type: string
          description: Additional notes
          example: "Strong bullish alignment across all timeframes"

    BotAnalysis:
      type: object
      required:
        - bot_name
        - timestamp
        - status
        - market_condition
        - reasoning
        - confidence_level
        - next_action
        - risk_assessment
        - timeframe
        - symbols_monitored
      properties:
        bot_name:
          type: string
          description: Bot identifier
          example: "titan2k"
          enum: ["titan2k", "titan_trend_tuned"]
        timestamp:
          type: integer
          description: Unix timestamp in milliseconds
          example: 1749233400000
        status:
          type: string
          description: Bot status
          example: "active"
          enum: ["active", "dormant", "analyzing"]
        market_condition:
          type: string
          description: Current market assessment
          example: "Strong bullish momentum with multi-timeframe alignment"
        reasoning:
          type: string
          description: Detailed reasoning for bot behavior
          example: "All EMAs aligned bullishly, MACD histogram positive across timeframes"
        confidence_level:
          type: number
          description: Confidence level 0-100
          example: 89.5
          minimum: 0
          maximum: 100
        next_action:
          type: string
          description: What the bot is waiting for or planning
          example: "Monitoring for trailing stop adjustment"
        technical_summary:
          type: object
          description: Technical indicators summary
          example:
            rsi_4h: 65.2
            macd_signal: "bullish"
            volume_ratio: 1.45
            volatility_percentile: 78.0
            support_level: "$42,150"
            resistance_level: "$44,800"
        risk_assessment:
          type: string
          description: Current risk level
          example: "Medium"
          enum: ["Low", "Medium", "Medium-High", "High"]
        timeframe:
          type: string
          description: Analysis timeframe
          example: "1h-4h-daily"
        symbols_monitored:
          type: array
          items:
            type: string
          description: Symbols being monitored
          example: ["BTC"]

    SignalWithAnalysis:
      type: object
      required:
        - signal
        - analysis
      properties:
        signal:
          $ref: '#/components/schemas/TradingSignal'
        analysis:
          $ref: '#/components/schemas/BotAnalysis'

    MultiBotAnalysis:
      type: object
      properties:
        analyses:
          type: array
          items:
            $ref: '#/components/schemas/BotAnalysis'
        last_updated:
          type: integer
          description: Last update timestamp
          example: 1749233400000

    SignalResponse:
      type: object
      properties:
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Signal received for BTC"
        signal_id:
          type: string
          example: "BTC-1749233400000"

    SignalAnalysisResponse:
      type: object
      properties:
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Signal and analysis received from titan2k"
        signal_id:
          type: string
          example: "BTC-1749233400000"
        bot_name:
          type: string
          example: "titan2k"
        timestamp:
          type: integer
          example: 1749233400000

tags:
  - name: Trading Signals
    description: Submit and manage trading signals
  - name: Bot Analysis
    description: Bot reasoning and analysis endpoints
  - name: Signal Management
    description: Testing and management utilities
