#!/usr/bin/env python3
"""
CALEB's CORRECTED Equity Curves Generator

FIXES the discrepancy CALEB found between logs and equity curves.
Uses the EXACT same algorithms we tested individually to ensure accuracy.
"""

import pandas as pd
import numpy as np
import sys
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import the EXACT algorithms we tested individually
sys.path.append('/Users/<USER>/TomorrowTech/python-backend/data_seed')

def load_data_seed_with_resampling(symbol: str):
    """Load data from data_seed and resample 1h to create 4h data."""
    
    symbol_map = {'BTC': 'XBTUSD', 'ADA': 'ADAUSDT'}
    
    if symbol not in symbol_map:
        return None
    
    data = {}
    
    print(f"📊 Loading data for {symbol}...")
    
    # Load daily data
    try:
        daily_filename = f"data_seed/{symbol_map[symbol]}_1440.csv"
        df_daily = pd.read_csv(daily_filename, header=None, 
                              names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'trades'])
        df_daily['datetime'] = pd.to_datetime(df_daily['timestamp'], unit='s')
        df_daily.set_index('timestamp', inplace=True)
        df_daily = df_daily.sort_index()
        data['daily'] = df_daily
        
    except Exception as e:
        print(f"   ❌ Error loading daily data: {e}")
        return None
    
    # Load 1h data and resample to 4h
    try:
        hourly_filename = f"data_seed/{symbol_map[symbol]}_60.csv"
        df_1h = pd.read_csv(hourly_filename, header=None, 
                           names=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'trades'])
        df_1h['datetime'] = pd.to_datetime(df_1h['timestamp'], unit='s')
        
        # Create copy for resampling
        df_1h_for_resampling = df_1h.copy()
        df_1h_for_resampling.set_index('datetime', inplace=True)
        df_1h_for_resampling = df_1h_for_resampling.sort_index()
        
        # Keep original with timestamp index
        df_1h.set_index('timestamp', inplace=True)
        df_1h = df_1h.sort_index()
        data['lower'] = df_1h
        
        # Resample 1h to 4h
        df_4h = df_1h_for_resampling.resample('4h').agg({
            'open': 'first', 'high': 'max', 'low': 'min', 'close': 'last',
            'volume': 'sum', 'trades': 'sum', 'timestamp': 'first'
        }).dropna()
        
        df_4h['datetime'] = df_4h.index
        df_4h['timestamp'] = df_4h.index.astype('int64') // 10**9
        df_4h.set_index('timestamp', inplace=True)
        data['medium'] = df_4h
        
    except Exception as e:
        print(f"   ❌ Error loading/resampling 1h data: {e}")
        return None
    
    duration_years = (data['daily']['datetime'].iloc[-1] - data['daily']['datetime'].iloc[0]).days / 365.25
    print(f"   ✅ Loaded {duration_years:.1f} years of data")
    return data

def run_corrected_algorithm(algorithm_name: str, symbol: str, initial_equity: float = 10000):
    """Run the EXACT algorithms we tested individually."""
    
    print(f"🧪 Running CORRECTED {algorithm_name} on {symbol}...")
    
    # Load data
    data = load_data_seed_with_resampling(symbol)
    if not data:
        return None
    
    # Import the EXACT algorithms we tested
    try:
        if algorithm_name == "PT_Adjusted":
            from titan_2_kcaleb_notrailing_confidencemode_ptadj import titan2kCaleb
            print(f"   ✅ Using PT Adjusted (3.5x/2.5x TP)")
        elif algorithm_name == "Confidence_Mode":
            from titan_2_kcaleb_notrailing_confidencemode import titan2kCaleb
            print(f"   ✅ Using Confidence Mode (4.0x/3.0x TP)")
        elif algorithm_name == "Aggressive_Only":
            # Import the specific file CALEB requested
            import importlib.util
            spec = importlib.util.spec_from_file_location("titan2kCaleb", "data_seed/titan_2_kcaleb_notrailing (1).py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            titan2kCaleb = module.titan2kCaleb
            print(f"   ✅ Using Aggressive Only (4.0x TP, 4% risk, aggressive mode only)")
        elif algorithm_name == "Conservative_02_Vol15":
            # Import CALEB's latest Conservative 0.2 Vol 15 version
            import importlib.util
            spec = importlib.util.spec_from_file_location("titan2kCaleb", "data_seed/titan_2_kcaleb_notrailing_confidencemode_ptadj_conservative02_vol15.py")
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            titan2kCaleb = module.titan2kCaleb
            print(f"   ✅ Using Conservative 0.2 Vol 15 (3.5x/2.5x TP, 0.2 conservative threshold, 15% vol)")
        else:
            print(f"   ❌ Unknown algorithm: {algorithm_name}")
            return None
            
        model = titan2kCaleb()
        
    except Exception as e:
        print(f"   ❌ Error importing {algorithm_name}: {e}")
        return None
    
    # Calculate indicators
    for tf_name in ['daily', 'medium', 'lower']:
        data[tf_name] = model.calculate_indicators(data[tf_name])
    
    # Combine timeframes
    combined_df = model.combine_timeframes(data['daily'], data['medium'], data['lower'])
    
    # Generate signals and equity curve
    try:
        result_df = model.generate_signals_with_compounding_and_reversal(combined_df, initial_equity)
        
        # Extract equity curve
        equity_curve = result_df[['datetime', 'equity']].copy()
        equity_curve = equity_curve.dropna()
        
        # Calculate drawdown
        equity_values = equity_curve['equity'].values
        peak = np.maximum.accumulate(equity_values)
        drawdown = (equity_values - peak) / peak * 100
        equity_curve['drawdown'] = drawdown
        
        # Calculate performance metrics
        final_equity = equity_curve['equity'].iloc[-1]
        total_return = (final_equity - initial_equity) / initial_equity * 100
        max_drawdown = np.min(drawdown)
        
        # Count trades
        trades = result_df[result_df['signal'] != 'NEUTRAL']
        
        print(f"   ✅ {algorithm_name}: {total_return:+.2f}% return, {max_drawdown:.2f}% max DD, {len(trades)} trades")
        
        return {
            'algorithm': algorithm_name,
            'symbol': symbol,
            'equity_curve': equity_curve,
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'total_trades': len(trades),
            'final_equity': final_equity
        }
        
    except Exception as e:
        print(f"   ❌ Error running {algorithm_name}: {e}")
        return None

def verify_against_previous_results():
    """Verify our corrected results match the individual algorithm tests."""
    
    print("🔍 VERIFYING CORRECTED RESULTS AGAINST PREVIOUS TESTS")
    print("=" * 70)
    
    # Expected results from our individual algorithm tests
    expected_results = {
        'PT_Adjusted': {
            'BTC': {'return': 21845987078.27, 'drawdown': -34.44, 'trades': 2124},
            'ADA': {'return': 386559.22, 'drawdown': -29.80, 'trades': 925}
        },
        'Confidence_Mode': {
            'BTC': {'return': 19807472760.0, 'drawdown': -30.64, 'trades': 1915},
            'ADA': {'return': 364374.0, 'drawdown': -35.98, 'trades': 855}
        }
    }
    
    algorithms = ['PT_Adjusted', 'Confidence_Mode', 'Aggressive_Only', 'Conservative_02_Vol15']
    symbols = ['BTC', 'ADA']
    
    all_results = []
    verification_passed = True
    
    for symbol in symbols:
        print(f"\n📊 Processing {symbol}...")
        
        for algorithm in algorithms:
            result = run_corrected_algorithm(algorithm, symbol)
            if result:
                all_results.append(result)
                
                # Verify against expected results (if available)
                if algorithm in expected_results:
                    expected = expected_results[algorithm][symbol]
                    actual_return = result['total_return']
                    expected_return = expected['return']

                    # Allow 1% tolerance for floating point differences
                    return_diff = abs(actual_return - expected_return) / expected_return * 100

                    if return_diff < 1.0:  # Less than 1% difference
                        print(f"   ✅ VERIFIED: {algorithm} {symbol} return matches ({return_diff:.3f}% diff)")
                    else:
                        print(f"   ❌ MISMATCH: {algorithm} {symbol} return differs by {return_diff:.2f}%")
                        print(f"      Expected: {expected_return:,.2f}%, Got: {actual_return:,.2f}%")
                        verification_passed = False
                else:
                    print(f"   📊 NEW ALGORITHM: {algorithm} {symbol} return: {result['total_return']:,.2f}%")
    
    # Create corrected summary
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"\n📊 CORRECTED EQUITY CURVE RESULTS:")
    print("=" * 80)
    print(f"{'Algorithm':<20} {'Symbol':<6} {'Return %':<15} {'Max DD %':<10} {'Trades':<8} {'Status':<10}")
    print("=" * 80)
    
    for result in all_results:
        if result['algorithm'] in expected_results:
            expected = expected_results[result['algorithm']][result['symbol']]
            return_diff = abs(result['total_return'] - expected['return']) / expected['return'] * 100
            status = "✅ MATCH" if return_diff < 1.0 else "❌ DIFF"
        else:
            status = "📊 NEW"

        print(f"{result['algorithm']:<20} {result['symbol']:<6} {result['total_return']:>14,.2f} {result['max_drawdown']:>9.2f} {result['total_trades']:>7} {status:<10}")
    
    print("=" * 80)
    
    # Save corrected equity curves
    for result in all_results:
        filename = f"CORRECTED_equity_curve_{result['symbol']}_{result['algorithm']}_{timestamp}.csv"
        result['equity_curve'].to_csv(filename, index=False)
        print(f"✅ Saved corrected: {filename}")
    
    # Create corrected summary
    summary_data = []
    for result in all_results:
        summary_data.append({
            'algorithm': result['algorithm'],
            'symbol': result['symbol'],
            'total_return_percent': result['total_return'],
            'max_drawdown_percent': result['max_drawdown'],
            'total_trades': result['total_trades'],
            'final_equity': result['final_equity']
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_filename = f"CORRECTED_EQUITY_CURVES_SUMMARY_{timestamp}.csv"
    summary_df.to_csv(summary_filename, index=False)
    
    print(f"\n🎯 VERIFICATION RESULT:")
    if verification_passed:
        print("✅ ALL RESULTS VERIFIED - Equity curves now match individual algorithm tests!")
        print("✅ PT Adjusted should now show as the clear winner in equity curves")
    else:
        print("❌ Some results still don't match - need further investigation")
    
    print(f"\n🏆 CORRECTED WINNER CONFIRMATION:")
    pt_btc = next(r for r in all_results if r['algorithm'] == 'PT_Adjusted' and r['symbol'] == 'BTC')
    pt_ada = next(r for r in all_results if r['algorithm'] == 'PT_Adjusted' and r['symbol'] == 'ADA')
    conf_btc = next(r for r in all_results if r['algorithm'] == 'Confidence_Mode' and r['symbol'] == 'BTC')
    conf_ada = next(r for r in all_results if r['algorithm'] == 'Confidence_Mode' and r['symbol'] == 'ADA')
    
    print(f"PT Adjusted BTC: {pt_btc['total_return']:,.2f}% vs Confidence Mode BTC: {conf_btc['total_return']:,.2f}%")
    print(f"PT Adjusted ADA: {pt_ada['total_return']:,.2f}% vs Confidence Mode ADA: {conf_ada['total_return']:,.2f}%")
    
    if pt_btc['total_return'] > conf_btc['total_return'] and pt_ada['total_return'] > conf_ada['total_return']:
        print("✅ CONFIRMED: PT Adjusted wins on both BTC and ADA!")
    else:
        print("❌ Issue still exists - PT Adjusted should win both")
    
    return all_results, summary_filename

if __name__ == "__main__":
    verify_against_previous_results()
