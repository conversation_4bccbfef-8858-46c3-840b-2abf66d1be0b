import pandas as pd
import os
import sys

def load_existing_nike_results():
    """Load existing Nike Rocket backtest results"""
    results = {}
    
    # Load existing CSV files
    files = {
        'massive_btc': 'NIKE_ROCKET_equity_curve_BTC_Nikes_Massive_Rocket_20250715_113138.csv',
        'baby_btc': 'NIKE_ROCKET_equity_curve_BTC_Nikes_Baby_Rocket_20250715_113138.csv',
        'massive_ada': 'NIKE_ROCKET_equity_curve_ADA_Nikes_Massive_Rocket_20250715_113138.csv',
        'baby_ada': 'NIKE_ROCKET_equity_curve_ADA_Nikes_Baby_Rocket_20250715_113138.csv'
    }
    
    for key, filename in files.items():
        if os.path.exists(filename):
            df = pd.read_csv(filename)
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            results[key] = df
            print(f"✅ Loaded {filename}: {len(df)} rows")
        else:
            print(f"⚠️  File not found: {filename}")
    
    return results

def calculate_drawdown(equity_series):
    """Calculate drawdown series"""
    peak = equity_series.expanding().max()
    drawdown = (equity_series - peak) / peak * 100
    return drawdown

def create_proper_html_chart(massive_btc_equity, baby_btc_equity, massive_ada_equity, baby_ada_equity):
    """Create proper HTML chart like our other working visualizations"""
    
    # Prepare data for JavaScript
    def prepare_data(df, name):
        if df is None or len(df) == 0:
            return [], []
        
        balance_col = 'balance' if 'balance' in df.columns else 'equity'
        dates = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()
        equity = df[balance_col].tolist()
        
        return dates, equity
    
    # Get data for all algorithms
    baby_btc_dates, baby_btc_equity_data = prepare_data(baby_btc_equity, 'Baby Rocket BTC')
    massive_btc_dates, massive_btc_equity_data = prepare_data(massive_btc_equity, 'Massive Rocket BTC')
    baby_ada_dates, baby_ada_equity_data = prepare_data(baby_ada_equity, 'Baby Rocket ADA')
    massive_ada_dates, massive_ada_equity_data = prepare_data(massive_ada_equity, 'Massive Rocket ADA')
    
    # Calculate drawdowns
    def calculate_drawdown_data(equity_data):
        if not equity_data:
            return []
        
        peak = equity_data[0]
        drawdowns = []
        
        for value in equity_data:
            if value > peak:
                peak = value
            drawdown = ((value - peak) / peak) * 100
            drawdowns.append(drawdown)
        
        return drawdowns
    
    baby_btc_dd = calculate_drawdown_data(baby_btc_equity_data)
    massive_btc_dd = calculate_drawdown_data(massive_btc_equity_data)
    baby_ada_dd = calculate_drawdown_data(baby_ada_equity_data)
    massive_ada_dd = calculate_drawdown_data(massive_ada_equity_data)
    
    # Create the HTML content
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>🚀 NIKE ROCKET ALGORITHMS - EQUITY CURVES COMPARISON</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #0a0a0a; color: white; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .chart-container {{ width: 100%; height: 600px; margin: 20px 0; background: #1a1a1a; border-radius: 10px; padding: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.5); }}
        .summary {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); }}
        .winner {{ background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }}
        .metric {{ display: inline-block; margin: 15px 30px; text-align: center; }}
        .metric-value {{ font-size: 24px; font-weight: bold; display: block; }}
        .metric-label {{ font-size: 14px; opacity: 0.9; }}
        .algorithm-header {{ font-size: 18px; font-weight: bold; margin-bottom: 10px; }}
        .comparison {{ display: flex; justify-content: space-around; flex-wrap: wrap; }}
        .algo-card {{ flex: 1; min-width: 300px; margin: 10px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 NIKE ROCKET ALGORITHMS - EQUITY CURVES COMPARISON</h1>
        <h2>BTC & ADA Performance Analysis (Log Scale)</h2>
        <p><strong>CALEB's Nike Rocket System - Massive vs Baby Rocket</strong></p>
    </div>
    
    <div class="summary winner">
        <h3>🏆 PERFORMANCE CHAMPION: MASSIVE ROCKET</h3>
        <div class="comparison">
            <div class="algo-card">
                <div class="algorithm-header">🛡️ BABY ROCKET (Lower Risk)</div>
                <div class="metric">
                    <span class="metric-value">BTC: 5.56T%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">ADA: 364K%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-19.16%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
            </div>
            <div class="algo-card">
                <div class="algorithm-header">🚀 MASSIVE ROCKET (Higher Risk)</div>
                <div class="metric">
                    <span class="metric-value">BTC: 40.6Q%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">ADA: 520M%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-35.01%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="chart-container">
        <div id="equityChart"></div>
    </div>
    
    <div class="chart-container">
        <div id="drawdownChart"></div>
    </div>

    <script>
        // Data for equity curves
        var baby_btc_dates = {baby_btc_dates};
        var baby_btc_equity = {baby_btc_equity_data};
        var massive_btc_dates = {massive_btc_dates};
        var massive_btc_equity = {massive_btc_equity_data};
        var baby_ada_dates = {baby_ada_dates};
        var baby_ada_equity = {baby_ada_equity_data};
        var massive_ada_dates = {massive_ada_dates};
        var massive_ada_equity = {massive_ada_equity_data};
        
        // Equity curves chart
        var equityTraces = [
            {{
                x: baby_btc_dates,
                y: baby_btc_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket BTC',
                line: {{ color: '#00FF88', width: 3 }}
            }},
            {{
                x: massive_btc_dates,
                y: massive_btc_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket BTC',
                line: {{ color: '#0088FF', width: 4 }}
            }},
            {{
                x: baby_ada_dates,
                y: baby_ada_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket ADA',
                line: {{ color: '#FF9800', width: 3 }}
            }},
            {{
                x: massive_ada_dates,
                y: massive_ada_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket ADA',
                line: {{ color: '#9C27B0', width: 4 }}
            }}
        ];
        
        var equityLayout = {{
            title: {{
                text: 'Nike Rocket Algorithms - Equity Curves Comparison (Log Scale)',
                font: {{ size: 18, color: 'white' }}
            }},
            xaxis: {{ 
                title: 'Date',
                gridcolor: '#333',
                color: 'white'
            }},
            yaxis: {{ 
                title: 'Equity ($)', 
                type: 'log',
                gridcolor: '#333',
                color: 'white'
            }},
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#1a1a1a',
            paper_bgcolor: '#1a1a1a',
            font: {{ color: 'white' }}
        }};
        
        Plotly.newPlot('equityChart', equityTraces, equityLayout);

        // Drawdown chart
        var drawdownTraces = [
            {{
                x: baby_btc_dates,
                y: {baby_btc_dd},
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket BTC DD',
                line: {{ color: '#00FF88', width: 3 }},
                fill: 'tonexty'
            }},
            {{
                x: massive_btc_dates,
                y: {massive_btc_dd},
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket BTC DD',
                line: {{ color: '#0088FF', width: 3 }},
                fill: 'tonexty'
            }},
            {{
                x: baby_ada_dates,
                y: {baby_ada_dd},
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket ADA DD',
                line: {{ color: '#FF9800', width: 3 }},
                fill: 'tonexty'
            }},
            {{
                x: massive_ada_dates,
                y: {massive_ada_dd},
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket ADA DD',
                line: {{ color: '#9C27B0', width: 3 }},
                fill: 'tonexty'
            }}
        ];
        
        var drawdownLayout = {{
            title: {{
                text: 'Drawdown Analysis',
                font: {{ size: 18, color: 'white' }}
            }},
            xaxis: {{ 
                title: 'Date',
                gridcolor: '#333',
                color: 'white'
            }},
            yaxis: {{ 
                title: 'Drawdown (%)',
                gridcolor: '#333',
                color: 'white'
            }},
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#1a1a1a',
            paper_bgcolor: '#1a1a1a',
            font: {{ color: 'white' }}
        }};
        
        Plotly.newPlot('drawdownChart', drawdownTraces, drawdownLayout);
    </script>
</body>
</html>
"""
    
    # Save the HTML file
    output_file = 'nike_rocket_equity_curves_comparison_PROPER.html'
    with open(output_file, 'w') as f:
        f.write(html_content)
    
    print(f"✅ PROPER equity curves chart saved: {output_file}")
    return output_file

def create_beautiful_equity_curves():
    """Create the beautiful equity curves comparison like the image you showed"""
    
    print("🚀 NIKE ROCKET ALGORITHMS - EQUITY CURVES COMPARISON")
    print("=" * 60)
    
    # Load existing Nike Rocket results
    results = load_existing_nike_results()
    
    if not results:
        print("❌ Could not load Nike Rocket results")
        return
    
    # Extract the equity data
    massive_btc_equity = results.get('massive_btc')
    baby_btc_equity = results.get('baby_btc')
    massive_ada_equity = results.get('massive_ada')
    baby_ada_equity = results.get('baby_ada')
    
    # Create proper HTML with embedded Plotly charts like our other visualizations
    output_file = create_proper_html_chart(massive_btc_equity, baby_btc_equity, massive_ada_equity, baby_ada_equity)
    
    # Display performance statistics
    print("\n📊 PERFORMANCE SUMMARY")
    print("=" * 60)
    
    algorithms = [
        ('Baby Rocket BTC', baby_btc_equity),
        ('Massive Rocket BTC', massive_btc_equity),
        ('Baby Rocket ADA', baby_ada_equity),
        ('Massive Rocket ADA', massive_ada_equity)
    ]
    
    for name, equity in algorithms:
        if equity is not None and len(equity) > 0:
            balance_col = 'balance' if 'balance' in equity.columns else 'equity'
            if balance_col in equity.columns:
                initial = equity[balance_col].iloc[0]
                final = equity[balance_col].iloc[-1]
                total_return = ((final - initial) / initial) * 100
                max_dd = calculate_drawdown(equity[balance_col]).min()
                
                print(f"\n🚀 {name}:")
                print(f"   Initial Balance: ${initial:,.2f}")
                print(f"   Final Balance: ${final:,.2f}")
                print(f"   Total Return: {total_return:,.2f}%")
                print(f"   Max Drawdown: {max_dd:.2f}%")
                print(f"   Data Points: {len(equity)}")
            else:
                print(f"\n⚠️  {name}: No balance/equity column found")
    
    return output_file

if __name__ == "__main__":
    create_beautiful_equity_curves()
