#!/usr/bin/env python3
"""
Live Signal Execution Testing
Tests the complete pipeline with live data and simulated signals.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService
from services.live_phemex_data_service import LivePhemexDataService
from services.algorithm_trade_monitor import TradeExecutionEngine
from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/live_signal_testing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LiveSignalTester:
    """Test live signal execution with real Phemex integration."""
    
    def __init__(self):
        """Initialize the tester."""
        self.phemex_service = PersonalPhemexService()
        self.data_service = LivePhemexDataService()
        self.execution_engine = TradeExecutionEngine()
        
        # Initialize models
        self.btc_model = TITAN2KModel(aggressive_mode=True)
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)
        
        self.test_results = {}
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}: {details}")
        
        self.test_results[test_name] = {
            "success": success, 
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
    
    async def test_live_data_fetching(self) -> bool:
        """Test live data fetching from Phemex."""
        try:
            logger.info("🔍 Testing live data fetching")
            
            # Test BTC data
            btc_data = await self.data_service.get_multi_timeframe_data(
                "BTC", ["1d", "4h", "1h"], limit=100
            )
            
            # Test ADA data
            ada_data = await self.data_service.get_multi_timeframe_data(
                "ADA", ["1d", "1h", "15m"], limit=100
            )
            
            if btc_data and ada_data:
                btc_latest = btc_data["1h"].iloc[-1]
                ada_latest = ada_data["1h"].iloc[-1]
                
                details = f"BTC: ${btc_latest['close']:.2f}, ADA: ${ada_latest['close']:.4f}"
                self.log_test("Live Data Fetching", True, details)
                return True
            else:
                self.log_test("Live Data Fetching", False, "Failed to fetch live data")
                return False
                
        except Exception as e:
            self.log_test("Live Data Fetching", False, str(e))
            return False
    
    async def test_algorithm_signal_generation(self) -> Dict[str, Any]:
        """Test algorithm signal generation with live data."""
        try:
            logger.info("🔍 Testing algorithm signal generation with live data")
            
            # Get live data
            btc_data = await self.data_service.get_multi_timeframe_data(
                "BTC", ["1d", "4h", "1h"], limit=500
            )
            ada_data = await self.data_service.get_multi_timeframe_data(
                "ADA", ["1d", "1h", "15m"], limit=500
            )
            
            signals = {}
            
            # Process BTC
            if btc_data:
                btc_result = self.btc_model.process_data(
                    btc_data["1d"], btc_data["4h"], btc_data["1h"]
                )
                
                if not btc_result.empty:
                    latest = btc_result.iloc[-1]
                    signals["BTC"] = {
                        "symbol": "BTC",
                        "action": latest.get('signal', 'HOLD'),
                        "confidence": latest.get('combined_trend', 0.0),
                        "entry_price": latest.get('close', 0.0),
                        "atr": latest.get('atr14', 0.0),
                        "timeframe_alignment": latest.get('timeframe_alignment', 0.0),
                        "exit_signal": latest.get('exit_signal', None)
                    }
            
            # Process ADA
            if ada_data:
                ada_result = self.ada_model.process_data(
                    ada_data["1d"], ada_data["1h"], ada_data["15m"]
                )
                
                if not ada_result.empty:
                    latest = ada_result.iloc[-1]
                    signals["ADA"] = {
                        "symbol": "ADA",
                        "action": latest.get('signal', 'HOLD'),
                        "confidence": latest.get('combined_trend', 0.0),
                        "entry_price": latest.get('close', 0.0),
                        "atr": latest.get('atr14', 0.0),
                        "timeframe_alignment": latest.get('timeframe_alignment', 0.0),
                        "exit_signal": latest.get('exit_signal', None)
                    }
            
            # Log results
            for symbol, signal in signals.items():
                logger.info(f"📊 {symbol} Signal:")
                logger.info(f"   Action: {signal['action']}")
                logger.info(f"   Confidence: {signal['confidence']:.3f}")
                logger.info(f"   Price: ${signal['entry_price']:.4f}")
                logger.info(f"   Exit Signal: {signal.get('exit_signal', 'None')}")
            
            details = f"Generated {len(signals)} signals"
            self.log_test("Algorithm Signal Generation", True, details)
            return signals
            
        except Exception as e:
            self.log_test("Algorithm Signal Generation", False, str(e))
            return {}
    
    def create_simulated_signals(self) -> Dict[str, Any]:
        """Create simulated signals for testing."""
        logger.info("🎭 Creating simulated signals for testing")
        
        # Get current prices
        btc_price = self.data_service.get_current_price("BTC")
        ada_price = self.data_service.get_current_price("ADA")
        
        simulated_signals = {
            "BTC": {
                "symbol": "BTC",
                "action": "BUY",
                "confidence": 0.65,  # Above 0.4 threshold
                "entry_price": btc_price,
                "atr": btc_price * 0.025,  # 2.5% ATR
                "timeframe_alignment": 0.75,
                "position_size": 0.001,  # Minimum BTC size
                "stop_loss": btc_price * 0.975,  # 2.5% stop
                "take_profit": btc_price * 1.0375,  # 3.75% profit (1.5:1 R:R)
                "exit_signal": None
            },
            "ADA": {
                "symbol": "ADA",
                "action": "BUY",
                "confidence": 0.55,  # Above 0.4 threshold
                "entry_price": ada_price,
                "atr": ada_price * 0.03,  # 3% ATR
                "timeframe_alignment": 0.70,
                "position_size": 100.0,  # 100 ADA
                "stop_loss": ada_price * 0.97,  # 3% stop
                "take_profit": ada_price * 1.036,  # 3.6% profit (1.2:1 R:R)
                "exit_signal": None
            }
        }
        
        for symbol, signal in simulated_signals.items():
            logger.info(f"🎭 Simulated {symbol} Signal:")
            logger.info(f"   Action: {signal['action']}")
            logger.info(f"   Confidence: {signal['confidence']:.3f}")
            logger.info(f"   Entry: ${signal['entry_price']:.4f}")
            logger.info(f"   Stop: ${signal['stop_loss']:.4f}")
            logger.info(f"   Target: ${signal['take_profit']:.4f}")
        
        return simulated_signals
    
    async def test_signal_execution_pipeline(self, use_simulated: bool = True) -> bool:
        """Test complete signal execution pipeline."""
        try:
            logger.info("🚀 Testing signal execution pipeline")
            
            if use_simulated:
                signals = self.create_simulated_signals()
            else:
                signals = await self.test_algorithm_signal_generation()
            
            if not signals:
                self.log_test("Signal Execution Pipeline", False, "No signals to test")
                return False
            
            # Test signal validation
            valid_signals = 0
            for symbol, signal in signals.items():
                if (abs(signal['confidence']) >= 0.4 and 
                    signal['timeframe_alignment'] >= 0.6 and
                    signal['action'] in ['BUY', 'SELL']):
                    valid_signals += 1
                    logger.info(f"✅ {symbol} signal valid for execution")
                else:
                    logger.info(f"⏭️ {symbol} signal below execution threshold")
            
            # Ask user if they want to execute real trades
            if valid_signals > 0:
                user_input = input(f"\n🚨 Execute {valid_signals} REAL trades? (y/N): ")
                if user_input.lower() == 'y':
                    # Execute real trades
                    executed = 0
                    for symbol, signal in signals.items():
                        if (abs(signal['confidence']) >= 0.4 and 
                            signal['timeframe_alignment'] >= 0.6 and
                            signal['action'] in ['BUY', 'SELL']):
                            
                            success = await self.execution_engine.execute_algorithm_trade(symbol, signal)
                            if success:
                                executed += 1
                                logger.info(f"✅ {symbol} trade executed successfully")
                            else:
                                logger.error(f"❌ {symbol} trade execution failed")
                    
                    details = f"Executed {executed}/{valid_signals} trades"
                    self.log_test("Signal Execution Pipeline", executed > 0, details)
                    return executed > 0
                else:
                    details = f"Validated {valid_signals} signals (execution skipped)"
                    self.log_test("Signal Execution Pipeline", True, details)
                    return True
            else:
                details = "No valid signals for execution"
                self.log_test("Signal Execution Pipeline", True, details)
                return True
                
        except Exception as e:
            self.log_test("Signal Execution Pipeline", False, str(e))
            return False
    
    async def test_exit_signal_monitoring(self) -> bool:
        """Test exit signal monitoring and position closing."""
        try:
            logger.info("🔍 Testing exit signal monitoring")
            
            # Simulate an exit signal
            simulated_exit = {
                "symbol": "BTC",
                "signal": "CLOSE",
                "exit_signal": "TRAILING_STOP",
                "close": 105000.0,
                "reason": "Algorithm trailing stop hit"
            }
            
            # Test exit detection
            exit_detected = await self.execution_engine.monitor.check_algorithm_exits(
                "BTC", simulated_exit
            )
            
            if exit_detected:
                logger.info("✅ Exit signal detection working")
                
                # Test emergency exit (dry run)
                logger.info("🧪 Testing emergency exit logic (dry run)")
                details = "Exit signal detection and logic validated"
                self.log_test("Exit Signal Monitoring", True, details)
                return True
            else:
                self.log_test("Exit Signal Monitoring", False, "Exit signal not detected")
                return False
                
        except Exception as e:
            self.log_test("Exit Signal Monitoring", False, str(e))
            return False
    
    async def run_comprehensive_test(self) -> bool:
        """Run comprehensive live signal testing."""
        logger.info("🚀 Starting Comprehensive Live Signal Testing")
        logger.info("="*60)
        logger.info("PURPOSE: Test complete pipeline with live data")
        logger.info("FEATURES: Live data, algorithm signals, trade execution")
        logger.info("MONITORING: Exit signal detection and position sync")
        logger.info("="*60)
        
        tests = [
            ("Live Data Fetching", self.test_live_data_fetching),
            ("Algorithm Signal Generation", lambda: self.test_algorithm_signal_generation()),
            ("Signal Execution Pipeline", lambda: self.test_signal_execution_pipeline(use_simulated=True)),
            ("Exit Signal Monitoring", self.test_exit_signal_monitoring),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔬 Running: {test_name}")
            try:
                if test_name == "Algorithm Signal Generation":
                    result = await test_func()
                    success = bool(result)  # Convert dict to bool
                else:
                    success = await test_func()
                
                if success:
                    passed += 1
            except Exception as e:
                logger.error(f"❌ {test_name} failed with exception: {str(e)}")
        
        # Final report
        logger.info("\n" + "="*60)
        logger.info("📊 LIVE SIGNAL TEST RESULTS")
        logger.info("="*60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            logger.info(f"{status} {test_name}: {result['details']}")
        
        logger.info(f"\n🎯 SUMMARY: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED!")
            logger.info("✅ Live signal execution system is ready for production!")
        else:
            logger.info("⚠️  Some tests failed - review before production")
        
        return passed == total

async def main():
    """Main function."""
    tester = LiveSignalTester()
    success = await tester.run_comprehensive_test()
    
    if success:
        logger.info("\n🚀 READY FOR PRODUCTION DEPLOYMENT!")
        logger.info("The complete live signal execution system is operational.")
    else:
        logger.info("\n🔧 Please resolve issues before production deployment.")

if __name__ == "__main__":
    asyncio.run(main())
