#!/usr/bin/env python3
"""
Phemex Position Mode Diagnostic Tool
Safely diagnoses position mode issues without placing real orders.
"""

import os
import sys
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PhemexDiagnostic:
    """Diagnose Phemex position mode and account settings."""
    
    def __init__(self):
        """Initialize the diagnostic tool."""
        self.service = PersonalPhemexService()
        self.exchange = self.service.exchange
        
    def check_account_info(self):
        """Check basic account information."""
        logger.info("🔍 Checking account information")
        
        try:
            balance = self.exchange.fetch_balance()
            logger.info(f"✅ Account connected successfully")
            logger.info(f"   USDT Balance: ${balance['total'].get('USDT', 0):.2f}")
            logger.info(f"   Free USDT: ${balance['free'].get('USDT', 0):.2f}")
            logger.info(f"   Used USDT: ${balance['used'].get('USDT', 0):.2f}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Account connection failed: {str(e)}")
            return False
    
    def check_markets_and_symbols(self):
        """Check available markets and symbol formats."""
        logger.info("🔍 Checking markets and symbols")
        
        try:
            markets = self.exchange.load_markets()
            
            # Check for our target symbols
            target_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
            
            for symbol in target_symbols:
                if symbol in markets:
                    market = markets[symbol]
                    logger.info(f"✅ {symbol} found:")
                    logger.info(f"   Type: {market.get('type', 'Unknown')}")
                    logger.info(f"   Settle: {market.get('settle', 'Unknown')}")
                    logger.info(f"   Contract: {market.get('contract', False)}")
                    logger.info(f"   Min Amount: {market['limits']['amount']['min']}")
                    logger.info(f"   Active: {market.get('active', False)}")
                else:
                    logger.error(f"❌ {symbol} not found in markets")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking markets: {str(e)}")
            return False
    
    def check_current_positions(self):
        """Check current open positions."""
        logger.info("🔍 Checking current positions")
        
        try:
            positions = self.exchange.fetch_positions()
            logger.info(f"Found {len(positions)} position entries")
            
            active_positions = [pos for pos in positions if pos['size'] != 0]
            logger.info(f"Active positions: {len(active_positions)}")
            
            # Check for BTC and ADA positions specifically
            target_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
            
            for symbol in target_symbols:
                symbol_positions = [pos for pos in positions if pos['symbol'] == symbol]
                
                if symbol_positions:
                    for pos in symbol_positions:
                        logger.info(f"📊 {symbol} position:")
                        logger.info(f"   Side: {pos.get('side', 'N/A')}")
                        logger.info(f"   Size: {pos.get('size', 0)}")
                        logger.info(f"   Contracts: {pos.get('contracts', 0)}")
                        logger.info(f"   Entry Price: {pos.get('entryPrice', 0)}")
                        logger.info(f"   Mark Price: {pos.get('markPrice', 0)}")
                        logger.info(f"   PnL: {pos.get('unrealizedPnl', 0)}")
                        
                        # Check raw info for position mode details
                        info = pos.get('info', {})
                        if info:
                            logger.info(f"   Raw info keys: {list(info.keys())}")
                            # Look for position mode indicators
                            for key, value in info.items():
                                if 'mode' in key.lower() or 'hedge' in key.lower():
                                    logger.info(f"   {key}: {value}")
                else:
                    logger.info(f"📊 {symbol}: No positions found")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking positions: {str(e)}")
            return False
    
    def check_leverage_settings(self):
        """Check current leverage settings."""
        logger.info("🔍 Checking leverage settings")
        
        target_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        
        for symbol in target_symbols:
            try:
                # Try to get current leverage
                logger.info(f"Checking {symbol} leverage...")
                
                # Method 1: Try to set leverage to current value to see what it is
                current_leverage = self.exchange.set_leverage(10, symbol)
                logger.info(f"✅ {symbol} leverage: {current_leverage}")
                
            except Exception as e:
                logger.error(f"❌ Error checking {symbol} leverage: {str(e)}")
        
        return True
    
    def test_order_validation(self):
        """Test order validation without placing real orders."""
        logger.info("🔍 Testing order validation (DRY RUN)")
        
        target_symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        test_amounts = [0.001, 10.0]  # BTC and ADA amounts from manual trades
        
        for symbol, amount in zip(target_symbols, test_amounts):
            try:
                logger.info(f"Testing {symbol} order validation...")
                
                # Get current price
                ticker = self.exchange.fetch_ticker(symbol)
                current_price = ticker['last']
                order_value = amount * current_price
                
                logger.info(f"   Amount: {amount} {symbol}")
                logger.info(f"   Price: ${current_price:.4f}")
                logger.info(f"   Value: ${order_value:.2f}")
                
                # Create order parameters (but don't place)
                order_params = {
                    'symbol': symbol,
                    'type': 'market',
                    'side': 'buy',
                    'amount': amount,
                    'params': {
                        'timeInForce': 'IOC',
                        'reduceOnly': False,
                        'postOnly': False
                    }
                }
                
                logger.info(f"   Order params: {order_params}")
                logger.info(f"✅ {symbol} order validation prepared")
                
            except Exception as e:
                logger.error(f"❌ Error validating {symbol} order: {str(e)}")
        
        return True
    
    def run_full_diagnostic(self):
        """Run complete diagnostic check."""
        logger.info("🚀 Starting Phemex Position Mode Diagnostic")
        logger.info("="*60)
        logger.info("PURPOSE: Diagnose position mode issues safely")
        logger.info("ACCOUNT: Personal Cashcoldgame Phemex account")
        logger.info("MODE: READ-ONLY (No real orders placed)")
        logger.info("="*60)
        
        tests = [
            ("Account Connection", self.check_account_info),
            ("Markets & Symbols", self.check_markets_and_symbols),
            ("Current Positions", self.check_current_positions),
            ("Leverage Settings", self.check_leverage_settings),
            ("Order Validation", self.test_order_validation),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\n🔬 Running: {test_name}")
            try:
                success = test_func()
                if success:
                    passed += 1
                    logger.info(f"✅ {test_name} completed")
                else:
                    logger.error(f"❌ {test_name} failed")
            except Exception as e:
                logger.error(f"❌ {test_name} failed with exception: {str(e)}")
        
        # Final report
        logger.info("\n" + "="*60)
        logger.info("📊 DIAGNOSTIC REPORT")
        logger.info("="*60)
        logger.info(f"Tests completed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All diagnostics passed!")
            logger.info("💡 Ready to proceed with position mode fix")
        else:
            logger.info("⚠️  Some diagnostics failed")
            logger.info("🔧 Review the errors before proceeding")
        
        logger.info("\n📋 NEXT STEPS:")
        logger.info("1. Review the diagnostic results above")
        logger.info("2. If all looks good, run: python3 scripts/fix_phemex_position_mode.py")
        logger.info("3. This will configure position mode and test with small orders")
        
        return passed == total

def main():
    """Main function."""
    diagnostic = PhemexDiagnostic()
    diagnostic.run_full_diagnostic()

if __name__ == "__main__":
    main()
