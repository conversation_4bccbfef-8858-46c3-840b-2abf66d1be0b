#!/usr/bin/env python3
"""
Test Position Mode Fix
Verify that the position mode fix resolved the TE_ERR_INCONSISTENT_POS_MODE error.
"""

import os
import sys
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PositionModeFixTester:
    """Test that position mode fix worked."""
    
    def __init__(self):
        """Initialize the tester."""
        self.service = PersonalPhemexService()
        self.exchange = self.service.exchange
        
    def test_leverage_setting(self):
        """Test leverage setting (this was failing before)."""
        logger.info("🧪 Testing leverage setting (previously failing)")
        
        symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        success_count = 0
        
        for symbol in symbols:
            try:
                logger.info(f"Setting {symbol} leverage to 10x...")
                result = self.exchange.set_leverage(10, symbol)
                logger.info(f"✅ {symbol} leverage set successfully: {result}")
                success_count += 1
                
            except Exception as e:
                error_msg = str(e)
                if "TE_ERR_INCONSISTENT_POS_MODE" in error_msg:
                    logger.error(f"❌ {symbol} STILL HAS POSITION MODE ERROR: {error_msg}")
                else:
                    logger.error(f"❌ {symbol} leverage error: {error_msg}")
        
        return success_count == len(symbols)
    
    def check_position_mode_status(self):
        """Check current position mode status."""
        logger.info("🔍 Checking current position mode status")
        
        try:
            positions = self.exchange.fetch_positions()
            
            # Check BTC and ADA position modes
            for pos in positions:
                if pos['symbol'] in ['BTC/USDT:USDT', 'ADA/USDT:USDT']:
                    info = pos.get('info', {})
                    pos_mode = info.get('posMode', 'Unknown')
                    logger.info(f"📊 {pos['symbol']}: Position Mode = {pos_mode}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking position mode: {str(e)}")
            return False
    
    def test_small_order_placement(self):
        """Test placing a very small order to verify everything works."""
        logger.info("🧪 Testing small order placement")
        
        # Test with minimal BTC amount
        symbol = 'BTC/USDT:USDT'
        amount = 0.001  # $105 worth
        
        try:
            # Get current price
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            order_value = amount * current_price
            
            logger.info(f"Order: {amount} {symbol} at ${current_price:.2f} = ${order_value:.2f}")
            
            # Create order parameters
            order_params = {
                'symbol': symbol,
                'type': 'market',
                'side': 'buy',
                'amount': amount,
                'params': {
                    'timeInForce': 'IOC',
                    'reduceOnly': False,
                    'postOnly': False
                }
            }
            
            logger.info("⚠️  PLACING REAL ORDER - This will use real money!")
            logger.info("Press Ctrl+C within 5 seconds to cancel...")
            
            import time
            time.sleep(5)
            
            # Place the order
            order = self.exchange.create_order(**order_params)
            
            logger.info(f"✅ ORDER PLACED SUCCESSFULLY!")
            logger.info(f"   Order ID: {order['id']}")
            logger.info(f"   Status: {order.get('status', 'Unknown')}")
            logger.info(f"   Filled: {order.get('filled', 0)}")
            logger.info(f"   Cost: ${order.get('cost', 0):.2f}")
            
            # Wait a moment then close the position
            time.sleep(2)
            
            logger.info("🔄 Closing position...")
            close_order = self.exchange.create_order(
                symbol=symbol,
                type='market',
                side='sell',
                amount=amount,
                params={'reduceOnly': True}
            )
            
            logger.info(f"✅ Position closed: {close_order['id']}")
            
            return True
            
        except KeyboardInterrupt:
            logger.info("❌ Order cancelled by user")
            return False
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ Order failed: {error_msg}")
            
            if "TE_ERR_INCONSISTENT_POS_MODE" in error_msg:
                logger.error("🚨 POSITION MODE ERROR STILL EXISTS!")
                return False
            else:
                logger.error(f"🔍 Different error (may be normal): {error_msg}")
                return False
    
    def run_complete_test(self):
        """Run complete test suite."""
        logger.info("🚀 Testing Position Mode Fix")
        logger.info("="*60)
        logger.info("PURPOSE: Verify TE_ERR_INCONSISTENT_POS_MODE is resolved")
        logger.info("="*60)
        
        # Test 1: Check position mode status
        logger.info("\n🔬 TEST 1: Position Mode Status")
        if not self.check_position_mode_status():
            logger.error("❌ Position mode check failed")
            return False
        
        # Test 2: Test leverage setting
        logger.info("\n🔬 TEST 2: Leverage Setting")
        if not self.test_leverage_setting():
            logger.error("❌ Leverage setting still failing")
            return False
        
        # Test 3: Test actual order placement
        logger.info("\n🔬 TEST 3: Small Order Placement")
        user_input = input("\nDo you want to test with a real $105 BTC order? (y/N): ")
        if user_input.lower() == 'y':
            if not self.test_small_order_placement():
                logger.error("❌ Order placement failed")
                return False
        else:
            logger.info("⏭️  Skipping real order test")
        
        logger.info("\n" + "="*60)
        logger.info("🎉 POSITION MODE FIX VERIFICATION COMPLETE!")
        logger.info("✅ Your account is ready for TITAN bot integration")
        logger.info("="*60)
        
        return True

def main():
    """Main function."""
    logger.info("🧪 Position Mode Fix Tester")
    logger.info("Verifying that TE_ERR_INCONSISTENT_POS_MODE is resolved")
    
    tester = PositionModeFixTester()
    success = tester.run_complete_test()
    
    if success:
        logger.info("\n🚀 Ready to integrate with TITAN bot and AXON system!")
        logger.info("📋 Next steps:")
        logger.info("1. Set up TITAN bot with your personal account")
        logger.info("2. Connect to AXON signal sender")
        logger.info("3. Start live trading with proper risk management")
    else:
        logger.info("\n🔧 Some issues remain - please review the errors.")

if __name__ == "__main__":
    main()
