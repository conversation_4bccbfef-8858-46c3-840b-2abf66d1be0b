#!/usr/bin/env python3
"""
Debug Take Profit Order Structure
Compare working stop loss vs failing take profit to find the exact issue.
"""

import os
import sys
import asyncio
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_order_structure():
    """Debug the exact order structure differences."""
    service = PersonalPhemexService()
    
    print("🔍 DEBUGGING TAKE PROFIT ORDER STRUCTURE")
    print("="*60)
    
    # Get current price
    ticker = service.exchange.fetch_ticker('BTC/USDT:USDT')
    current_price = ticker['last']
    print(f"Current BTC Price: ${current_price:.1f}")
    
    # Create position
    print(f"\n📈 Creating test position...")
    entry_result = service.place_order("BTC", "buy", 0.001, order_type="market")
    
    if not entry_result.get('success'):
        print(f"❌ Entry failed: {entry_result.get('error')}")
        return
    
    print(f"✅ Entry placed: {entry_result['order_id']}")
    await asyncio.sleep(5)
    
    # Test 1: Working stop loss (for comparison)
    stop_price = current_price * 0.97
    print(f"\n✅ WORKING STOP LOSS (for comparison):")
    print(f"Target: ${stop_price:.1f}")
    
    try:
        stop_order = service.exchange.create_order(
            symbol='BTC/USDT:USDT',
            type='stopLimit',
            side='sell',
            amount=0.001,
            price=round(stop_price * 0.99, 1),  # Limit price
            params={
                'stopPx': round(stop_price, 1),
                'triggerDirection': 'down',
                'reduceOnly': True
            }
        )
        print(f"✅ Stop loss placed: {stop_order['id']}")
        print(f"   Type: stopLimit")
        print(f"   Price: {round(stop_price * 0.99, 1)}")
        print(f"   StopPx: {round(stop_price, 1)}")
        print(f"   TriggerDirection: down")
        print(f"   ReduceOnly: True")
        
    except Exception as e:
        print(f"❌ Stop loss failed: {str(e)}")
    
    # Test 2: Take profit using SAME structure as stop loss
    tp_price = current_price * 1.05
    print(f"\n🎯 TAKE PROFIT using STOP LOSS structure:")
    print(f"Target: ${tp_price:.1f}")
    
    try:
        # Use stopLimit but with 'up' direction (like stop loss but opposite)
        tp_order = service.exchange.create_order(
            symbol='BTC/USDT:USDT',
            type='stopLimit',  # Same type as working stop loss
            side='sell',
            amount=0.001,
            price=round(tp_price * 1.01, 1),  # Limit price slightly above
            params={
                'stopPx': round(tp_price, 1),
                'triggerDirection': 'up',  # Opposite of stop loss
                'reduceOnly': True
            }
        )
        print(f"✅ Take profit placed: {tp_order['id']}")
        print(f"   Type: stopLimit (same as stop loss)")
        print(f"   Price: {round(tp_price * 1.01, 1)}")
        print(f"   StopPx: {round(tp_price, 1)}")
        print(f"   TriggerDirection: up")
        print(f"   ReduceOnly: True")
        
    except Exception as e:
        print(f"❌ Take profit with stop structure failed: {str(e)}")
        
        # Test 3: Simple limit order with minimal params
        print(f"\n🎯 SIMPLE LIMIT ORDER (minimal params):")
        try:
            simple_order = service.exchange.create_order(
                symbol='BTC/USDT:USDT',
                type='limit',
                side='sell',
                amount=0.001,
                price=round(tp_price, 1)
                # No params at all
            )
            print(f"✅ Simple limit placed: {simple_order['id']}")
            
        except Exception as e:
            print(f"❌ Simple limit failed: {str(e)}")
            
            # Test 4: Check if it's a position issue
            print(f"\n🔍 CHECKING POSITION STATUS:")
            positions = service.get_open_positions()
            for pos in positions.get('positions', []):
                if pos['symbol'] == 'BTC/USDT:USDT' and pos.get('contracts', 0) != 0:
                    print(f"   Position Size: {pos.get('contracts', 0)}")
                    print(f"   Position Side: {pos.get('side', 'unknown')}")
                    print(f"   Entry Price: ${pos.get('entryPrice', 0):.1f}")
                    print(f"   Position Mode: {pos.get('info', {}).get('posMode', 'unknown')}")
            
            # Test 5: Try with exact position size
            print(f"\n🎯 USING EXACT POSITION SIZE:")
            try:
                # Get exact position size
                position_size = 0
                for pos in positions.get('positions', []):
                    if pos['symbol'] == 'BTC/USDT:USDT' and pos.get('contracts', 0) != 0:
                        position_size = abs(pos.get('contracts', 0))
                        break
                
                if position_size > 0:
                    exact_order = service.exchange.create_order(
                        symbol='BTC/USDT:USDT',
                        type='limit',
                        side='sell',
                        amount=position_size,  # Exact position size
                        price=round(tp_price, 1),
                        params={'reduceOnly': True}
                    )
                    print(f"✅ Exact size limit placed: {exact_order['id']}")
                else:
                    print(f"❌ No position found to match")
                    
            except Exception as e:
                print(f"❌ Exact size limit failed: {str(e)}")
                
                # Test 6: Check account balance and permissions
                print(f"\n🔍 CHECKING ACCOUNT STATUS:")
                try:
                    balance = service.get_account_balance()
                    print(f"   USDT Balance: ${balance.get('total_usdt', 0):.2f}")
                    print(f"   Free USDT: ${balance.get('free_usdt', 0):.2f}")
                    
                    # Check if we can place any sell order at all
                    print(f"\n🧪 TESTING BASIC SELL ORDER:")
                    test_order = service.exchange.create_order(
                        symbol='BTC/USDT:USDT',
                        type='market',
                        side='sell',
                        amount=0.0005,  # Half position
                        params={'reduceOnly': True}
                    )
                    print(f"✅ Basic sell order works: {test_order['id']}")
                    
                except Exception as e:
                    print(f"❌ Basic sell order failed: {str(e)}")
    
    # Check all open orders
    print(f"\n📋 CHECKING ALL OPEN ORDERS:")
    try:
        open_orders = service.exchange.fetch_open_orders('BTC/USDT:USDT')
        print(f"Total open orders: {len(open_orders)}")
        for order in open_orders:
            print(f"   {order['id']}: {order['type']} {order['side']} @ ${order.get('price', 'market')}")
    except Exception as e:
        print(f"Error checking orders: {e}")
    
    # Close position
    user_input = input("\nClose test position and cancel orders? (y/N): ")
    if user_input.lower() == 'y':
        # Cancel all orders first
        try:
            open_orders = service.exchange.fetch_open_orders('BTC/USDT:USDT')
            for order in open_orders:
                try:
                    service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
                    print(f"✅ Cancelled {order['id']}")
                except:
                    pass
        except:
            pass
        
        # Close position
        close_result = service.place_order("BTC", "sell", 0.001, order_type="market")
        if close_result.get('success'):
            print(f"✅ Position closed: {close_result['order_id']}")

if __name__ == "__main__":
    asyncio.run(debug_order_structure())
