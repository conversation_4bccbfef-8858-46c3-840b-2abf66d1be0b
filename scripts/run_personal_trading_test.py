#!/usr/bin/env python3
"""
Personal Trading Test Script
Tests trading algorithms with your personal Phemex account.
Uses original backtested thresholds for accurate testing.
"""

import os
import sys
import asyncio
import argparse
import logging
import csv
from datetime import datetime, timedelta
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService
from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/personal_trading_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PersonalTradingTest:
    """Personal trading test using your Phemex account."""
    
    def __init__(self):
        """Initialize the personal trading test."""
        logger.info("💰 Initializing Personal Trading Test")
        logger.info("   Account: Your personal Cashcoldgame Phemex")
        logger.info("   Purpose: Test trading algorithms safely")
        
        # Initialize models with original backtested parameters
        self.btc_model = TITAN2KModel(aggressive_mode=True)
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)
        
        # Ensure original backtested thresholds
        self.btc_model.trend_strength_threshold = 0.4
        self.ada_model.trend_strength_threshold = 0.4
        
        # Services
        self.market_data = MarketDataService()
        self.phemex_service = PersonalPhemexService()
        
        # Trading state
        self.open_positions = {}
        self.trades_file = "trades_personal.csv"
        
        logger.info("✅ Personal Trading Test initialized")
        logger.info(f"   BTC Threshold: {self.btc_model.trend_strength_threshold}")
        logger.info(f"   ADA Threshold: {self.ada_model.trend_strength_threshold}")
        logger.info(f"   Trades File: {self.trades_file}")
        
        # Initialize CSV file
        self.init_trades_csv()
    
    def init_trades_csv(self):
        """Initialize trades CSV file."""
        if not os.path.exists(self.trades_file):
            with open(self.trades_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'symbol', 'signal', 'price', 'quantity', 
                    'confidence', 'combined_trend', 'order_id', 'status', 'account'
                ])
            logger.info(f"📝 Created trades file: {self.trades_file}")
    
    def save_trade(self, trade_data: Dict[str, Any]):
        """Save trade to CSV file."""
        try:
            with open(self.trades_file, 'a', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    trade_data.get('timestamp', datetime.now().isoformat()),
                    trade_data.get('symbol', ''),
                    trade_data.get('signal', ''),
                    trade_data.get('price', 0),
                    trade_data.get('quantity', 0),
                    trade_data.get('confidence', 0),
                    trade_data.get('combined_trend', 0),
                    trade_data.get('order_id', ''),
                    trade_data.get('status', ''),
                    trade_data.get('account', 'PERSONAL')
                ])
            logger.info(f"📝 Saved trade to {self.trades_file}")
        except Exception as e:
            logger.error(f"Error saving trade: {str(e)}")
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get market data for analysis."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")
            
            if symbol == "BTC":
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                medium_df = await self.market_data.get_historical_data(symbol, "4h", start_date_str, end_date_str)
                lower_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                return {"daily": daily_df, "medium": medium_df, "lower": lower_df}
            else:  # ADA
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                hourly_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                minute15_df = await self.market_data.get_historical_data(symbol, "15m", start_date_str, end_date_str)
                return {"daily": daily_df, "hourly": hourly_df, "minute15": minute15_df}
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return None
    
    async def analyze_and_trade(self, symbol: str) -> Dict[str, Any]:
        """Analyze symbol and execute trade if signal generated."""
        try:
            logger.info(f"📊 Analyzing {symbol} with backtested algorithm")
            
            # Check if we already have an open position
            if symbol in self.open_positions:
                logger.info(f"⏸️ Already have open position for {symbol}, skipping")
                return {"status": "SKIPPED", "reason": f"Open position for {symbol}"}
            
            # Get market data
            data = await self.get_market_data(symbol)
            if not data:
                return {"error": "Failed to get market data"}
            
            # Process with appropriate model
            if symbol == "BTC":
                result_df = self.btc_model.process_data(data["daily"], data["medium"], data["lower"])
                model_name = "TITAN2K"
            else:  # ADA
                result_df = self.ada_model.process_data(data["daily"], data["hourly"], data["minute15"])
                model_name = "TITAN2K_TrendTuned"
            
            if result_df is None or result_df.empty:
                return {"error": "No analysis data generated"}
            
            latest = result_df.iloc[-1]
            current_price = latest.get('close', 0)
            signal = latest.get('signal', 'HOLD')
            confidence = latest.get('confidence', 0)
            combined_trend = latest.get('combined_trend', 0)
            threshold = 0.4
            
            logger.info(f"📈 {symbol} Analysis:")
            logger.info(f"   Price: ${current_price:.4f}")
            logger.info(f"   Combined Trend: {combined_trend:.3f}")
            logger.info(f"   Threshold: {threshold}")
            logger.info(f"   Signal: {signal}")
            logger.info(f"   Confidence: {confidence:.1%}")
            
            # Execute trade if signal generated
            if signal in ["BUY", "SELL"]:
                logger.info(f"🚀 SIGNAL DETECTED: {signal} {symbol}")
                
                # Execute trade
                trade_result = self.phemex_service.execute_trade(
                    symbol=symbol,
                    signal=signal,
                    price=current_price,
                    confidence=confidence
                )
                
                if trade_result.get("status") == "success":
                    logger.info(f"✅ TRADE EXECUTED: {signal} {symbol}")
                    
                    # Track open position
                    self.open_positions[symbol] = {
                        "signal": signal,
                        "price": current_price,
                        "quantity": trade_result.get("quantity"),
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Save trade
                    trade_data = {
                        "timestamp": datetime.now().isoformat(),
                        "symbol": symbol,
                        "signal": signal,
                        "price": current_price,
                        "quantity": trade_result.get("quantity"),
                        "confidence": confidence,
                        "combined_trend": combined_trend,
                        "order_id": trade_result.get("order_id"),
                        "status": "EXECUTED",
                        "account": "PERSONAL"
                    }
                    self.save_trade(trade_data)
                    
                    return {
                        "status": "TRADE_EXECUTED",
                        "symbol": symbol,
                        "signal": signal,
                        "price": current_price,
                        "quantity": trade_result.get("quantity"),
                        "order_id": trade_result.get("order_id"),
                        "confidence": confidence,
                        "combined_trend": combined_trend
                    }
                else:
                    logger.error(f"❌ Trade failed: {trade_result.get('error')}")
                    return {
                        "status": "TRADE_FAILED",
                        "symbol": symbol,
                        "signal": signal,
                        "error": trade_result.get("error")
                    }
            else:
                logger.info(f"📊 No signal: trend {combined_trend:.3f} below threshold {threshold}")
                return {
                    "status": "NO_SIGNAL",
                    "symbol": symbol,
                    "combined_trend": combined_trend,
                    "threshold": threshold,
                    "price": current_price
                }
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {str(e)}")
            return {"error": str(e)}
    
    async def check_account_status(self):
        """Check account balance and positions."""
        try:
            balance = self.phemex_service.get_account_balance()
            positions = self.phemex_service.get_open_positions()
            
            logger.info("💰 Account Status:")
            logger.info(f"   Free USDT: ${balance.get('free_usdt', 0):.2f}")
            logger.info(f"   Total USDT: ${balance.get('total_usdt', 0):.2f}")
            logger.info(f"   Open Positions: {positions.get('count', 0)}")
            
            return {"balance": balance, "positions": positions}
            
        except Exception as e:
            logger.error(f"Error checking account: {str(e)}")
            return {"error": str(e)}
    
    async def run_trading_cycle(self):
        """Run one complete trading cycle."""
        logger.info("🔄 Starting personal trading cycle")
        
        # Check account status
        await self.check_account_status()
        
        # Analyze and trade BTC
        btc_result = await self.analyze_and_trade("BTC")
        if btc_result.get("status") == "TRADE_EXECUTED":
            logger.info(f"✅ BTC trade executed")
        
        # Analyze and trade ADA
        ada_result = await self.analyze_and_trade("ADA")
        if ada_result.get("status") == "TRADE_EXECUTED":
            logger.info(f"✅ ADA trade executed")
        
        logger.info("✅ Personal trading cycle completed")
    
    async def run(self, interval_minutes: int = 30):
        """Run the personal trading test continuously."""
        logger.info("🚀 Starting Personal Trading Test")
        logger.info("="*60)
        logger.info("ACCOUNT: Your personal Cashcoldgame Phemex")
        logger.info("PURPOSE: Test trading algorithms safely")
        logger.info("THRESHOLDS: Original backtested (0.4)")
        logger.info(f"INTERVAL: Every {interval_minutes} minutes")
        logger.info("="*60)
        
        while True:
            try:
                await self.run_trading_cycle()
                logger.info(f"⏳ Waiting {interval_minutes} minutes until next cycle")
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in trading test: {str(e)}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying

async def main():
    """Main function to run Personal Trading Test."""
    parser = argparse.ArgumentParser(description="Personal Trading Test")
    parser.add_argument("--interval", type=int, default=30, help="Trading interval in minutes")
    parser.add_argument("--check-balance", action="store_true", help="Just check account balance and exit")
    args = parser.parse_args()
    
    # Create trading test
    test = PersonalTradingTest()
    
    if args.check_balance:
        logger.info("💰 Checking account balance...")
        await test.check_account_status()
        return
    
    logger.info("🚀 Starting Personal Trading Test")
    logger.info("="*60)
    logger.info("ACCOUNT: Your personal Cashcoldgame Phemex")
    logger.info("PURPOSE: Safe testing of trading algorithms")
    logger.info("THRESHOLDS: Original backtested parameters")
    logger.info(f"INTERVAL: Every {args.interval} minutes")
    logger.info("="*60)
    
    # Run trading test
    await test.run(args.interval)

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run personal trading test
    asyncio.run(main())
