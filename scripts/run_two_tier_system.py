#!/usr/bin/env python3
"""
Two-Tier Trading System Master Controller
Manages both TIER 1 (AXON Signals) and TIER 2 (Phemex Trading) services.
"""

import os
import sys
import asyncio
import argparse
import logging
import subprocess
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/two_tier_system.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TwoTierSystemController:
    """Master controller for the two-tier trading system."""
    
    def __init__(self):
        """Initialize the controller."""
        self.processes = {}
        self.running = False
        
    def start_tier1_axon_signals(self):
        """Start TIER 1: AXON Signal Service."""
        try:
            logger.info("🔍 Starting TIER 1: AXON Signal Service")
            
            cmd = [
                sys.executable,
                "scripts/run_tier1_axon_signals.py",
                "--interval", "5"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes["tier1_axon"] = process
            logger.info(f"✅ TIER 1 started with PID: {process.pid}")
            
        except Exception as e:
            logger.error(f"❌ Failed to start TIER 1: {str(e)}")
    
    def start_tier2_trading(self, account: str):
        """Start TIER 2: Phemex Trading Service."""
        try:
            logger.info(f"💰 Starting TIER 2: Phemex Trading ({account})")
            
            cmd = [
                sys.executable,
                "scripts/run_tier2_phemex_trading.py",
                "--account", account,
                "--interval", "30"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes[f"tier2_{account.lower()}"] = process
            logger.info(f"✅ TIER 2 ({account}) started with PID: {process.pid}")
            
        except Exception as e:
            logger.error(f"❌ Failed to start TIER 2 ({account}): {str(e)}")
    
    def check_processes(self):
        """Check if all processes are still running."""
        for name, process in self.processes.items():
            if process.poll() is not None:
                logger.warning(f"⚠️ Process {name} has stopped (exit code: {process.returncode})")
                return False
        return True
    
    def stop_all_processes(self):
        """Stop all running processes."""
        logger.info("🛑 Stopping all processes...")
        
        for name, process in self.processes.items():
            try:
                process.terminate()
                process.wait(timeout=10)
                logger.info(f"✅ Stopped {name}")
            except subprocess.TimeoutExpired:
                logger.warning(f"⚠️ Force killing {name}")
                process.kill()
            except Exception as e:
                logger.error(f"❌ Error stopping {name}: {str(e)}")
        
        self.processes.clear()
    
    def display_status(self):
        """Display system status."""
        logger.info("📊 TWO-TIER SYSTEM STATUS")
        logger.info("="*60)
        
        # TIER 1 Status
        tier1_status = "🟢 RUNNING" if "tier1_axon" in self.processes and self.processes["tier1_axon"].poll() is None else "🔴 STOPPED"
        logger.info(f"TIER 1 (AXON Signals): {tier1_status}")
        logger.info("  Purpose: Information Only - sends analysis to AXON AI")
        logger.info("  Thresholds: Original backtested (0.4)")
        logger.info("  Interval: Every 5 minutes")
        
        # TIER 2 Status
        logger.info("")
        for account in ["CALEB", "GUTHRIX"]:
            process_key = f"tier2_{account.lower()}"
            tier2_status = "🟢 RUNNING" if process_key in self.processes and self.processes[process_key].poll() is None else "🔴 STOPPED"
            logger.info(f"TIER 2 ({account}): {tier2_status}")
            logger.info(f"  Purpose: Live Trading on Phemex {account} account")
            logger.info("  Thresholds: IDENTICAL to backtested (0.4)")
            logger.info("  Interval: Every 30 minutes")
        
        logger.info("="*60)
    
    async def run(self, accounts: list):
        """Run the two-tier system."""
        logger.info("🚀 STARTING TWO-TIER TRADING SYSTEM")
        logger.info("="*80)
        logger.info("ARCHITECTURE:")
        logger.info("  TIER 1: AXON Signal Service (Information Only)")
        logger.info("    - Original backtested thresholds (0.4)")
        logger.info("    - Sends analysis to AXON AI every 5 minutes")
        logger.info("    - NO trade execution")
        logger.info("")
        logger.info("  TIER 2: Phemex Trading Service (Live Trading)")
        logger.info("    - IDENTICAL algorithms and thresholds as backtested")
        logger.info("    - Executes real trades on Phemex accounts")
        logger.info("    - Conservative position sizing")
        logger.info("="*80)
        
        self.running = True
        
        try:
            # Start TIER 1: AXON Signal Service
            self.start_tier1_axon_signals()
            await asyncio.sleep(5)  # Give it time to start
            
            # Start TIER 2: Phemex Trading Services
            for account in accounts:
                self.start_tier2_trading(account)
                await asyncio.sleep(5)  # Stagger starts
            
            # Monitor system
            while self.running:
                await asyncio.sleep(60)  # Check every minute
                
                if not self.check_processes():
                    logger.warning("⚠️ Some processes have stopped")
                
                # Display status every 10 minutes
                if datetime.now().minute % 10 == 0:
                    self.display_status()
                
        except KeyboardInterrupt:
            logger.info("🛑 Received shutdown signal")
        except Exception as e:
            logger.error(f"❌ System error: {str(e)}")
        finally:
            self.running = False
            self.stop_all_processes()
            logger.info("✅ Two-tier system shutdown complete")

async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Two-Tier Trading System Controller")
    parser.add_argument(
        "--accounts", 
        nargs="+", 
        choices=["CALEB", "GUTHRIX"], 
        default=["CALEB", "GUTHRIX"],
        help="Trading accounts to activate"
    )
    parser.add_argument(
        "--tier1-only", 
        action="store_true",
        help="Run only TIER 1 (AXON signals) without trading"
    )
    args = parser.parse_args()
    
    # Create controller
    controller = TwoTierSystemController()
    
    # Determine which accounts to run
    accounts = [] if args.tier1_only else args.accounts
    
    if args.tier1_only:
        logger.info("🔍 Running TIER 1 ONLY (Information Only)")
    else:
        logger.info(f"🚀 Running FULL TWO-TIER SYSTEM with accounts: {', '.join(accounts)}")
    
    # Run system
    await controller.run(accounts)

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run system
    asyncio.run(main())
