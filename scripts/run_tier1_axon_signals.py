#!/usr/bin/env python3
"""
TIER 1: AXON Signal Service Runner
Runs the information-only signal service with original backtested thresholds.
Sends continuous analysis to AXON AI frontend without executing trades.
"""

import os
import sys
import asyncio
import argparse
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.axon_signal_service import AxonSignalService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/tier1_axon_signals.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """Main function to run TIER 1 AXON Signal Service."""
    parser = argparse.ArgumentParser(description="TIER 1: AXON Signal Service (Information Only)")
    parser.add_argument("--interval", type=int, default=5, help="Analysis interval in minutes")
    args = parser.parse_args()
    
    logger.info("🚀 Starting TIER 1: AXON Signal Service")
    logger.info("="*60)
    logger.info("PURPOSE: Information Only - NO trade execution")
    logger.info("THRESHOLDS: Original backtested parameters (0.4)")
    logger.info("DESTINATION: AXON AI frontend for monitoring")
    logger.info("INTERVAL: Every 5 minutes")
    logger.info("="*60)
    
    # Create and run AXON signal service
    service = AxonSignalService()
    await service.run(args.interval)

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run TIER 1 service
    asyncio.run(main())
