#!/usr/bin/env python3
"""
Comprehensive Phemex Position Mode Fix
Resolves TE_ERR_INCONSISTENT_POS_MODE error by properly configuring account settings.
"""

import os
import sys
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PhemexPositionModeFixer:
    """Fix Phemex position mode issues for successful trading."""
    
    def __init__(self):
        """Initialize the fixer."""
        self.service = PersonalPhemexService()
        self.exchange = self.service.exchange
        
    def check_current_position_mode(self):
        """Check current position mode settings."""
        logger.info("🔍 Checking current position mode settings")
        
        try:
            # Method 1: Check via positions
            positions = self.exchange.fetch_positions()
            logger.info(f"Found {len(positions)} position entries")
            
            # Look for BTC and ADA position info
            for pos in positions:
                if pos['symbol'] in ['BTC/USDT:USDT', 'ADA/USDT:USDT']:
                    logger.info(f"📊 {pos['symbol']} position info:")
                    logger.info(f"   Side: {pos.get('side', 'N/A')}")
                    logger.info(f"   Size: {pos.get('size', 0)}")
                    logger.info(f"   Contracts: {pos.get('contracts', 0)}")
                    
                    # Check raw info for position mode details
                    info = pos.get('info', {})
                    if info:
                        logger.info(f"   Raw info: {info}")
                        
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking position mode: {str(e)}")
            return False
    
    def set_position_mode_one_way(self):
        """Set position mode to One-Way (non-hedged) for both symbols."""
        logger.info("🔧 Setting position mode to One-Way (non-hedged)")
        
        symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        success_count = 0
        
        for symbol in symbols:
            try:
                logger.info(f"Setting {symbol} to One-Way mode...")
                result = self.exchange.set_position_mode(hedged=False, symbol=symbol)
                logger.info(f"✅ {symbol} position mode set: {result}")
                success_count += 1
                
            except Exception as e:
                error_msg = str(e)
                if "already" in error_msg.lower() or "same" in error_msg.lower():
                    logger.info(f"✅ {symbol} already in One-Way mode")
                    success_count += 1
                else:
                    logger.error(f"❌ Failed to set {symbol} position mode: {error_msg}")
        
        return success_count == len(symbols)
    
    def set_margin_mode_cross(self):
        """Set margin mode to Cross for both symbols."""
        logger.info("🔧 Setting margin mode to Cross")
        
        symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
        success_count = 0
        
        for symbol in symbols:
            try:
                logger.info(f"Setting {symbol} to Cross margin...")
                result = self.exchange.set_margin_mode('cross', symbol)
                logger.info(f"✅ {symbol} margin mode set: {result}")
                success_count += 1
                
            except Exception as e:
                error_msg = str(e)
                if "already" in error_msg.lower() or "same" in error_msg.lower():
                    logger.info(f"✅ {symbol} already in Cross margin mode")
                    success_count += 1
                else:
                    logger.error(f"❌ Failed to set {symbol} margin mode: {error_msg}")
        
        return success_count == len(symbols)
    
    def test_minimal_order(self, symbol: str, amount: float):
        """Test a minimal order to verify position mode is working."""
        logger.info(f"🧪 Testing minimal {symbol} order")
        
        try:
            # Get current price
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # Calculate order value
            order_value = amount * current_price
            logger.info(f"Order details: {amount} {symbol} at ${current_price:.2f} = ${order_value:.2f}")
            
            # Create order parameters matching manual trades
            order_params = {
                'symbol': symbol,
                'type': 'market',
                'side': 'buy',
                'amount': amount,
                'params': {
                    'timeInForce': 'IOC',  # Immediate or Cancel
                    'reduceOnly': False,
                    'postOnly': False
                }
            }
            
            logger.info(f"Order parameters: {order_params}")
            
            # Place the order
            logger.info("⚠️  PLACING REAL ORDER - This will use real money!")
            order = self.exchange.create_order(**order_params)
            
            logger.info(f"✅ Order placed successfully: {order['id']}")
            logger.info(f"   Status: {order.get('status', 'Unknown')}")
            logger.info(f"   Filled: {order.get('filled', 0)}")
            
            return True
            
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ Order failed: {error_msg}")
            
            # Check if it's still the position mode error
            if "TE_ERR_INCONSISTENT_POS_MODE" in error_msg:
                logger.error("🚨 POSITION MODE ERROR STILL EXISTS!")
                return False
            elif "TE_CANNOT_COVER_ESTIMATE_ORDER_LOSS" in error_msg:
                logger.error("💰 INSUFFICIENT MARGIN - Reduce position size")
                return False
            elif "TE_PLACE_ORDER_INSUFFICIENT_QUOTE_BALANCE" in error_msg:
                logger.error("💸 INSUFFICIENT BALANCE")
                return False
            else:
                logger.error(f"🔍 Unknown error: {error_msg}")
                return False
    
    def run_complete_fix(self):
        """Run the complete position mode fix process."""
        logger.info("🚀 Starting Comprehensive Phemex Position Mode Fix")
        logger.info("="*60)
        
        # Step 1: Check current settings
        logger.info("STEP 1: Checking current position mode")
        self.check_current_position_mode()
        
        # Step 2: Set position mode to One-Way
        logger.info("\nSTEP 2: Setting position mode to One-Way")
        if not self.set_position_mode_one_way():
            logger.error("❌ Failed to set position mode")
            return False
        
        # Step 3: Set margin mode to Cross
        logger.info("\nSTEP 3: Setting margin mode to Cross")
        if not self.set_margin_mode_cross():
            logger.error("❌ Failed to set margin mode")
            return False
        
        # Step 4: Test with minimal BTC order
        logger.info("\nSTEP 4: Testing with minimal BTC order")
        if not self.test_minimal_order('BTC/USDT:USDT', 0.001):
            logger.error("❌ BTC test order failed")
            return False
        
        # Step 5: Test with minimal ADA order
        logger.info("\nSTEP 5: Testing with minimal ADA order")
        if not self.test_minimal_order('ADA/USDT:USDT', 10.0):
            logger.error("❌ ADA test order failed")
            return False
        
        logger.info("\n" + "="*60)
        logger.info("🎉 POSITION MODE FIX COMPLETED SUCCESSFULLY!")
        logger.info("✅ Your account is now ready for TITAN bot trading")
        logger.info("="*60)
        
        return True

def main():
    """Main function."""
    logger.info("🔧 Phemex Position Mode Fixer")
    logger.info("Purpose: Fix TE_ERR_INCONSISTENT_POS_MODE error")
    logger.info("Account: Personal Cashcoldgame Phemex account")
    
    fixer = PhemexPositionModeFixer()
    success = fixer.run_complete_fix()
    
    if success:
        logger.info("\n🚀 Ready to integrate with TITAN bot and AXON system!")
    else:
        logger.info("\n🔧 Please review the errors and try again.")

if __name__ == "__main__":
    main()
