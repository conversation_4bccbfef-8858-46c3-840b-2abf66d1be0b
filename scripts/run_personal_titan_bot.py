#!/usr/bin/env python3
"""
Personal TITAN Trading Bot
Executes TITAN2K (BTC) and Trend-Tuned (ADA) algorithms on personal Phemex account.
"""

import os
import sys
import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService
from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/personal_titan_bot.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PersonalTitanBot:
    """Personal TITAN trading bot for Phemex account."""
    
    def __init__(self):
        """Initialize the trading bot."""
        self.phemex_service = PersonalPhemexService()
        self.market_data_service = MarketDataService()
        
        # Initialize models with aggressive settings (matching backtesting)
        self.btc_model = TITAN2KModel(aggressive_mode=True)
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)
        
        # Trading configuration
        self.config = {
            "btc": {
                "symbol": "BTC/USDT:USDT",
                "risk_percent": 0.005,  # 0.5% of account per trade
                "confidence_threshold": 0.4,  # Original backtested threshold
                "timeframes": ["1d", "4h", "1h"],  # TITAN2K timeframes
                "atr_stop_multiplier": 2.5,
                "take_profit_ratios": [1.5, 2.5, 4.0]
            },
            "ada": {
                "symbol": "ADA/USDT:USDT", 
                "risk_percent": 0.003,  # 0.3% of account per trade
                "confidence_threshold": 0.4,  # Original backtested threshold
                "timeframes": ["1d", "1h", "15m"],  # Trend-Tuned timeframes
                "atr_stop_multiplier": 2.0,
                "take_profit_ratios": [1.2, 2.0, 3.5]
            }
        }
        
        # State tracking
        self.active_positions = {}
        self.last_signals = {}
        self.daily_stats = {
            "trades_executed": 0,
            "signals_generated": 0,
            "pnl": 0.0,
            "start_balance": 0.0
        }
        
    async def get_market_data(self, symbol: str, timeframes: list) -> Dict[str, Any]:
        """Get market data for all required timeframes."""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
            
            data = {}
            for timeframe in timeframes:
                df = await self.market_data_service.get_ohlcv_data(
                    symbol=symbol.replace("/USDT:USDT", ""),
                    timeframe=timeframe,
                    start_date=start_date,
                    end_date=end_date
                )
                if not df.empty:
                    data[timeframe] = df
                else:
                    logger.error(f"No data received for {symbol} {timeframe}")
                    return None
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return None
    
    async def analyze_btc(self) -> Optional[Dict[str, Any]]:
        """Analyze BTC using TITAN2K model."""
        try:
            logger.info("🔍 Analyzing BTC with TITAN2K model")
            
            # Get market data
            data = await self.get_market_data("BTC", self.config["btc"]["timeframes"])
            if not data:
                return None
            
            # Process with TITAN2K model
            result_df = self.btc_model.process_data(
                data["1d"], data["4h"], data["1h"]
            )
            
            if result_df is None or result_df.empty:
                logger.warning("No BTC analysis data generated")
                return None
            
            # Get latest signal
            latest = result_df.iloc[-1]
            
            signal = {
                "timestamp": datetime.now().isoformat(),
                "symbol": "BTC",
                "phemex_symbol": "BTC/USDT:USDT",
                "action": latest.get('signal', 'HOLD'),
                "confidence": latest.get('combined_trend', 0.0),
                "entry_price": latest.get('close', 0.0),
                "atr": latest.get('atr14', 0.0),
                "timeframe_alignment": latest.get('timeframe_alignment', 0.0),
                "model": "TITAN2K",
                "reasoning": f"Trend strength: {latest.get('combined_trend', 0.0):.3f}, "
                           f"Alignment: {latest.get('timeframe_alignment', 0.0):.3f}"
            }
            
            # Calculate stop loss and take profit
            if signal['action'] in ['BUY', 'SELL']:
                signal.update(self.calculate_risk_levels(signal, "btc"))
            
            logger.info(f"BTC Signal: {signal['action']} (Confidence: {signal['confidence']:.3f})")
            return signal
            
        except Exception as e:
            logger.error(f"Error analyzing BTC: {str(e)}")
            return None
    
    async def analyze_ada(self) -> Optional[Dict[str, Any]]:
        """Analyze ADA using Trend-Tuned model."""
        try:
            logger.info("🔍 Analyzing ADA with Trend-Tuned model")
            
            # Get market data
            data = await self.get_market_data("ADA", self.config["ada"]["timeframes"])
            if not data:
                return None
            
            # Process with Trend-Tuned model
            result_df = self.ada_model.process_data(
                data["1d"], data["1h"], data["15m"]
            )
            
            if result_df is None or result_df.empty:
                logger.warning("No ADA analysis data generated")
                return None
            
            # Get latest signal
            latest = result_df.iloc[-1]
            
            signal = {
                "timestamp": datetime.now().isoformat(),
                "symbol": "ADA",
                "phemex_symbol": "ADA/USDT:USDT",
                "action": latest.get('signal', 'HOLD'),
                "confidence": latest.get('combined_trend', 0.0),
                "entry_price": latest.get('close', 0.0),
                "atr": latest.get('atr14', 0.0),
                "timeframe_alignment": latest.get('timeframe_alignment', 0.0),
                "model": "TITAN2K_TrendTuned",
                "reasoning": f"Trend strength: {latest.get('combined_trend', 0.0):.3f}, "
                           f"Alignment: {latest.get('timeframe_alignment', 0.0):.3f}"
            }
            
            # Calculate stop loss and take profit
            if signal['action'] in ['BUY', 'SELL']:
                signal.update(self.calculate_risk_levels(signal, "ada"))
            
            logger.info(f"ADA Signal: {signal['action']} (Confidence: {signal['confidence']:.3f})")
            return signal
            
        except Exception as e:
            logger.error(f"Error analyzing ADA: {str(e)}")
            return None
    
    def calculate_risk_levels(self, signal: Dict[str, Any], asset: str) -> Dict[str, Any]:
        """Calculate stop loss and take profit levels."""
        config = self.config[asset]
        entry_price = signal['entry_price']
        atr = signal['atr']
        
        # Calculate stop loss
        if signal['action'] == 'BUY':
            stop_loss = entry_price - (atr * config['atr_stop_multiplier'])
        else:  # SELL
            stop_loss = entry_price + (atr * config['atr_stop_multiplier'])
        
        # Calculate take profit levels
        risk_amount = abs(entry_price - stop_loss)
        take_profits = []
        
        for ratio in config['take_profit_ratios']:
            if signal['action'] == 'BUY':
                tp = entry_price + (risk_amount * ratio)
            else:  # SELL
                tp = entry_price - (risk_amount * ratio)
            take_profits.append(tp)
        
        # Calculate position size
        account_balance = self.phemex_service.get_account_balance()['total_usdt']
        risk_amount_usd = account_balance * config['risk_percent']
        position_size = (risk_amount_usd / risk_amount) * 10  # 10x leverage
        
        return {
            "stop_loss": stop_loss,
            "take_profit_1": take_profits[0],
            "take_profit_2": take_profits[1], 
            "take_profit_3": take_profits[2],
            "position_size": position_size,
            "risk_reward_ratio": config['take_profit_ratios'][0]
        }
    
    def validate_signal(self, signal: Dict[str, Any]) -> bool:
        """Validate signal before execution."""
        if not signal or signal.get('action') == 'HOLD':
            return False
        
        # Check confidence threshold
        asset = signal['symbol'].lower()
        if signal['confidence'] < self.config[asset]['confidence_threshold']:
            logger.info(f"{signal['symbol']} signal below confidence threshold: "
                       f"{signal['confidence']:.3f} < {self.config[asset]['confidence_threshold']}")
            return False
        
        # Check timeframe alignment
        if signal.get('timeframe_alignment', 0) < 0.6:
            logger.info(f"{signal['symbol']} insufficient timeframe alignment: "
                       f"{signal.get('timeframe_alignment', 0):.3f}")
            return False
        
        # Check for existing position
        if signal['phemex_symbol'] in self.active_positions:
            logger.info(f"{signal['symbol']} already has active position")
            return False
        
        # Check account balance
        balance = self.phemex_service.get_account_balance()
        if balance['free_usdt'] < 50:  # Minimum $50 for trading
            logger.warning(f"Insufficient balance: ${balance['free_usdt']:.2f}")
            return False
        
        return True
    
    async def execute_trade(self, signal: Dict[str, Any]) -> bool:
        """Execute trade based on signal."""
        try:
            logger.info(f"🚀 Executing {signal['symbol']} {signal['action']} trade")
            
            # Place entry order
            order_result = self.phemex_service.place_order(
                symbol=signal['symbol'],
                side=signal['action'].lower(),
                amount=signal['position_size'],
                order_type="market"
            )
            
            if order_result.get('success'):
                logger.info(f"✅ {signal['symbol']} entry order placed: {order_result['order_id']}")
                
                # Track position
                self.active_positions[signal['phemex_symbol']] = {
                    "signal": signal,
                    "entry_order": order_result,
                    "entry_time": datetime.now(),
                    "stop_loss_order": None,
                    "take_profit_orders": []
                }
                
                # Place risk management orders
                await self.place_risk_management_orders(signal)
                
                # Update stats
                self.daily_stats['trades_executed'] += 1
                
                return True
            else:
                logger.error(f"❌ {signal['symbol']} entry order failed: {order_result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing {signal['symbol']} trade: {str(e)}")
            return False
    
    async def place_risk_management_orders(self, signal: Dict[str, Any]):
        """Place stop loss and take profit orders."""
        try:
            # Place stop loss
            stop_order = self.phemex_service.place_stop_loss_order(
                symbol=signal['symbol'],
                side='sell' if signal['action'] == 'BUY' else 'buy',
                amount=signal['position_size'],
                stop_price=signal['stop_loss']
            )
            
            if stop_order.get('success'):
                logger.info(f"✅ {signal['symbol']} stop loss placed at ${signal['stop_loss']:.2f}")
                self.active_positions[signal['phemex_symbol']]['stop_loss_order'] = stop_order
            
            # Place take profit orders
            tp_orders = []
            remaining_size = signal['position_size']
            tp_percentages = [0.33, 0.50, 1.00]  # 33%, 50%, remaining
            
            for i, (tp_price, tp_percent) in enumerate(zip(
                [signal['take_profit_1'], signal['take_profit_2'], signal['take_profit_3']], 
                tp_percentages
            )):
                if i == 2:  # Last TP takes remaining size
                    tp_size = remaining_size
                else:
                    tp_size = signal['position_size'] * tp_percent
                    remaining_size -= tp_size
                
                tp_order = self.phemex_service.place_take_profit_order(
                    symbol=signal['symbol'],
                    side='sell' if signal['action'] == 'BUY' else 'buy',
                    amount=tp_size,
                    price=tp_price
                )
                
                if tp_order.get('success'):
                    logger.info(f"✅ {signal['symbol']} TP{i+1} placed at ${tp_price:.2f}")
                    tp_orders.append(tp_order)
            
            self.active_positions[signal['phemex_symbol']]['take_profit_orders'] = tp_orders
            
        except Exception as e:
            logger.error(f"Error placing risk management orders: {str(e)}")
    
    async def monitor_positions(self):
        """Monitor and update active positions."""
        try:
            if not self.active_positions:
                return
            
            logger.info(f"📊 Monitoring {len(self.active_positions)} active positions")
            
            for symbol, position in list(self.active_positions.items()):
                # Check position status
                current_positions = self.phemex_service.get_open_positions()
                
                # Update position if still active
                if any(pos['symbol'] == symbol for pos in current_positions.get('positions', [])):
                    # Position still active - could update trailing stops here
                    pass
                else:
                    # Position closed - remove from tracking
                    logger.info(f"📈 {symbol} position closed")
                    del self.active_positions[symbol]
            
        except Exception as e:
            logger.error(f"Error monitoring positions: {str(e)}")
    
    async def run_trading_cycle(self):
        """Run one complete trading cycle."""
        try:
            logger.info("🔄 Starting trading cycle")
            
            # Update daily stats
            if self.daily_stats['start_balance'] == 0:
                balance = self.phemex_service.get_account_balance()
                self.daily_stats['start_balance'] = balance['total_usdt']
            
            # Analyze BTC
            btc_signal = await self.analyze_btc()
            if btc_signal:
                self.daily_stats['signals_generated'] += 1
                self.last_signals['BTC'] = btc_signal
                
                if self.validate_signal(btc_signal):
                    await self.execute_trade(btc_signal)
            
            # Analyze ADA
            ada_signal = await self.analyze_ada()
            if ada_signal:
                self.daily_stats['signals_generated'] += 1
                self.last_signals['ADA'] = ada_signal
                
                if self.validate_signal(ada_signal):
                    await self.execute_trade(ada_signal)
            
            # Monitor existing positions
            await self.monitor_positions()
            
            # Log cycle summary
            logger.info(f"✅ Trading cycle completed - "
                       f"Signals: {self.daily_stats['signals_generated']}, "
                       f"Trades: {self.daily_stats['trades_executed']}, "
                       f"Active Positions: {len(self.active_positions)}")
            
        except Exception as e:
            logger.error(f"Error in trading cycle: {str(e)}")
    
    async def run(self, interval_minutes: int = 5):
        """Run the trading bot continuously."""
        logger.info("🚀 Starting Personal TITAN Trading Bot")
        logger.info("="*60)
        logger.info("MODELS: TITAN2K (BTC) + Trend-Tuned (ADA)")
        logger.info("ACCOUNT: Personal Cashcoldgame Phemex")
        logger.info("TRADING: ENABLED - Real money trades")
        logger.info(f"INTERVAL: Every {interval_minutes} minutes")
        logger.info("THRESHOLDS: Original backtested (0.4)")
        logger.info("="*60)
        
        while True:
            try:
                await self.run_trading_cycle()
                logger.info(f"⏳ Waiting {interval_minutes} minutes until next cycle")
                await asyncio.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                logger.info("🛑 Bot stopped by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {str(e)}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

async def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Personal TITAN Trading Bot")
    parser.add_argument("--interval", type=int, default=5, help="Trading cycle interval in minutes")
    parser.add_argument("--test", action="store_true", help="Run in test mode (no real trades)")
    args = parser.parse_args()
    
    if args.test:
        logger.info("🧪 Running in TEST MODE - No real trades will be executed")
    
    bot = PersonalTitanBot()
    await bot.run(args.interval)

if __name__ == "__main__":
    asyncio.run(main())
