#!/usr/bin/env python3
"""
TIER 2: Phemex Trading Service Runner
Runs live trading with IDENTICAL backtested algorithms and thresholds.
Executes actual trades on Phemex accounts when signals are generated.
"""

import os
import sys
import asyncio
import argparse
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService
from services.caleb_phemex_service import CalebPhemexService
from services.guthrix_phemex_service import GuthrixPhemexService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/tier2_phemex_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PhemexTradingService:
    """TIER 2: Live trading service using IDENTICAL backtested algorithms."""
    
    def __init__(self, account_type: str):
        """Initialize the Phemex trading service."""
        logger.info(f"💰 Initializing TIER 2: Phemex Trading Service")
        logger.info(f"   Account: {account_type}")
        
        self.account_type = account_type
        
        # Initialize models with IDENTICAL backtested parameters
        self.btc_model = TITAN2KModel(aggressive_mode=True)
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)
        
        # CRITICAL: Restore original backtested thresholds
        self.btc_model.trend_strength_threshold = 0.4
        self.ada_model.trend_strength_threshold = 0.4
        
        # Market data service
        self.market_data = MarketDataService()
        
        # Initialize Phemex service
        if account_type == "CALEB":
            self.phemex_service = CalebPhemexService()
        elif account_type == "GUTHRIX":
            self.phemex_service = GuthrixPhemexService()
        else:
            raise ValueError(f"Invalid account type: {account_type}")
        
        logger.info("✅ TIER 2 initialized with EXACT backtested parameters")
        logger.info(f"   BTC Threshold: {self.btc_model.trend_strength_threshold}")
        logger.info(f"   ADA Threshold: {self.ada_model.trend_strength_threshold}")
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get market data - IDENTICAL to TIER 1."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")
            
            if symbol == "BTC":
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                medium_df = await self.market_data.get_historical_data(symbol, "4h", start_date_str, end_date_str)
                lower_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                return {"daily": daily_df, "medium": medium_df, "lower": lower_df}
            else:  # ADA
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                hourly_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                minute15_df = await self.market_data.get_historical_data(symbol, "15m", start_date_str, end_date_str)
                return {"daily": daily_df, "hourly": hourly_df, "minute15": minute15_df}
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return None
    
    async def calculate_safe_position_size(self, symbol: str) -> float:
        """Calculate ultra-conservative position size to avoid Phemex errors."""
        try:
            balance = self.phemex_service.exchange.fetch_balance()
            free_balance = balance['free']['USDT']
            
            # Ultra-conservative: 0.2% risk, no leverage
            risk_percent = 0.002
            
            if symbol == "BTC":
                ticker = self.phemex_service.exchange.fetch_ticker('BTC/USDT')
            else:
                ticker = self.phemex_service.exchange.fetch_ticker('ADA/USDT')
            
            current_price = ticker['last']
            position_value = free_balance * risk_percent
            quantity = position_value / current_price
            
            # Minimum amounts
            if symbol == "ADA" and quantity < 2:
                quantity = 2
            elif symbol == "BTC" and quantity < 0.00002:
                quantity = 0.00002
            
            logger.info(f"💰 Ultra-safe position: {quantity:.6f} {symbol} (${position_value:.2f})")
            return quantity
            
        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return 0.00002 if symbol == "BTC" else 2
    
    async def analyze_and_trade(self, symbol: str) -> Dict[str, Any]:
        """Analyze and execute trades using IDENTICAL backtested algorithms."""
        try:
            logger.info(f"📊 Analyzing {symbol} with backtested algorithm (threshold: 0.4)")
            
            # Get market data
            data = await self.get_market_data(symbol)
            if not data:
                return {"error": "Failed to get market data"}
            
            # Process with appropriate model
            if symbol == "BTC":
                result_df = self.btc_model.process_data(data["daily"], data["medium"], data["lower"])
            else:  # ADA
                result_df = self.ada_model.process_data(data["daily"], data["hourly"], data["minute15"])
            
            if result_df is None or result_df.empty:
                return {"error": "No analysis data generated"}
            
            latest = result_df.iloc[-1]
            current_price = latest.get('close', 0)
            signal = latest.get('signal', 'HOLD')
            confidence = latest.get('confidence', 0)
            combined_trend = latest.get('combined_trend', 0)
            
            logger.info(f"📈 {symbol} Analysis:")
            logger.info(f"   Price: ${current_price:.4f}")
            logger.info(f"   Combined Trend: {combined_trend:.3f}")
            logger.info(f"   Threshold: 0.4")
            logger.info(f"   Signal: {signal}")
            logger.info(f"   Confidence: {confidence:.1%}")
            
            # Execute trade if signal generated
            if signal in ["BUY", "SELL"]:
                logger.info(f"🚀 BACKTESTED SIGNAL DETECTED: {signal} {symbol}")
                
                # Calculate position size
                quantity = await self.calculate_safe_position_size(symbol)
                
                # Execute trade using spot trading
                try:
                    symbol_pair = f"{symbol}/USDT"
                    
                    if signal == "BUY":
                        order = self.phemex_service.exchange.create_market_buy_order(
                            symbol=symbol_pair,
                            amount=quantity
                        )
                    else:  # SELL
                        order = self.phemex_service.exchange.create_market_sell_order(
                            symbol=symbol_pair,
                            amount=quantity
                        )
                    
                    if order and order.get("id"):
                        logger.info(f"✅ TRADE EXECUTED: {signal} {quantity:.6f} {symbol}")
                        logger.info(f"   Order ID: {order['id']}")
                        logger.info(f"   Price: ${current_price:.4f}")
                        
                        return {
                            "status": "TRADE_EXECUTED",
                            "signal": signal,
                            "quantity": quantity,
                            "price": current_price,
                            "order_id": order["id"],
                            "confidence": confidence,
                            "combined_trend": combined_trend
                        }
                    else:
                        logger.error(f"❌ Trade failed: No order ID returned")
                        return {"status": "TRADE_FAILED", "error": "No order ID"}
                        
                except Exception as e:
                    logger.error(f"❌ Trade execution error: {str(e)}")
                    return {"status": "TRADE_ERROR", "error": str(e)}
            else:
                logger.info(f"📊 No signal: trend {combined_trend:.3f} below threshold 0.4")
                return {
                    "status": "NO_SIGNAL",
                    "combined_trend": combined_trend,
                    "threshold": 0.4,
                    "price": current_price
                }
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {str(e)}")
            return {"error": str(e)}
    
    async def run_trading_cycle(self):
        """Run one complete trading cycle."""
        logger.info(f"🔄 Starting {self.account_type} trading cycle")
        
        # Analyze and trade BTC
        btc_result = await self.analyze_and_trade("BTC")
        if btc_result.get("status") == "TRADE_EXECUTED":
            logger.info(f"✅ BTC trade executed on {self.account_type}")
        
        # Analyze and trade ADA
        ada_result = await self.analyze_and_trade("ADA")
        if ada_result.get("status") == "TRADE_EXECUTED":
            logger.info(f"✅ ADA trade executed on {self.account_type}")
        
        logger.info(f"✅ {self.account_type} trading cycle completed")
    
    async def run(self, interval_minutes: int = 30):
        """Run the trading service continuously."""
        logger.info(f"🚀 Starting TIER 2: Phemex Trading Service")
        logger.info(f"   Account: {self.account_type}")
        logger.info(f"   Interval: {interval_minutes} minutes")
        logger.info(f"   Algorithms: IDENTICAL to backtested versions")
        
        while True:
            try:
                await self.run_trading_cycle()
                logger.info(f"⏳ Waiting {interval_minutes} minutes until next cycle")
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in trading service: {str(e)}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying

async def main():
    """Main function to run TIER 2 Phemex Trading Service."""
    parser = argparse.ArgumentParser(description="TIER 2: Phemex Trading Service (Live Trading)")
    parser.add_argument("--account", choices=["CALEB", "GUTHRIX"], required=True, help="Trading account")
    parser.add_argument("--interval", type=int, default=30, help="Trading interval in minutes")
    args = parser.parse_args()
    
    logger.info("🚀 Starting TIER 2: Phemex Trading Service")
    logger.info("="*60)
    logger.info("PURPOSE: Live Trading with backtested algorithms")
    logger.info("THRESHOLDS: IDENTICAL to backtested (0.4)")
    logger.info("EXECUTION: Real trades on Phemex")
    logger.info(f"ACCOUNT: {args.account}")
    logger.info(f"INTERVAL: Every {args.interval} minutes")
    logger.info("="*60)
    
    # Create and run trading service
    service = PhemexTradingService(args.account)
    await service.run(args.interval)

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run TIER 2 service
    asyncio.run(main())
