#!/usr/bin/env python3
"""
Investigate Take Profit Requirements
Analyze Phemex API requirements for take profit orders on USDT-settled perpetual contracts.
"""

import os
import sys
import asyncio
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def investigate_take_profit():
    """Investigate take profit order requirements."""
    service = PersonalPhemexService()
    
    print("🔍 INVESTIGATING TAKE PROFIT ORDER REQUIREMENTS")
    print("="*60)
    
    # Get current BTC price and market info
    ticker = service.exchange.fetch_ticker('BTC/USDT:USDT')
    current_price = ticker['last']
    
    markets = service.exchange.load_markets()
    btc_market = markets['BTC/USDT:USDT']
    
    print(f"Current BTC Price: ${current_price:.1f}")
    print(f"Market Type: {btc_market['type']}")
    print(f"Contract: {btc_market['contract']}")
    print(f"Settle: {btc_market['settle']}")
    print(f"Price Precision: {btc_market['precision']['price']}")
    print(f"Amount Precision: {btc_market['precision']['amount']}")
    print(f"Min Amount: {btc_market['limits']['amount']['min']}")
    print(f"Min Price: {btc_market['limits']['price']['min']}")
    print(f"Max Price: {btc_market['limits']['price']['max']}")
    
    # Check market info for order requirements
    info = btc_market['info']
    print(f"\n📋 MARKET INFO ANALYSIS:")
    for key, value in info.items():
        if any(keyword in key.lower() for keyword in ['price', 'qty', 'order', 'tick', 'step']):
            print(f"   {key}: {value}")
    
    # Create a small position first
    print(f"\n📈 STEP 1: Creating small BTC position")
    entry_result = service.place_order("BTC", "buy", 0.001, order_type="market")
    
    if not entry_result.get('success'):
        print(f"❌ Entry failed: {entry_result.get('error')}")
        return
    
    print(f"✅ Entry placed: {entry_result['order_id']}")
    await asyncio.sleep(5)
    
    # Test different take profit approaches
    target_prices = [
        current_price * 1.02,   # 2% above (close)
        current_price * 1.05,   # 5% above (medium)
        current_price * 1.10,   # 10% above (far)
    ]
    
    print(f"\n🎯 STEP 2: Testing different take profit approaches")
    
    for i, target_price in enumerate(target_prices, 1):
        print(f"\n--- Test {i}: Target ${target_price:.1f} ({((target_price/current_price-1)*100):+.1f}%) ---")
        
        # Test A: Basic limit order
        try:
            print("A. Testing basic limit order...")
            order = service.exchange.create_order(
                symbol='BTC/USDT:USDT',
                type='limit',
                side='sell',
                amount=0.001,
                price=round(target_price, 1),
                params={
                    'reduceOnly': True,
                    'timeInForce': 'GTC'
                }
            )
            print(f"✅ Basic limit order placed: {order['id']}")
            
            # Cancel it immediately
            await asyncio.sleep(1)
            service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
            print("   Order cancelled for next test")
            
        except Exception as e:
            print(f"❌ Basic limit failed: {str(e)}")
            
            # Test B: Limit order without reduceOnly
            try:
                print("B. Testing limit order without reduceOnly...")
                order = service.exchange.create_order(
                    symbol='BTC/USDT:USDT',
                    type='limit',
                    side='sell',
                    amount=0.001,
                    price=round(target_price, 1),
                    params={
                        'timeInForce': 'GTC'
                    }
                )
                print(f"✅ Limit without reduceOnly placed: {order['id']}")
                
                # Cancel it
                await asyncio.sleep(1)
                service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
                print("   Order cancelled for next test")
                
            except Exception as e:
                print(f"❌ Limit without reduceOnly failed: {str(e)}")
                
                # Test C: Different price precision
                try:
                    print("C. Testing different price precision...")
                    # Try with more decimal places
                    precise_price = round(target_price, 2)
                    order = service.exchange.create_order(
                        symbol='BTC/USDT:USDT',
                        type='limit',
                        side='sell',
                        amount=0.001,
                        price=precise_price,
                        params={
                            'reduceOnly': True,
                            'timeInForce': 'GTC'
                        }
                    )
                    print(f"✅ Precise price order placed: {order['id']}")
                    
                    # Cancel it
                    await asyncio.sleep(1)
                    service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
                    print("   Order cancelled for next test")
                    
                except Exception as e:
                    print(f"❌ Precise price failed: {str(e)}")
                    
                    # Test D: Take profit order type
                    try:
                        print("D. Testing takeProfitMarket order...")
                        order = service.exchange.create_order(
                            symbol='BTC/USDT:USDT',
                            type='takeProfitMarket',
                            side='sell',
                            amount=0.001,
                            params={
                                'stopPx': round(target_price, 1),
                                'triggerDirection': 'up',
                                'reduceOnly': True
                            }
                        )
                        print(f"✅ TakeProfitMarket order placed: {order['id']}")
                        
                        # Cancel it
                        await asyncio.sleep(1)
                        service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
                        print("   Order cancelled for next test")
                        
                    except Exception as e:
                        print(f"❌ TakeProfitMarket failed: {str(e)}")
                        
                        # Test E: Take profit limit order
                        try:
                            print("E. Testing takeProfitLimit order...")
                            order = service.exchange.create_order(
                                symbol='BTC/USDT:USDT',
                                type='takeProfitLimit',
                                side='sell',
                                amount=0.001,
                                price=round(target_price, 1),
                                params={
                                    'stopPx': round(target_price, 1),
                                    'triggerDirection': 'up',
                                    'reduceOnly': True
                                }
                            )
                            print(f"✅ TakeProfitLimit order placed: {order['id']}")
                            
                            # Cancel it
                            await asyncio.sleep(1)
                            service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
                            print("   Order cancelled for next test")
                            
                        except Exception as e:
                            print(f"❌ TakeProfitLimit failed: {str(e)}")
    
    # Check final position status
    print(f"\n📊 FINAL STATUS:")
    positions = service.get_open_positions()
    for pos in positions.get('positions', []):
        if pos['symbol'] == 'BTC/USDT:USDT' and pos.get('contracts', 0) != 0:
            print(f"Position: {pos.get('contracts', 0)} BTC")
            print(f"Entry: ${pos.get('entryPrice', 0):.1f}")
            print(f"PnL: ${pos.get('unrealizedPnl', 0):.2f}")
    
    # Close position
    user_input = input("\nClose test position? (y/N): ")
    if user_input.lower() == 'y':
        close_result = service.place_order("BTC", "sell", 0.001, order_type="market")
        if close_result.get('success'):
            print(f"✅ Position closed: {close_result['order_id']}")

if __name__ == "__main__":
    asyncio.run(investigate_take_profit())
