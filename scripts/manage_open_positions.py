#!/usr/bin/env python3
"""
Manage Open Positions
Check current positions and properly close them with stop losses and take profits.
"""

import os
import sys
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PositionManager:
    """Manage open positions and orders."""
    
    def __init__(self):
        """Initialize the position manager."""
        self.service = PersonalPhemexService()
        
    def check_current_positions(self):
        """Check all current open positions."""
        logger.info("🔍 Checking current open positions")
        
        try:
            positions = self.service.get_open_positions()
            open_positions = []
            
            logger.info("="*50)
            logger.info("📊 CURRENT OPEN POSITIONS")
            logger.info("="*50)
            
            for pos in positions.get('positions', []):
                # Use 'contracts' field instead of 'size'
                position_size = pos.get('contracts', 0)
                if position_size != 0:
                    open_positions.append(pos)

                    logger.info(f"🔸 {pos['symbol']}")
                    logger.info(f"   Side: {pos['side']}")
                    logger.info(f"   Size: {position_size}")
                    logger.info(f"   Entry Price: ${pos.get('entryPrice', 0):.4f}")
                    logger.info(f"   Mark Price: ${pos.get('markPrice', 0):.4f}")
                    logger.info(f"   PnL: ${pos.get('unrealizedPnl', 0):.2f}")
                    logger.info(f"   Value: ${abs(position_size) * pos.get('markPrice', 0):.2f}")
                    logger.info(f"   Stop Loss: {pos.get('stopLossPrice', 'None')}")
                    logger.info(f"   Take Profit: {pos.get('takeProfitPrice', 'None')}")
                    logger.info("   ---")
            
            if not open_positions:
                logger.info("✅ No open positions found")
            else:
                logger.info(f"📈 Found {len(open_positions)} open positions")
            
            return open_positions
            
        except Exception as e:
            logger.error(f"Error checking positions: {str(e)}")
            return []
    
    def check_open_orders(self):
        """Check all open orders."""
        logger.info("🔍 Checking open orders")
        
        try:
            logger.info("="*50)
            logger.info("📋 OPEN ORDERS")
            logger.info("="*50)
            
            symbols = ['BTC/USDT:USDT', 'ADA/USDT:USDT']
            all_orders = []
            
            for symbol in symbols:
                try:
                    orders = self.service.exchange.fetch_open_orders(symbol)
                    if orders:
                        logger.info(f"🔸 {symbol}: {len(orders)} open orders")
                        for order in orders:
                            all_orders.append(order)
                            logger.info(f"   Order ID: {order['id']}")
                            logger.info(f"   Type: {order['type']} {order['side']}")
                            logger.info(f"   Amount: {order['amount']}")
                            logger.info(f"   Price: ${order.get('price', 'market')}")
                            logger.info(f"   Status: {order['status']}")
                            logger.info("   ---")
                    else:
                        logger.info(f"✅ {symbol}: No open orders")
                        
                except Exception as e:
                    logger.warning(f"⚠️ Error checking {symbol} orders: {str(e)}")
            
            if not all_orders:
                logger.info("✅ No open orders found")
            
            return all_orders
            
        except Exception as e:
            logger.error(f"Error checking orders: {str(e)}")
            return []
    
    def place_stop_loss_and_take_profit(self, position):
        """Place stop loss and take profit for a position."""
        try:
            symbol = position['symbol'].replace('/USDT:USDT', '')
            side = position['side']
            size = abs(position.get('contracts', 0))  # Use contracts field
            entry_price = position.get('entryPrice', 0)
            current_price = position.get('markPrice', entry_price)
            
            logger.info(f"🛡️ Setting up risk management for {symbol}")
            logger.info(f"   Position: {side} {size} @ ${entry_price:.4f}")
            
            # Calculate stop loss and take profit
            if side == 'long':
                # Long position: stop below, profit above
                stop_loss_price = entry_price * 0.975  # 2.5% stop loss
                take_profit_price = entry_price * 1.0375  # 3.75% take profit
                stop_side = 'sell'
                tp_side = 'sell'
            else:
                # Short position: stop above, profit below
                stop_loss_price = entry_price * 1.025  # 2.5% stop loss
                take_profit_price = entry_price * 0.9625  # 3.75% take profit
                stop_side = 'buy'
                tp_side = 'buy'
            
            logger.info(f"   Stop Loss: ${stop_loss_price:.4f}")
            logger.info(f"   Take Profit: ${take_profit_price:.4f}")
            
            # Ask user for confirmation
            user_input = input(f"\nPlace stop loss and take profit for {symbol}? (y/N): ")
            if user_input.lower() == 'y':
                
                # Place stop loss
                stop_result = self.service.place_stop_loss_order(
                    symbol=symbol,
                    side=stop_side,
                    amount=size,
                    stop_price=stop_loss_price
                )
                
                if stop_result.get('success'):
                    logger.info(f"✅ Stop loss placed: {stop_result['order_id']}")
                else:
                    logger.error(f"❌ Stop loss failed: {stop_result.get('error')}")
                
                # Place take profit
                tp_result = self.service.place_take_profit_order(
                    symbol=symbol,
                    side=tp_side,
                    amount=size,
                    price=take_profit_price
                )
                
                if tp_result.get('success'):
                    logger.info(f"✅ Take profit placed: {tp_result['order_id']}")
                else:
                    logger.error(f"❌ Take profit failed: {tp_result.get('error')}")
                
                return True
            else:
                logger.info("⏭️ Skipped risk management setup")
                return False
                
        except Exception as e:
            logger.error(f"Error setting up risk management: {str(e)}")
            return False
    
    def close_position(self, position):
        """Close a position immediately."""
        try:
            symbol = position['symbol'].replace('/USDT:USDT', '')
            side = position['side']
            size = abs(position.get('contracts', 0))  # Use contracts field
            current_price = position.get('markPrice', 0)
            unrealized_pnl = position.get('unrealizedPnl', 0)
            
            logger.info(f"🔥 Closing {symbol} position")
            logger.info(f"   Position: {side} {size}")
            logger.info(f"   Current Price: ${current_price:.4f}")
            logger.info(f"   Unrealized PnL: ${unrealized_pnl:.2f}")
            
            # Determine close side
            close_side = 'sell' if side == 'long' else 'buy'
            
            # Ask user for confirmation
            user_input = input(f"\nClose {symbol} position? (y/N): ")
            if user_input.lower() == 'y':
                
                # Place market order to close
                close_result = self.service.place_order(
                    symbol=symbol,
                    side=close_side,
                    amount=size,
                    order_type="market"
                )
                
                if close_result.get('success'):
                    logger.info(f"✅ Position closed: {close_result['order_id']}")
                    logger.info(f"   Realized PnL: ~${unrealized_pnl:.2f}")
                    return True
                else:
                    logger.error(f"❌ Failed to close position: {close_result.get('error')}")
                    return False
            else:
                logger.info("⏭️ Position close cancelled")
                return False
                
        except Exception as e:
            logger.error(f"Error closing position: {str(e)}")
            return False
    
    def cancel_all_orders(self, symbol=None):
        """Cancel all open orders for a symbol or all symbols."""
        try:
            symbols = [symbol] if symbol else ['BTC/USDT:USDT', 'ADA/USDT:USDT']
            
            for sym in symbols:
                try:
                    orders = self.service.exchange.fetch_open_orders(sym)
                    if orders:
                        logger.info(f"🗑️ Cancelling {len(orders)} orders for {sym}")
                        
                        for order in orders:
                            try:
                                cancel_result = self.service.exchange.cancel_order(order['id'], sym)
                                logger.info(f"✅ Cancelled order: {order['id']}")
                            except Exception as e:
                                logger.warning(f"⚠️ Failed to cancel {order['id']}: {str(e)}")
                    else:
                        logger.info(f"✅ No orders to cancel for {sym}")
                        
                except Exception as e:
                    logger.warning(f"⚠️ Error checking orders for {sym}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error cancelling orders: {str(e)}")
    
    def run_position_management(self):
        """Run complete position management."""
        logger.info("🚀 Starting Position Management")
        logger.info("="*60)
        
        # Check current state
        positions = self.check_current_positions()
        orders = self.check_open_orders()
        
        if not positions and not orders:
            logger.info("✅ No open positions or orders - account is clean")
            return
        
        # Handle each position
        for position in positions:
            symbol = position['symbol'].replace('/USDT:USDT', '')
            
            logger.info(f"\n🔧 Managing {symbol} position")
            print("Options:")
            print("1. Place stop loss and take profit")
            print("2. Close position immediately")
            print("3. Leave as is")
            
            choice = input(f"Choose option for {symbol} (1/2/3): ")
            
            if choice == '1':
                self.place_stop_loss_and_take_profit(position)
            elif choice == '2':
                self.close_position(position)
            else:
                logger.info(f"⏭️ Leaving {symbol} position as is")
        
        # Handle orders
        if orders:
            user_input = input(f"\nCancel all {len(orders)} open orders? (y/N): ")
            if user_input.lower() == 'y':
                self.cancel_all_orders()
        
        logger.info("\n✅ Position management complete")

def main():
    """Main function."""
    manager = PositionManager()
    manager.run_position_management()

if __name__ == "__main__":
    main()
