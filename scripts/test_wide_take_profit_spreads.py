#!/usr/bin/env python3
"""
Test Wide Take Profit Spreads
Test take profit orders with much wider price spreads to find minimum requirements.
"""

import os
import sys
import asyncio
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_wide_spreads():
    """Test take profit with wide price spreads."""
    service = PersonalPhemexService()
    
    print("🎯 TESTING WIDE TAKE PROFIT SPREADS")
    print("="*60)
    
    # Get current price
    ticker = service.exchange.fetch_ticker('BTC/USDT:USDT')
    current_price = ticker['last']
    print(f"Current BTC Price: ${current_price:.1f}")
    
    # Create position
    print(f"\n📈 Creating small BTC position...")
    entry_result = service.place_order("BTC", "buy", 0.001, order_type="market")
    
    if not entry_result.get('success'):
        print(f"❌ Entry failed: {entry_result.get('error')}")
        return
    
    print(f"✅ Entry placed: {entry_result['order_id']}")
    await asyncio.sleep(5)
    
    # Test with very wide spreads
    wide_spreads = [
        (current_price * 1.15, "15%"),   # 15% above
        (current_price * 1.20, "20%"),   # 20% above
        (current_price * 1.30, "30%"),   # 30% above
        (current_price * 1.50, "50%"),   # 50% above
        (current_price * 2.00, "100%"),  # 100% above (double)
    ]
    
    successful_orders = []
    
    for target_price, spread_label in wide_spreads:
        print(f"\n--- Testing {spread_label} spread: ${target_price:.1f} ---")
        
        # Test basic limit order first
        try:
            print(f"Testing basic limit order at ${target_price:.1f}...")
            order = service.exchange.create_order(
                symbol='BTC/USDT:USDT',
                type='limit',
                side='sell',
                amount=0.001,
                price=round(target_price, 1),
                params={
                    'reduceOnly': True,
                    'timeInForce': 'GTC'
                }
            )
            print(f"✅ SUCCESS! Limit order placed: {order['id']}")
            successful_orders.append({
                'id': order['id'],
                'type': 'limit',
                'price': target_price,
                'spread': spread_label
            })
            
            # Don't cancel yet - let's see if we can place multiple
            
        except Exception as e:
            print(f"❌ Limit order failed: {str(e)}")
            
            # If basic limit fails, try takeProfitLimit
            try:
                print(f"Testing takeProfitLimit at ${target_price:.1f}...")
                order = service.exchange.create_order(
                    symbol='BTC/USDT:USDT',
                    type='takeProfitLimit',
                    side='sell',
                    amount=0.001,
                    price=round(target_price, 1),
                    params={
                        'stopPx': round(target_price, 1),
                        'triggerDirection': 'up',
                        'reduceOnly': True
                    }
                )
                print(f"✅ SUCCESS! TakeProfitLimit placed: {order['id']}")
                successful_orders.append({
                    'id': order['id'],
                    'type': 'takeProfitLimit',
                    'price': target_price,
                    'spread': spread_label
                })
                
            except Exception as e:
                print(f"❌ TakeProfitLimit failed: {str(e)}")
    
    # Show results
    print(f"\n📊 RESULTS SUMMARY:")
    print(f"Successful orders: {len(successful_orders)}")
    
    if successful_orders:
        print(f"\n✅ WORKING TAKE PROFIT CONFIGURATIONS:")
        for order in successful_orders:
            print(f"   {order['type']}: {order['spread']} spread (${order['price']:.1f})")
        
        # Check orders in account
        print(f"\n📋 Checking orders in account...")
        try:
            open_orders = service.exchange.fetch_open_orders('BTC/USDT:USDT')
            print(f"Open orders found: {len(open_orders)}")
            for order in open_orders:
                print(f"   {order['type']} {order['side']} @ ${order.get('price', 'market')}")
        except Exception as e:
            print(f"Error checking orders: {e}")
        
        # Cancel successful orders
        print(f"\n🗑️ Cancelling test orders...")
        for order in successful_orders:
            try:
                service.exchange.cancel_order(order['id'], 'BTC/USDT:USDT')
                print(f"✅ Cancelled {order['id']}")
            except Exception as e:
                print(f"⚠️ Failed to cancel {order['id']}: {str(e)}")
    
    else:
        print(f"❌ NO SUCCESSFUL TAKE PROFIT ORDERS")
        print(f"Need to investigate further...")
    
    # Check final position
    print(f"\n📊 Final position status:")
    positions = service.get_open_positions()
    for pos in positions.get('positions', []):
        if pos['symbol'] == 'BTC/USDT:USDT' and pos.get('contracts', 0) != 0:
            print(f"Position: {pos.get('contracts', 0)} BTC")
            print(f"Entry: ${pos.get('entryPrice', 0):.1f}")
            print(f"PnL: ${pos.get('unrealizedPnl', 0):.2f}")
    
    # Close position
    user_input = input("\nClose test position? (y/N): ")
    if user_input.lower() == 'y':
        close_result = service.place_order("BTC", "sell", 0.001, order_type="market")
        if close_result.get('success'):
            print(f"✅ Position closed: {close_result['order_id']}")
    
    return successful_orders

if __name__ == "__main__":
    asyncio.run(test_wide_spreads())
