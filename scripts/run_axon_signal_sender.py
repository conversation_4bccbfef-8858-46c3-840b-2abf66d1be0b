#!/usr/bin/env python3
"""
Standalone AXON Signal Sender
Sends continuous market analysis to AXON AI frontend without any trading.
Uses original backtested thresholds for accurate signal generation.
"""

import os
import sys
import asyncio
import argparse
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService
from services.axon_signal_service import AxonSignalService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/axon_signal_sender.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AxonSignalSender:
    """Standalone AXON signal sender - NO TRADING, only analysis."""
    
    def __init__(self):
        """Initialize the AXON signal sender."""
        logger.info("🔍 Initializing AXON Signal Sender")
        logger.info("   Purpose: Send market analysis to AXON AI frontend")
        logger.info("   Trading: DISABLED - Information only")
        
        # Initialize models with original backtested parameters
        self.btc_model = TITAN2KModel(aggressive_mode=True)
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)
        
        # Ensure original backtested thresholds
        self.btc_model.trend_strength_threshold = 0.4
        self.ada_model.trend_strength_threshold = 0.4
        
        # Market data service
        self.market_data = MarketDataService()
        
        # AXON signal service
        self.axon_service = AxonSignalService()
        
        logger.info("✅ AXON Signal Sender initialized")
        logger.info(f"   BTC Threshold: {self.btc_model.trend_strength_threshold}")
        logger.info(f"   ADA Threshold: {self.ada_model.trend_strength_threshold}")
        logger.info("   Mode: Information Only - No Trading")
    
    async def get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get market data for analysis."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")
            
            if symbol == "BTC":
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                medium_df = await self.market_data.get_historical_data(symbol, "4h", start_date_str, end_date_str)
                lower_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                return {"daily": daily_df, "medium": medium_df, "lower": lower_df}
            else:  # ADA
                daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)
                hourly_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)
                minute15_df = await self.market_data.get_historical_data(symbol, "15m", start_date_str, end_date_str)
                return {"daily": daily_df, "hourly": hourly_df, "minute15": minute15_df}
                
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return None
    
    async def analyze_symbol(self, symbol: str) -> Dict[str, Any]:
        """Analyze symbol and generate signal information."""
        try:
            logger.info(f"📊 Analyzing {symbol} with backtested algorithm")
            
            # Get market data
            data = await self.get_market_data(symbol)
            if not data:
                return {"error": "Failed to get market data"}
            
            # Process with appropriate model
            if symbol == "BTC":
                result_df = self.btc_model.process_data(data["daily"], data["medium"], data["lower"])
                model_name = "TITAN2K"
            else:  # ADA
                result_df = self.ada_model.process_data(data["daily"], data["hourly"], data["minute15"])
                model_name = "TITAN2K_TrendTuned"
            
            if result_df is None or result_df.empty:
                return {"error": "No analysis data generated"}
            
            latest = result_df.iloc[-1]
            current_price = latest.get('close', 0)
            signal = latest.get('signal', 'HOLD')
            confidence = latest.get('confidence', 0)
            combined_trend = latest.get('combined_trend', 0)
            threshold = 0.4
            
            # Determine status and reasoning
            if abs(combined_trend) >= threshold:
                status = "SIGNAL_GENERATED"
                reasoning = f"{symbol} {signal} signal: Combined trend {combined_trend:.3f} exceeds threshold {threshold}"
            else:
                status = "NO_SIGNAL"
                reasoning = f"{symbol} analysis: Combined trend {combined_trend:.3f} below threshold {threshold} - waiting for stronger market conditions"
            
            logger.info(f"📈 {symbol} Analysis:")
            logger.info(f"   Price: ${current_price:.4f}")
            logger.info(f"   Combined Trend: {combined_trend:.3f}")
            logger.info(f"   Threshold: {threshold}")
            logger.info(f"   Signal: {signal}")
            logger.info(f"   Confidence: {confidence:.1%}")
            logger.info(f"   Status: {status}")
            
            return {
                "symbol": symbol,
                "model": model_name,
                "price": current_price,
                "signal": signal,
                "confidence": confidence,
                "combined_trend": combined_trend,
                "threshold": threshold,
                "status": status,
                "reasoning": reasoning,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing {symbol}: {str(e)}")
            return {"error": str(e)}
    
    async def send_to_axon(self, analysis: Dict[str, Any]) -> bool:
        """Send analysis to AXON AI."""
        try:
            success = await self.axon_service.send_to_axon(analysis)
            if success:
                logger.info(f"✅ Successfully sent {analysis['symbol']} analysis to AXON AI")
            else:
                logger.error(f"❌ Failed to send {analysis['symbol']} analysis to AXON AI")
            return success
        except Exception as e:
            logger.error(f"Error sending to AXON: {str(e)}")
            return False
    
    async def run_analysis_cycle(self):
        """Run one complete analysis cycle."""
        logger.info("🔄 Starting AXON analysis cycle")
        
        # Analyze BTC
        btc_analysis = await self.analyze_symbol("BTC")
        if not btc_analysis.get("error"):
            await self.send_to_axon(btc_analysis)
        
        # Analyze ADA
        ada_analysis = await self.analyze_symbol("ADA")
        if not ada_analysis.get("error"):
            await self.send_to_axon(ada_analysis)
        
        logger.info("✅ AXON analysis cycle completed")
    
    async def run(self, interval_minutes: int = 5):
        """Run the AXON signal sender continuously."""
        logger.info("🚀 Starting AXON Signal Sender")
        logger.info("="*60)
        logger.info("PURPOSE: Send continuous market analysis to AXON AI")
        logger.info("TRADING: DISABLED - Information only")
        logger.info("THRESHOLDS: Original backtested (0.4)")
        logger.info(f"INTERVAL: Every {interval_minutes} minutes")
        logger.info("="*60)
        
        while True:
            try:
                await self.run_analysis_cycle()
                logger.info(f"⏳ Waiting {interval_minutes} minutes until next analysis")
                await asyncio.sleep(interval_minutes * 60)
                
            except Exception as e:
                logger.error(f"Error in AXON signal sender: {str(e)}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying

async def main():
    """Main function to run AXON Signal Sender."""
    parser = argparse.ArgumentParser(description="AXON Signal Sender (Information Only)")
    parser.add_argument("--interval", type=int, default=5, help="Analysis interval in minutes")
    args = parser.parse_args()
    
    logger.info("🔍 Starting AXON Signal Sender")
    logger.info("="*60)
    logger.info("PURPOSE: Continuous market analysis for AXON AI")
    logger.info("TRADING: DISABLED")
    logger.info("THRESHOLDS: Original backtested parameters")
    logger.info(f"INTERVAL: Every {args.interval} minutes")
    logger.info("="*60)
    
    # Create and run signal sender
    sender = AxonSignalSender()
    await sender.run(args.interval)

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run AXON signal sender
    asyncio.run(main())
