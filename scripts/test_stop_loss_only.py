#!/usr/bin/env python3
"""
Test Stop Loss Only
Simple test to get stop loss orders working first.
"""

import os
import sys
import asyncio
import logging

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_stop_loss():
    """Test stop loss order placement."""
    service = PersonalPhemexService()
    
    # Get current BTC price
    ticker = service.exchange.fetch_ticker('BTC/USDT:USDT')
    current_price = ticker['last']
    
    print(f"Current BTC Price: ${current_price:.1f}")
    
    # Create a small position first
    print("\n1. Creating small BTC position...")
    entry_result = service.place_order(
        symbol="BTC",
        side="buy", 
        amount=0.001,
        order_type="market"
    )
    
    if not entry_result.get('success'):
        print(f"❌ Entry failed: {entry_result.get('error')}")
        return
    
    print(f"✅ Entry placed: {entry_result['order_id']}")
    
    # Wait for fill
    await asyncio.sleep(5)
    
    # Try different stop loss approaches
    stop_price = current_price * 0.97  # 3% below
    
    print(f"\n2. Testing stop loss at ${stop_price:.1f}")
    
    # Approach 1: Basic stop market
    try:
        print("Trying basic stopMarket order...")
        order = service.exchange.create_order(
            symbol='BTC/USDT:USDT',
            type='stopMarket',
            side='sell',
            amount=0.001,
            params={
                'stopPx': round(stop_price, 1),
                'triggerDirection': 'down',
                'reduceOnly': True
            }
        )
        print(f"✅ Stop loss placed: {order['id']}")
        
    except Exception as e:
        print(f"❌ stopMarket failed: {str(e)}")
        
        # Approach 2: Try stop limit
        try:
            print("Trying stopLimit order...")
            order = service.exchange.create_order(
                symbol='BTC/USDT:USDT',
                type='stopLimit',
                side='sell',
                amount=0.001,
                price=round(stop_price * 0.99, 1),  # Limit price slightly below stop
                params={
                    'stopPx': round(stop_price, 1),
                    'triggerDirection': 'down',
                    'reduceOnly': True
                }
            )
            print(f"✅ Stop limit placed: {order['id']}")
            
        except Exception as e:
            print(f"❌ stopLimit failed: {str(e)}")
            
            # Approach 3: Try conditional order
            try:
                print("Trying conditional order...")
                order = service.exchange.create_order(
                    symbol='BTC/USDT:USDT',
                    type='market',
                    side='sell',
                    amount=0.001,
                    params={
                        'stopPx': round(stop_price, 1),
                        'ordType': 'Stop',
                        'reduceOnly': True
                    }
                )
                print(f"✅ Conditional order placed: {order['id']}")
                
            except Exception as e:
                print(f"❌ Conditional order failed: {str(e)}")
    
    # Check final status
    print("\n3. Checking position status...")
    positions = service.get_open_positions()
    for pos in positions.get('positions', []):
        if pos['symbol'] == 'BTC/USDT:USDT' and pos.get('contracts', 0) != 0:
            print(f"Position: {pos.get('contracts', 0)} BTC")
            print(f"Entry: ${pos.get('entryPrice', 0):.1f}")
            print(f"PnL: ${pos.get('unrealizedPnl', 0):.2f}")
    
    # Check orders
    try:
        orders = service.exchange.fetch_open_orders('BTC/USDT:USDT')
        print(f"Open orders: {len(orders)}")
        for order in orders:
            print(f"  {order['type']} {order['side']} @ ${order.get('price', 'market')}")
    except Exception as e:
        print(f"Error checking orders: {e}")
    
    # Ask to close
    user_input = input("\nClose position? (y/N): ")
    if user_input.lower() == 'y':
        close_result = service.place_order("BTC", "sell", 0.001, "market")
        if close_result.get('success'):
            print(f"✅ Position closed: {close_result['order_id']}")

if __name__ == "__main__":
    asyncio.run(test_stop_loss())
