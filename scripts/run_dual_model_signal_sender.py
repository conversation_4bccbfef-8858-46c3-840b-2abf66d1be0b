#!/usr/bin/env python
"""
Dual Model Signal Sender

This script runs two specific models and sends their signals to the AXON AI frontend:
1. Original TITAN model for BTC
2. Trend-tuned model for ADA

Each model can have one position open at a time.

Usage:
    python run_dual_model_signal_sender.py --interval 30
"""

import os
import sys
import json
import argparse
import logging
import requests
import asyncio
import pandas as pd
import numpy as np
import csv
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the models
from services.titan2k_model import TITAN2KModel  # Original TITAN for BTC
from services.titan2k_trend_tuned import TITAN2KTrendTuned  # Trend-tuned for ADA
from services.market_data_service import MarketDataService
from services.phemex_service import PhemexService  # Import Phemex service
from config.phemex_config import PHEMEX_TRADING_ENABLED  # Import Phemex config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("logs/dual_model_signals.log")
    ]
)
logger = logging.getLogger(__name__)

# AXON AI webhook URL
AXON_WEBHOOK_URL = "https://axonai-production.up.railway.app/api/v1/signals/tradingview-webhook"

# Flag to enable/disable actual sending of signals to AXON
ENABLE_SIGNAL_SENDING = True

class DualModelSignalSender:
    """
    Service to run the original TITAN model for BTC and trend-tuned model for ADA,
    and send signals to AXON AI
    """
    def __init__(self):
        """Initialize the signal sender."""
        self.market_data = MarketDataService()

        # Initialize models
        self.btc_model = TITAN2KModel(aggressive_mode=True)  # Original TITAN for BTC
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)  # Trend-tuned for ADA

        # Track last signal times and positions
        self.last_btc_signal_time = None
        self.last_ada_signal_time = None
        self.btc_position = None
        self.ada_position = None

        # Ensure data directories exist
        os.makedirs("titan2k_live_btc_risk3", exist_ok=True)
        os.makedirs("titan2k_live_ada_risk2", exist_ok=True)

        # Initialize trades files if they don't exist
        self._init_trades_file("titan2k_live_btc_risk3/trades.csv")
        self._init_trades_file("titan2k_live_ada_risk2/trades.csv")

    def _init_trades_file(self, file_path: str):
        """Initialize a trades CSV file if it doesn't exist."""
        if not os.path.exists(file_path):
            with open(file_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    "entry_time", "exit_time", "symbol", "type",
                    "entry_price", "exit_price", "position_size",
                    "position_value", "leverage", "risk_pct",
                    "pnl_pct", "leveraged_pnl_pct", "pnl_value", "exit_reason"
                ])

    def _save_signal_to_trades(self, symbol: str, signal_type: str, price: float):
        """
        Save a signal to the trades CSV file.

        Args:
            symbol: Trading symbol (BTC or ADA)
            signal_type: Signal type (BUY or SELL)
            price: Entry price
        """
        # Determine the file path based on the symbol
        file_path = f"titan2k_live_btc_risk3/trades.csv" if symbol == "BTC" else f"titan2k_live_ada_risk2/trades.csv"

        # Check if there's an open position (no exit time in the last row)
        has_open_position = False
        try:
            df = pd.read_csv(file_path)
            if not df.empty and pd.isna(df.iloc[-1]['exit_time']):
                has_open_position = True
        except (FileNotFoundError, pd.errors.EmptyDataError):
            pass

        # If there's already an open position, don't add a new one
        if has_open_position:
            logger.warning(f"Already have an open position for {symbol} in trades.csv, not adding a new one")
            return

        # Prepare the trade data
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        leverage = 10.0  # Default leverage
        risk_pct = 3.0   # Default risk percentage
        position_size = 0.03 if symbol == "BTC" else 500  # 3% of account for BTC
        position_value = position_size * price

        # Write the trade to the CSV file
        with open(file_path, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                current_time,  # entry_time
                "",            # exit_time (empty for open position)
                symbol,        # symbol
                signal_type,   # type
                price,         # entry_price
                "",            # exit_price (empty for open position)
                position_size, # position_size
                position_value,# position_value
                leverage,      # leverage
                risk_pct,      # risk_pct
                "",            # pnl_pct (empty for open position)
                "",            # leveraged_pnl_pct (empty for open position)
                "",            # pnl_value (empty for open position)
                ""             # exit_reason (empty for open position)
            ])

        logger.info(f"Saved {signal_type} signal for {symbol} to trades.csv with entry price {price}")

    def _get_detailed_analysis(self, latest_row: pd.Series, symbol: str, current_price: float) -> Dict[str, Any]:
        """
        Get detailed analysis and reasoning for why a trade was taken or skipped.

        Args:
            latest_row: Latest row from the processed dataframe
            symbol: Trading symbol (BTC or ADA)
            current_price: Current price

        Returns:
            Dictionary with detailed analysis
        """
        try:
            # Get technical indicators from the latest row
            rsi = latest_row.get('rsi', 50)
            macd = latest_row.get('macd', 0)
            macd_signal = latest_row.get('macd_signal', 0)
            bb_upper = latest_row.get('bb_upper', current_price * 1.02)
            bb_lower = latest_row.get('bb_lower', current_price * 0.98)
            volume = latest_row.get('volume', 0)
            sma_20 = latest_row.get('sma_20', current_price)
            sma_50 = latest_row.get('sma_50', current_price)
            ema_12 = latest_row.get('ema_12', current_price)
            ema_26 = latest_row.get('ema_26', current_price)
            atr = latest_row.get('atr', current_price * 0.02)
            signal = latest_row.get('signal', None)
            confidence = latest_row.get('confidence', 0.5)

            # Analyze trend direction
            if current_price > sma_20 > sma_50:
                trend_direction = "Strong Uptrend"
            elif current_price > sma_20:
                trend_direction = "Weak Uptrend"
            elif current_price < sma_20 < sma_50:
                trend_direction = "Strong Downtrend"
            elif current_price < sma_20:
                trend_direction = "Weak Downtrend"
            else:
                trend_direction = "Sideways/Consolidation"

            # Analyze trend strength
            price_vs_sma20 = (current_price - sma_20) / sma_20 * 100
            if abs(price_vs_sma20) > 5:
                trend_strength = "Very Strong"
            elif abs(price_vs_sma20) > 2:
                trend_strength = "Strong"
            elif abs(price_vs_sma20) > 1:
                trend_strength = "Moderate"
            else:
                trend_strength = "Weak"

            # Analyze momentum
            macd_momentum = macd - macd_signal
            if macd_momentum > 0.001:
                momentum = "Bullish (MACD > Signal)"
            elif macd_momentum < -0.001:
                momentum = "Bearish (MACD < Signal)"
            else:
                momentum = "Neutral (MACD ≈ Signal)"

            # Analyze RSI conditions
            if rsi > 70:
                rsi_condition = "Overbought"
            elif rsi < 30:
                rsi_condition = "Oversold"
            elif rsi > 60:
                rsi_condition = "Bullish"
            elif rsi < 40:
                rsi_condition = "Bearish"
            else:
                rsi_condition = "Neutral"

            # Analyze Bollinger Bands
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            if bb_position > 0.8:
                bb_analysis = "Near Upper Band (Potential Resistance)"
            elif bb_position < 0.2:
                bb_analysis = "Near Lower Band (Potential Support)"
            else:
                bb_analysis = f"Middle Range ({bb_position:.1%} of BB range)"

            # Volume analysis (simplified)
            volume_analysis = "Normal" if volume > 0 else "Low"

            # Generate reasoning based on conditions
            reasoning_parts = []

            if signal == "BUY":
                reasoning_parts.append(f"BUY signal triggered")
                reasoning_parts.append(f"RSI: {rsi:.1f} ({rsi_condition})")
                reasoning_parts.append(f"MACD momentum: {momentum}")
                reasoning_parts.append(f"Price vs SMA20: {price_vs_sma20:+.2f}%")
            elif signal == "SELL":
                reasoning_parts.append(f"SELL signal triggered")
                reasoning_parts.append(f"RSI: {rsi:.1f} ({rsi_condition})")
                reasoning_parts.append(f"MACD momentum: {momentum}")
                reasoning_parts.append(f"Price vs SMA20: {price_vs_sma20:+.2f}%")
            else:
                # Determine why no signal was generated
                skip_reasons = []

                if 30 <= rsi <= 70:
                    skip_reasons.append(f"RSI neutral ({rsi:.1f})")
                if abs(macd_momentum) < 0.001:
                    skip_reasons.append("MACD momentum weak")
                if abs(price_vs_sma20) < 1:
                    skip_reasons.append("Price too close to SMA20")
                if confidence < 0.6:
                    skip_reasons.append(f"Low confidence ({confidence:.1%})")
                if trend_direction == "Sideways/Consolidation":
                    skip_reasons.append("Market consolidating")

                if not skip_reasons:
                    skip_reasons.append("Algorithm conditions not met")

                reasoning_parts.extend(skip_reasons)

            reasoning = "; ".join(reasoning_parts)

            # Generate skip reason if no signal
            if not signal or signal not in ["BUY", "SELL"]:
                if trend_direction == "Sideways/Consolidation":
                    skip_reason = f"Market consolidating - waiting for clear direction (RSI: {rsi:.1f}, Trend: {trend_strength})"
                elif confidence < 0.6:
                    skip_reason = f"Low confidence setup ({confidence:.1%}) - need stronger signals"
                elif rsi > 70:
                    skip_reason = f"Overbought conditions (RSI: {rsi:.1f}) - waiting for pullback"
                elif rsi < 30:
                    skip_reason = f"Oversold conditions (RSI: {rsi:.1f}) - waiting for bounce confirmation"
                elif abs(macd_momentum) < 0.001:
                    skip_reason = f"Weak momentum (MACD ≈ Signal) - waiting for stronger move"
                else:
                    skip_reason = f"Algorithm criteria not met - need better setup"
            else:
                skip_reason = "N/A - Signal generated"

            return {
                "reasoning": reasoning,
                "trend_direction": trend_direction,
                "trend_strength": trend_strength,
                "momentum": momentum,
                "rsi_condition": rsi_condition,
                "bb_analysis": bb_analysis,
                "volume_analysis": volume_analysis,
                "confidence": confidence,
                "skip_reason": skip_reason,
                "technical_data": {
                    "rsi": rsi,
                    "macd": macd,
                    "macd_signal": macd_signal,
                    "price_vs_sma20": price_vs_sma20,
                    "bb_position": bb_position
                }
            }

        except Exception as e:
            logger.error(f"Error in detailed analysis: {str(e)}")
            return {
                "reasoning": "Analysis error",
                "trend_direction": "Unknown",
                "trend_strength": "Unknown",
                "momentum": "Unknown",
                "rsi_condition": "Unknown",
                "bb_analysis": "Unknown",
                "volume_analysis": "Unknown",
                "confidence": 0.0,
                "skip_reason": f"Analysis error: {str(e)}",
                "technical_data": {}
            }

    def _close_position_in_trades(self, symbol: str, exit_price: float, exit_reason: str = "SIGNAL"):
        """
        Close an open position in the trades CSV file.

        Args:
            symbol: Trading symbol (BTC or ADA)
            exit_price: Exit price
            exit_reason: Reason for closing the position
        """
        # Determine the file path based on the symbol
        file_path = f"titan2k_live_btc_risk3/trades.csv" if symbol == "BTC" else f"titan2k_live_ada_risk2/trades.csv"

        try:
            # Read the trades file
            df = pd.read_csv(file_path)

            # Check if there's an open position (no exit time in the last row)
            if not df.empty and pd.isna(df.iloc[-1]['exit_time']):
                # Get the last row (open position)
                last_row = df.iloc[-1].copy()

                # Update the exit information
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                last_row['exit_time'] = current_time
                last_row['exit_price'] = exit_price

                # Calculate PnL
                entry_price = last_row['entry_price']
                if last_row['type'] == 'BUY':
                    pnl_pct = (exit_price - entry_price) / entry_price * 100
                else:  # SELL
                    pnl_pct = (entry_price - exit_price) / entry_price * 100

                leverage = last_row['leverage']
                leveraged_pnl_pct = pnl_pct * leverage
                position_value = last_row['position_value']
                pnl_value = position_value * pnl_pct / 100

                last_row['pnl_pct'] = pnl_pct
                last_row['leveraged_pnl_pct'] = leveraged_pnl_pct
                last_row['pnl_value'] = pnl_value
                last_row['exit_reason'] = exit_reason

                # Update the last row in the dataframe
                df.iloc[-1] = last_row

                # Write the updated dataframe back to the CSV file
                df.to_csv(file_path, index=False)

                logger.info(f"Closed {last_row['type']} position for {symbol} in trades.csv with exit price {exit_price} and PnL {pnl_pct:.2f}%")

        except Exception as e:
            logger.error(f"Error closing position in trades.csv: {str(e)}")

    async def get_market_data(self, symbol: str) -> Dict[str, pd.DataFrame]:
        """
        Get market data for a symbol.

        Args:
            symbol: Trading symbol (BTC or ADA)

        Returns:
            Dictionary with appropriate timeframe data for each model
        """
        logger.info(f"Getting market data for {symbol}")

        try:
            # Get data for the last 90 days
            end_date = datetime.now()
            start_date = end_date - timedelta(days=90)

            # Format dates
            start_date_str = start_date.strftime("%Y-%m-%d")
            end_date_str = end_date.strftime("%Y-%m-%d")

            # Get daily data
            logger.info(f"Fetching {symbol} 1d data from {start_date_str} to {end_date_str}")
            daily_df = await self.market_data.get_historical_data(symbol, "1d", start_date_str, end_date_str)

            if symbol == "BTC":
                # For BTC, get 4h and 1h data (original TITAN model)
                logger.info(f"Fetching {symbol} 4h data from {start_date_str} to {end_date_str}")
                medium_df = await self.market_data.get_historical_data(symbol, "4h", start_date_str, end_date_str)

                logger.info(f"Fetching {symbol} 1h data from {start_date_str} to {end_date_str}")
                lower_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)

                return {
                    "daily": daily_df,
                    "medium": medium_df,
                    "lower": lower_df
                }
            else:
                # For ADA, get 1h and 15m data (trend-tuned model)
                logger.info(f"Fetching {symbol} 1h data from {start_date_str} to {end_date_str}")
                hourly_df = await self.market_data.get_historical_data(symbol, "1h", start_date_str, end_date_str)

                logger.info(f"Fetching {symbol} 15m data from {start_date_str} to {end_date_str}")
                minute15_df = await self.market_data.get_historical_data(symbol, "15m", start_date_str, end_date_str)

                return {
                    "daily": daily_df,
                    "hourly": hourly_df,
                    "minute15": minute15_df
                }

        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None

    async def generate_signals(self, symbol: str) -> Dict[str, Any]:
        """
        Generate signals for a symbol using the appropriate model.

        Args:
            symbol: Trading symbol (BTC or ADA)

        Returns:
            Dictionary with signal data
        """
        logger.info(f"Generating signals for {symbol}")

        try:
            # Get market data
            data = await self.get_market_data(symbol)
            if not data:
                logger.error(f"No market data available for {symbol}")
                return None

            # Process data with the appropriate model
            if symbol == "BTC":
                # For BTC, use daily, 4h, and 1h data (original TITAN model)
                df = self.btc_model.process_data(data["daily"], data["medium"], data["lower"])
            else:  # ADA
                # For ADA, use daily, 1h, and 15m data (trend-tuned model)
                df = self.ada_model.process_data(data["daily"], data["hourly"], data["minute15"])

            # Get the latest row
            latest_row = df.iloc[-1]
            current_price = latest_row["close"]

            # Get detailed analysis for reasoning
            analysis = self._get_detailed_analysis(latest_row, symbol, current_price)

            # Log detailed reasoning
            logger.info(f"📊 {symbol} ANALYSIS @ ${current_price:,.2f}")
            logger.info(f"   🔍 {analysis['reasoning']}")
            logger.info(f"   📈 Trend: {analysis['trend_direction']}")
            logger.info(f"   💪 Strength: {analysis['trend_strength']}")
            logger.info(f"   ⚡ Momentum: {analysis['momentum']}")
            logger.info(f"   📊 Volume: {analysis['volume_analysis']}")
            logger.info(f"   🎯 Confidence: {analysis['confidence']:.1%}")

            # Check if we have a signal
            signal = latest_row.get("signal", None)
            if not signal or signal not in ["BUY", "SELL"]:
                logger.info(f"❌ NO TRADE: {analysis['skip_reason']}")

                # Send skip reasoning to AXON AI
                await self._send_analysis_to_axon(symbol, analysis, current_price)
                return None

            # Signal found - log success
            logger.info(f"✅ {signal} SIGNAL GENERATED!")

            # Get stop loss and take profit
            if signal == "BUY":
                stop_loss = latest_row.get("stop_loss", current_price * 0.97)  # Default to 3% stop loss
                take_profit = latest_row.get("take_profit", current_price * 1.09)  # Default to 9% take profit
            else:  # SELL
                stop_loss = latest_row.get("stop_loss", current_price * 1.03)  # Default to 3% stop loss
                take_profit = latest_row.get("take_profit", current_price * 0.91)  # Default to 9% take profit

            # Get confidence level
            confidence = latest_row.get("confidence", 0.7)

            # Get timeframe
            timeframe = "4h" if symbol == "BTC" else "15m"

            # Create signal data
            signal_data = {
                "symbol": symbol,
                "signal": signal,
                "price": current_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "confidence": confidence,
                "timeframe": timeframe,
                "timestamp": datetime.now().isoformat(),
                "strategy": "TITAN2K_Original" if symbol == "BTC" else "ADA_Trend_Tuned"
            }

            return signal_data

        except Exception as e:
            logger.error(f"Error generating signals for {symbol}: {e}")
            return None

    async def calculate_position_size(self, phemex_service, symbol: str, risk_percent: float, leverage: int = 1) -> float:
        """
        Calculate safe position size based on account balance.

        Args:
            phemex_service: Phemex service instance
            symbol: Trading symbol (BTC or ADA)
            risk_percent: Risk percentage (e.g., 0.02 for 2%)
            leverage: Leverage multiplier

        Returns:
            Safe position size in base currency units
        """
        try:
            # Get account balance
            balance = phemex_service.exchange.fetch_balance()
            free_balance = balance['free']['USDT']

            # Get current price
            if symbol == "BTC":
                ticker = phemex_service.exchange.fetch_ticker('BTC/USDT:USDT')
            else:  # ADA
                ticker = phemex_service.exchange.fetch_ticker('ADA/USDT:USDT')

            current_price = ticker['last']

            # Calculate safe position size
            # Position Value = Free Balance * Risk% * Leverage
            safe_position_value = free_balance * risk_percent * leverage
            safe_quantity = safe_position_value / current_price

            # Add safety margin (use 50% of calculated amount for testing)
            safe_quantity = safe_quantity * 0.5

            # Ensure minimum viable trade size
            if symbol == "ADA" and safe_quantity < 10:
                safe_quantity = 10  # Minimum 10 ADA
            elif symbol == "BTC" and safe_quantity < 0.001:
                safe_quantity = 0.001  # Minimum 0.001 BTC

            logger.info(f"💰 Position sizing for {symbol}:")
            logger.info(f"   Free Balance: ${free_balance:.2f}")
            logger.info(f"   Risk: {risk_percent:.1%}, Leverage: {leverage}x")
            logger.info(f"   Position Value: ${safe_position_value:.2f}")
            logger.info(f"   Price: ${current_price:.4f}")
            logger.info(f"   Safe Quantity: {safe_quantity:.2f} {symbol}")

            return safe_quantity

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            # Return minimal safe amounts as fallback
            return 0.001 if symbol == "BTC" else 10

    async def format_signal_for_axon(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Format signal data for AXON AI.

        Args:
            signal_data: Signal data

        Returns:
            Formatted signal for AXON AI
        """
        if not signal_data:
            return None

        symbol = signal_data["symbol"]
        signal_type = signal_data["signal"]
        price = signal_data["price"]
        stop_loss = signal_data["stop_loss"]
        take_profit = signal_data["take_profit"]
        confidence = signal_data["confidence"]
        timeframe = signal_data["timeframe"]
        strategy = signal_data["strategy"]

        # Calculate stop loss and take profit percentages
        if signal_type == "BUY":
            stop_loss_percent = abs((price - stop_loss) / price * 100)
            take_profit_percent = abs((take_profit - price) / price * 100)
        else:  # SELL
            stop_loss_percent = abs((stop_loss - price) / price * 100)
            take_profit_percent = abs((price - take_profit) / price * 100)

        # Format confidence level
        confidence_level = "High" if confidence >= 0.8 else "Medium" if confidence >= 0.6 else "Low"

        # Format signal for AXON
        axon_signal = {
            "symbol": symbol,
            "type": signal_type,
            "price": price,
            "target": take_profit,
            "stop_loss": stop_loss,
            "stop_loss_percent": stop_loss_percent,
            "take_profit_percent": take_profit_percent,
            "confidence": confidence_level,
            "strategy": strategy,
            "timeframe": timeframe
        }

        return axon_signal

    async def send_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Send a signal to AXON AI and execute trade on Phemex if enabled.

        Args:
            signal_data: Formatted signal data for AXON AI

        Returns:
            Response from AXON AI and Phemex or status message
        """
        if not signal_data:
            return {"status": "skipped", "message": "No valid signal data provided"}

        # Log the signal we're about to send
        logger.info(f"Preparing to send {signal_data['type']} signal for {signal_data['symbol']} to AXON AI")
        logger.debug(f"Signal details: {json.dumps(signal_data, indent=2)}")

        # Check if signal sending is enabled
        if not ENABLE_SIGNAL_SENDING:
            logger.info("Signal sending is disabled. Set ENABLE_SIGNAL_SENDING to True to enable.")
            return {
                "status": "simulated",
                "message": f"Signal for {signal_data['symbol']} would have been sent (sending disabled)",
                "signal": signal_data
            }

        # Send the signal to AXON AI
        axon_response = None
        try:
            headers = {"Content-Type": "application/json"}
            response = requests.post(AXON_WEBHOOK_URL, headers=headers, json=signal_data, timeout=10)

            # Check if the request was successful
            if response.status_code == 200:
                logger.info(f"Successfully sent {signal_data['type']} signal for {signal_data['symbol']} to AXON AI")
                axon_response = response.json()
            else:
                logger.error(f"Failed to send signal to AXON AI: {response.status_code} - {response.text}")
                axon_response = {
                    "status": "error",
                    "message": f"Failed to send signal: {response.status_code}",
                    "details": response.text
                }

        except Exception as e:
            logger.error(f"Error sending signal to AXON AI: {str(e)}")
            axon_response = {
                "status": "error",
                "message": f"Error sending signal: {str(e)}"
            }

        # Execute trade on Phemex if enabled
        phemex_response = None
        if PHEMEX_TRADING_ENABLED and axon_response and axon_response.get("status") == "success":
            try:
                logger.info(f"Executing {signal_data['type']} trade for {signal_data['symbol']} on Phemex")

                # Initialize Phemex service
                phemex_service = PhemexService()

                # Calculate safe position size based on account balance
                risk_percent = 0.03 if signal_data['symbol'] == "BTC" else 0.02  # 3% for BTC, 2% for ADA
                safe_quantity = await self.calculate_position_size(phemex_service, signal_data['symbol'], risk_percent)

                # Execute the trade
                if signal_data['type'] == "BUY":
                    # For BUY signals, place a buy order
                    phemex_response = await phemex_service.place_order(
                        symbol=signal_data['symbol'],
                        side="buy",
                        order_type="Market",
                        quantity=safe_quantity,
                        price=signal_data['price']
                    )
                else:  # SELL
                    # For SELL signals, place a sell order
                    phemex_response = await phemex_service.place_order(
                        symbol=signal_data['symbol'],
                        side="sell",
                        order_type="Market",
                        quantity=safe_quantity,
                        price=signal_data['price']
                    )

                logger.info(f"Phemex trade execution result: {json.dumps(phemex_response, indent=2)}")

            except Exception as e:
                logger.error(f"Error executing trade on Phemex: {str(e)}")
                phemex_response = {
                    "status": "error",
                    "message": f"Error executing trade: {str(e)}"
                }

        # Combine responses
        combined_response = {
            "axon": axon_response,
            "phemex": phemex_response
        }

        return combined_response

    async def _send_analysis_to_axon(self, symbol: str, analysis: Dict[str, Any], current_price: float):
        """
        Send bot analysis (skip reasoning) to AXON AI when no signal is generated.

        Args:
            symbol: Trading symbol (BTC or ADA)
            analysis: Detailed analysis from _get_detailed_analysis
            current_price: Current price
        """
        try:
            # Determine bot name based on symbol
            bot_name = "titan2k" if symbol == "BTC" else "titan_trend_tuned"

            # Create analysis payload for AXON
            analysis_payload = {
                "bot_name": bot_name,
                "timestamp": int(datetime.now().timestamp() * 1000),  # Unix timestamp in milliseconds
                "status": "analyzing",
                "market_condition": f"{symbol} {analysis['trend_direction']} - {analysis['trend_strength']} strength",
                "reasoning": analysis['reasoning'],
                "confidence_level": analysis['confidence'] * 100,  # Convert to percentage
                "next_action": analysis['skip_reason'],
                "technical_summary": {
                    "rsi_4h": analysis['technical_data'].get('rsi', 50),
                    "macd_signal": "bullish" if "bullish" in analysis['momentum'].lower() else "bearish" if "bearish" in analysis['momentum'].lower() else "neutral",
                    "volume_ratio": 1.0,  # Default value
                    "volatility_percentile": 50,  # Default value
                    "support_level": f"${current_price * 0.95:,.2f}",
                    "resistance_level": f"${current_price * 1.05:,.2f}",
                    "current_price": f"${current_price:,.2f}"
                },
                "risk_assessment": "Low" if analysis['confidence'] < 0.3 else "Medium" if analysis['confidence'] < 0.7 else "High",
                "timeframe": "1h-4h-daily",
                "symbols_monitored": [symbol]
            }

            # Send to AXON AI bot analysis endpoint
            headers = {"Content-Type": "application/json"}
            response = requests.post(
                "https://axonai-production.up.railway.app/api/v1/signals/bot-analysis",
                headers=headers,
                json=analysis_payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"✅ Successfully sent {symbol} analysis to AXON AI")
            else:
                logger.error(f"❌ Failed to send {symbol} analysis to AXON AI: {response.status_code}")

        except Exception as e:
            logger.error(f"❌ Error sending {symbol} analysis to AXON AI: {str(e)}")

    async def _send_signal_with_analysis_to_axon(self, signal_data: Dict[str, Any], analysis: Dict[str, Any], symbol: str, current_price: float):
        """
        Send both signal and analysis to AXON AI using the combined endpoint.

        Args:
            signal_data: Signal data
            analysis: Detailed analysis from _get_detailed_analysis
            symbol: Trading symbol
            current_price: Current price
        """
        try:
            # Format signal for AXON
            axon_signal = await self.format_signal_for_axon(signal_data)
            if not axon_signal:
                logger.error(f"Could not format signal for {symbol}")
                return

            # Determine bot name based on symbol
            bot_name = "titan2k" if symbol == "BTC" else "titan_trend_tuned"

            # Create analysis payload
            analysis_payload = {
                "bot_name": bot_name,
                "timestamp": int(datetime.now().timestamp() * 1000),
                "status": "active",
                "market_condition": f"{symbol} {signal_data['signal']} signal - {analysis['trend_direction']} with {analysis['trend_strength']} strength",
                "reasoning": analysis['reasoning'],
                "confidence_level": analysis['confidence'] * 100,
                "next_action": f"Monitoring {signal_data['signal']} position for {symbol}",
                "technical_summary": {
                    "rsi_4h": analysis['technical_data'].get('rsi', 50),
                    "macd_signal": "bullish" if "bullish" in analysis['momentum'].lower() else "bearish" if "bearish" in analysis['momentum'].lower() else "neutral",
                    "volume_ratio": 1.2,  # Assume higher volume on signal
                    "volatility_percentile": 65,  # Assume moderate volatility on signal
                    "support_level": f"${signal_data['stop_loss']:,.2f}",
                    "resistance_level": f"${signal_data['take_profit']:,.2f}",
                    "current_price": f"${current_price:,.2f}"
                },
                "risk_assessment": "Medium" if analysis['confidence'] < 0.8 else "High",
                "timeframe": "1h-4h-daily",
                "symbols_monitored": [symbol]
            }

            # Create combined payload for AXON
            combined_payload = {
                "signal": axon_signal,
                "analysis": analysis_payload
            }

            # Send to AXON AI combined endpoint
            headers = {"Content-Type": "application/json"}
            response = requests.post(
                "https://axonai-production.up.railway.app/api/v1/signals/bot-analysis-with-signal",
                headers=headers,
                json=combined_payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"✅ Successfully sent {symbol} {signal_data['signal']} signal with analysis to AXON AI")
                return response.json()
            else:
                logger.error(f"❌ Failed to send {symbol} signal with analysis to AXON AI: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ Error sending {symbol} signal with analysis to AXON AI: {str(e)}")
            return None

    async def process_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Process a symbol to generate and send signals.

        Args:
            symbol: Trading symbol (BTC or ADA)

        Returns:
            Response from AXON AI or status message
        """
        # Check if we already have an open position for this symbol
        if symbol == "BTC" and self.btc_position:
            logger.info(f"Already have an open {self.btc_position} position for BTC, skipping signal generation")
            return {"status": "skipped", "message": f"Already have an open {self.btc_position} position for BTC"}
        elif symbol == "ADA" and self.ada_position:
            logger.info(f"Already have an open {self.ada_position} position for ADA, skipping signal generation")
            return {"status": "skipped", "message": f"Already have an open {self.ada_position} position for ADA"}

        # Check if we've sent a signal recently
        current_time = datetime.now()
        if symbol == "BTC" and self.last_btc_signal_time and (current_time - self.last_btc_signal_time).total_seconds() < 3600:
            logger.info(f"Sent a BTC signal less than an hour ago, skipping")
            return {"status": "skipped", "message": "Sent a BTC signal less than an hour ago"}
        elif symbol == "ADA" and self.last_ada_signal_time and (current_time - self.last_ada_signal_time).total_seconds() < 3600:
            logger.info(f"Sent an ADA signal less than an hour ago, skipping")
            return {"status": "skipped", "message": "Sent an ADA signal less than an hour ago"}

        # Generate signals (this now handles sending to AXON with analysis)
        signal_data = await self.generate_signals(symbol)
        if not signal_data:
            return {"status": "skipped", "message": f"No signal generated for {symbol}"}

        # Signal was already sent to AXON with analysis in generate_signals
        # Now execute trade on Phemex if enabled
        phemex_response = None
        if PHEMEX_TRADING_ENABLED:
            try:
                logger.info(f"Executing {signal_data['signal']} trade for {symbol} on Phemex")

                # Initialize Phemex service
                phemex_service = PhemexService()

                # Calculate safe position size based on account balance
                risk_percent = 0.03 if symbol == "BTC" else 0.02  # 3% for BTC, 2% for ADA
                safe_quantity = await self.calculate_position_size(phemex_service, symbol, risk_percent)

                # Execute the trade
                if signal_data['signal'] == "BUY":
                    phemex_response = await phemex_service.place_order(
                        symbol=symbol,
                        side="buy",
                        order_type="Market",
                        quantity=safe_quantity,
                        price=signal_data['price']
                    )
                else:  # SELL
                    phemex_response = await phemex_service.place_order(
                        symbol=symbol,
                        side="sell",
                        order_type="Market",
                        quantity=safe_quantity,
                        price=signal_data['price']
                    )

                logger.info(f"Phemex trade execution result: {json.dumps(phemex_response, indent=2)}")

            except Exception as e:
                logger.error(f"Error executing trade on Phemex: {str(e)}")
                phemex_response = {
                    "status": "error",
                    "message": f"Error executing trade: {str(e)}"
                }

        response = {
            "axon": {"status": "success", "message": f"Signal sent with analysis for {symbol}"},
            "phemex": phemex_response
        }

        # Update last signal time and position
        axon_status = response.get("axon", {}).get("status")
        if axon_status == "success":
            if symbol == "BTC":
                self.last_btc_signal_time = current_time
                self.btc_position = signal_data["signal"]

                # Save the signal to the trades.csv file
                self._save_signal_to_trades(
                    symbol=symbol,
                    signal_type=signal_data["signal"],
                    price=signal_data["price"]
                )
            else:  # ADA
                self.last_ada_signal_time = current_time
                self.ada_position = signal_data["signal"]

                # Save the signal to the trades.csv file
                self._save_signal_to_trades(
                    symbol=symbol,
                    signal_type=signal_data["signal"],
                    price=signal_data["price"]
                )

        # Log Phemex response if available
        phemex_response = response.get("phemex")
        if phemex_response:
            if phemex_response.get("error"):
                logger.warning(f"Phemex trade execution had errors: {phemex_response.get('message')}")
            else:
                logger.info(f"Phemex trade executed successfully for {symbol}")

        return response

    async def run(self, interval: int = 30):
        """
        Run the signal sender continuously.

        Args:
            interval: Interval in minutes between signal checks
        """
        logger.info(f"Starting dual model signal sender with {interval} minute interval")
        logger.info("Using original TITAN model for BTC and trend-tuned model for ADA")

        while True:
            try:
                # Process BTC with original TITAN model
                logger.info("Processing BTC signals with original TITAN model")
                btc_response = await self.process_symbol("BTC")
                logger.info(f"BTC response: {json.dumps(btc_response, indent=2)}")

                # Process ADA with trend-tuned model
                logger.info("Processing ADA signals with trend-tuned model")
                ada_response = await self.process_symbol("ADA")
                logger.info(f"ADA response: {json.dumps(ada_response, indent=2)}")

                # Wait for the next interval
                logger.info(f"Waiting {interval} minutes until next signal check")
                await asyncio.sleep(interval * 60)

            except Exception as e:
                logger.error(f"Error in signal sender: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying

async def main():
    """Main function to run the dual model signal sender."""
    parser = argparse.ArgumentParser(description="Run dual model signal sender")
    parser.add_argument("--interval", type=int, default=30, help="Interval in minutes between signal checks")
    args = parser.parse_args()

    # Create signal sender
    signal_sender = DualModelSignalSender()

    # Run signal sender
    await signal_sender.run(args.interval)

if __name__ == "__main__":
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)

    # Run main
    asyncio.run(main())
