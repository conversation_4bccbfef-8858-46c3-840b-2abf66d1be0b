#!/usr/bin/env python3
"""
Phase 2: Algorithm Integration Testing
Test TITAN2K (BTC) and Trend-Tuned (ADA) algorithms with fixed Phemex service.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService
from services.titan2k_model import TITAN2KModel
from services.titan2k_trend_tuned import TITAN2KTrendTuned
from services.market_data_service import MarketDataService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/phase2_algorithm_testing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Phase2AlgorithmTester:
    """Phase 2 algorithm integration testing suite."""
    
    def __init__(self):
        """Initialize the tester."""
        self.phemex_service = PersonalPhemexService()
        self.market_data_service = MarketDataService()
        
        # Initialize models with aggressive settings (matching backtesting)
        self.btc_model = TITAN2KModel(aggressive_mode=True)
        self.ada_model = TITAN2KTrendTuned(aggressive_mode=True)
        
        self.test_results = {}
        self.critical_failures = []
        
    def log_test(self, test_name: str, success: bool, details: str = "", critical: bool = False):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}: {details}")
        
        self.test_results[test_name] = {
            "success": success, 
            "details": details,
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        
        if not success and critical:
            self.critical_failures.append(test_name)
    
    async def get_market_data(self, symbol: str, timeframes: list) -> Optional[Dict[str, Any]]:
        """Get market data for algorithm testing."""
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=365)).strftime("%Y-%m-%d")
            
            data = {}
            for timeframe in timeframes:
                df = await self.market_data_service.get_historical_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    start_date=start_date,
                    end_date=end_date
                )
                if not df.empty:
                    data[timeframe] = df
                    logger.info(f"✅ {symbol} {timeframe}: {len(df)} candles loaded")
                else:
                    logger.error(f"❌ No data for {symbol} {timeframe}")
                    return None
            
            return data
            
        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {str(e)}")
            return None
    
    async def test_btc_titan2k_signal_generation(self) -> bool:
        """Test BTC TITAN2K signal generation."""
        try:
            logger.info("🔍 Testing BTC TITAN2K signal generation")
            
            # Get market data for TITAN2K timeframes
            data = await self.get_market_data("BTC", ["1d", "4h", "1h"])
            if not data:
                self.log_test("BTC Signal Generation", False, "Failed to get market data", critical=True)
                return False
            
            # Process with TITAN2K model
            result_df = self.btc_model.process_data(
                data["1d"], data["4h"], data["1h"]
            )
            
            if result_df is None or result_df.empty:
                self.log_test("BTC Signal Generation", False, "No analysis data generated", critical=True)
                return False
            
            # Analyze latest signal
            latest = result_df.iloc[-1]
            signal = latest.get('signal', 'HOLD')
            confidence = latest.get('combined_trend', 0.0)
            alignment = latest.get('timeframe_alignment', 0.0)
            
            details = f"Signal: {signal}, Confidence: {confidence:.3f}, Alignment: {alignment:.3f}"
            
            if signal in ['BUY', 'SELL', 'HOLD']:
                self.log_test("BTC Signal Generation", True, details)
                return True
            else:
                self.log_test("BTC Signal Generation", False, f"Invalid signal: {signal}", critical=True)
                return False
                
        except Exception as e:
            self.log_test("BTC Signal Generation", False, str(e), critical=True)
            return False
    
    async def test_ada_trend_tuned_signal_generation(self) -> bool:
        """Test ADA Trend-Tuned signal generation."""
        try:
            logger.info("🔍 Testing ADA Trend-Tuned signal generation")
            
            # Get market data for Trend-Tuned timeframes
            data = await self.get_market_data("ADA", ["1d", "1h", "15m"])
            if not data:
                self.log_test("ADA Signal Generation", False, "Failed to get market data", critical=True)
                return False
            
            # Process with Trend-Tuned model
            result_df = self.ada_model.process_data(
                data["1d"], data["1h"], data["15m"]
            )
            
            if result_df is None or result_df.empty:
                self.log_test("ADA Signal Generation", False, "No analysis data generated", critical=True)
                return False
            
            # Analyze latest signal
            latest = result_df.iloc[-1]
            signal = latest.get('signal', 'HOLD')
            confidence = latest.get('combined_trend', 0.0)
            alignment = latest.get('timeframe_alignment', 0.0)
            
            details = f"Signal: {signal}, Confidence: {confidence:.3f}, Alignment: {alignment:.3f}"
            
            if signal in ['BUY', 'SELL', 'HOLD']:
                self.log_test("ADA Signal Generation", True, details)
                return True
            else:
                self.log_test("ADA Signal Generation", False, f"Invalid signal: {signal}", critical=True)
                return False
                
        except Exception as e:
            self.log_test("ADA Signal Generation", False, str(e), critical=True)
            return False
    
    async def test_confidence_threshold_filtering(self) -> bool:
        """Test confidence threshold filtering (0.4)."""
        try:
            logger.info("🔍 Testing confidence threshold filtering")
            
            # Test with both models
            btc_data = await self.get_market_data("BTC", ["1d", "4h", "1h"])
            ada_data = await self.get_market_data("ADA", ["1d", "1h", "15m"])
            
            if not btc_data or not ada_data:
                self.log_test("Confidence Filtering", False, "Failed to get market data")
                return False
            
            # Process signals
            btc_result = self.btc_model.process_data(btc_data["1d"], btc_data["4h"], btc_data["1h"])
            ada_result = self.ada_model.process_data(ada_data["1d"], ada_data["1h"], ada_data["15m"])
            
            # Check confidence thresholds
            btc_confidence = btc_result.iloc[-1].get('combined_trend', 0.0) if not btc_result.empty else 0.0
            ada_confidence = ada_result.iloc[-1].get('combined_trend', 0.0) if not ada_result.empty else 0.0
            
            # Test threshold logic
            btc_above_threshold = abs(btc_confidence) >= 0.4
            ada_above_threshold = abs(ada_confidence) >= 0.4
            
            details = f"BTC: {btc_confidence:.3f} ({'✅' if btc_above_threshold else '❌'}), " \
                     f"ADA: {ada_confidence:.3f} ({'✅' if ada_above_threshold else '❌'})"
            
            self.log_test("Confidence Filtering", True, details)
            return True
            
        except Exception as e:
            self.log_test("Confidence Filtering", False, str(e))
            return False
    
    async def test_position_size_calculation(self) -> bool:
        """Test position size calculations with algorithms."""
        try:
            logger.info("🔍 Testing algorithmic position size calculation")
            
            # Get account balance
            balance = self.phemex_service.get_account_balance()
            account_balance = balance['total_usdt']
            
            # Get current prices
            btc_ticker = self.phemex_service.exchange.fetch_ticker('BTC/USDT:USDT')
            ada_ticker = self.phemex_service.exchange.fetch_ticker('ADA/USDT:USDT')
            
            btc_price = btc_ticker['last']
            ada_price = ada_ticker['last']
            
            # Calculate position sizes using algorithm parameters
            btc_risk = 0.005  # 0.5% risk for BTC
            ada_risk = 0.003  # 0.3% risk for ADA
            
            btc_risk_amount = account_balance * btc_risk
            ada_risk_amount = account_balance * ada_risk
            
            # Simulate ATR-based stop distances
            btc_stop_distance = btc_price * 0.025  # 2.5% stop
            ada_stop_distance = ada_price * 0.03   # 3% stop
            
            # Calculate position sizes
            btc_size = (btc_risk_amount / btc_stop_distance) * 10  # 10x leverage
            ada_size = (ada_risk_amount / ada_stop_distance) * 10  # 10x leverage
            
            # Apply minimum constraints
            btc_size = max(btc_size, 0.001)  # BTC minimum
            ada_size = max(ada_size, 1.0)    # ADA minimum
            
            details = f"BTC: {btc_size:.6f} (${btc_size * btc_price:.2f}), " \
                     f"ADA: {ada_size:.2f} (${ada_size * ada_price:.2f})"
            
            if btc_size >= 0.001 and ada_size >= 1.0:
                self.log_test("Position Size Calculation", True, details)
                return True
            else:
                self.log_test("Position Size Calculation", False, f"Sizes too small: {details}")
                return False
                
        except Exception as e:
            self.log_test("Position Size Calculation", False, str(e))
            return False
    
    async def test_risk_management_calculation(self) -> bool:
        """Test ATR-based risk management calculations."""
        try:
            logger.info("🔍 Testing ATR-based risk management")
            
            # Get market data to calculate ATR
            btc_data = await self.get_market_data("BTC", ["1h"])
            ada_data = await self.get_market_data("ADA", ["15m"])
            
            if not btc_data or not ada_data:
                self.log_test("Risk Management", False, "Failed to get market data")
                return False
            
            # Process data to get ATR values
            btc_result = self.btc_model.calculate_indicators(btc_data["1h"])
            ada_result = self.ada_model.calculate_indicators(ada_data["15m"])
            
            # Get latest ATR values
            btc_atr = btc_result.iloc[-1].get('atr14', 0.0) if not btc_result.empty else 0.0
            ada_atr = ada_result.iloc[-1].get('atr14', 0.0) if not ada_result.empty else 0.0
            
            # Get current prices
            btc_price = btc_result.iloc[-1].get('close', 0.0) if not btc_result.empty else 0.0
            ada_price = ada_result.iloc[-1].get('close', 0.0) if not ada_result.empty else 0.0
            
            # Calculate stop losses (ATR-based)
            btc_stop_long = btc_price - (btc_atr * 2.5)  # TITAN2K multiplier
            btc_stop_short = btc_price + (btc_atr * 2.5)
            
            ada_stop_long = ada_price - (ada_atr * 2.0)  # Trend-Tuned multiplier
            ada_stop_short = ada_price + (ada_atr * 2.0)
            
            # Calculate take profits
            btc_tp_long = btc_price + (btc_atr * 4.0)   # 1.6:1 R:R
            ada_tp_long = ada_price + (ada_atr * 3.5)   # 1.75:1 R:R
            
            details = f"BTC ATR: {btc_atr:.2f}, Stop: ${btc_stop_long:.2f}, TP: ${btc_tp_long:.2f} | " \
                     f"ADA ATR: {ada_atr:.4f}, Stop: ${ada_stop_long:.4f}, TP: ${ada_tp_long:.4f}"
            
            if btc_atr > 0 and ada_atr > 0:
                self.log_test("Risk Management", True, details)
                return True
            else:
                self.log_test("Risk Management", False, f"Invalid ATR values: {details}")
                return False
                
        except Exception as e:
            self.log_test("Risk Management", False, str(e))
            return False
    
    async def test_signal_to_execution_pipeline(self) -> bool:
        """Test complete signal-to-execution pipeline (DRY RUN)."""
        try:
            logger.info("🔍 Testing signal-to-execution pipeline")
            
            # Generate signals
            btc_data = await self.get_market_data("BTC", ["1d", "4h", "1h"])
            ada_data = await self.get_market_data("ADA", ["1d", "1h", "15m"])
            
            if not btc_data or not ada_data:
                self.log_test("Signal Pipeline", False, "Failed to get market data")
                return False
            
            # Process signals
            btc_result = self.btc_model.process_data(btc_data["1d"], btc_data["4h"], btc_data["1h"])
            ada_result = self.ada_model.process_data(ada_data["1d"], ada_data["1h"], ada_data["15m"])
            
            # Create signal objects
            signals = []
            
            if not btc_result.empty:
                btc_latest = btc_result.iloc[-1]
                btc_signal = {
                    "symbol": "BTC",
                    "action": btc_latest.get('signal', 'HOLD'),
                    "confidence": btc_latest.get('combined_trend', 0.0),
                    "entry_price": btc_latest.get('close', 0.0),
                    "atr": btc_latest.get('atr14', 0.0),
                    "timeframe_alignment": btc_latest.get('timeframe_alignment', 0.0)
                }
                signals.append(btc_signal)
            
            if not ada_result.empty:
                ada_latest = ada_result.iloc[-1]
                ada_signal = {
                    "symbol": "ADA",
                    "action": ada_latest.get('signal', 'HOLD'),
                    "confidence": ada_latest.get('combined_trend', 0.0),
                    "entry_price": ada_latest.get('close', 0.0),
                    "atr": ada_latest.get('atr14', 0.0),
                    "timeframe_alignment": ada_latest.get('timeframe_alignment', 0.0)
                }
                signals.append(ada_signal)
            
            # Test signal validation
            valid_signals = 0
            for signal in signals:
                if (abs(signal['confidence']) >= 0.4 and 
                    signal['timeframe_alignment'] >= 0.6 and
                    signal['action'] in ['BUY', 'SELL']):
                    valid_signals += 1
                    logger.info(f"✅ {signal['symbol']} signal valid for execution")
                else:
                    logger.info(f"⏭️ {signal['symbol']} signal below threshold")
            
            details = f"Generated {len(signals)} signals, {valid_signals} valid for execution"
            self.log_test("Signal Pipeline", True, details)
            return True
            
        except Exception as e:
            self.log_test("Signal Pipeline", False, str(e))
            return False
    
    async def run_phase2_tests(self) -> bool:
        """Run all Phase 2 algorithm integration tests."""
        logger.info("🚀 Starting Phase 2: Algorithm Integration Testing")
        logger.info("="*60)
        logger.info("PURPOSE: Test TITAN algorithms with fixed Phemex service")
        logger.info("MODELS: TITAN2K (BTC) + Trend-Tuned (ADA)")
        logger.info("THRESHOLDS: Original backtested (0.4)")
        logger.info("="*60)
        
        tests = [
            ("BTC TITAN2K Signal Generation", self.test_btc_titan2k_signal_generation, True),
            ("ADA Trend-Tuned Signal Generation", self.test_ada_trend_tuned_signal_generation, True),
            ("Confidence Threshold Filtering", self.test_confidence_threshold_filtering, False),
            ("Position Size Calculation", self.test_position_size_calculation, False),
            ("ATR-based Risk Management", self.test_risk_management_calculation, False),
            ("Signal-to-Execution Pipeline", self.test_signal_to_execution_pipeline, True),
        ]
        
        passed = 0
        critical_passed = 0
        total = len(tests)
        critical_total = sum(1 for _, _, critical in tests if critical)
        
        for test_name, test_func, critical in tests:
            logger.info(f"\n🔬 Running: {test_name}")
            try:
                success = await test_func()
                if success:
                    passed += 1
                    if critical:
                        critical_passed += 1
            except Exception as e:
                logger.error(f"❌ {test_name} failed with exception: {str(e)}")
                self.log_test(test_name, False, str(e), critical)
        
        # Final report
        logger.info("\n" + "="*60)
        logger.info("📊 PHASE 2 TEST RESULTS")
        logger.info("="*60)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            critical_flag = " [CRITICAL]" if result["critical"] else ""
            logger.info(f"{status} {test_name}{critical_flag}: {result['details']}")
        
        logger.info(f"\n🎯 SUMMARY:")
        logger.info(f"Total Tests: {passed}/{total} passed")
        logger.info(f"Critical Tests: {critical_passed}/{critical_total} passed")
        
        if self.critical_failures:
            logger.error(f"🚨 CRITICAL FAILURES: {', '.join(self.critical_failures)}")
            logger.error("❌ Phase 2 FAILED - Cannot proceed to Phase 3")
            return False
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED!")
            logger.info("✅ Phase 2 COMPLETE - Ready for Phase 3 (Production Deployment)")
        else:
            logger.info("⚠️  Some non-critical tests failed")
            logger.info("✅ Phase 2 PASSED - Can proceed to Phase 3 with caution")
        
        return critical_passed == critical_total

async def main():
    """Main function."""
    tester = Phase2AlgorithmTester()
    success = await tester.run_phase2_tests()
    
    if success:
        logger.info("\n🚀 READY FOR PHASE 3: Production Deployment")
        logger.info("Next step: Deploy live TITAN trading bot")
    else:
        logger.info("\n🔧 Please resolve critical failures before proceeding")

if __name__ == "__main__":
    asyncio.run(main())
