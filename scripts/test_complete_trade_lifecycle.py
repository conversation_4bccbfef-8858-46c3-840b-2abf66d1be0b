#!/usr/bin/env python3
"""
Complete Trade Lifecycle Test
Demonstrates full trade execution with real stop losses and take profits.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.personal_phemex_service import PersonalPhemexService
from services.live_phemex_data_service import LivePhemexDataService
from services.algorithm_trade_monitor import TradeExecutionEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/complete_trade_lifecycle.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteTradeLifecycleTester:
    """Test complete trade lifecycle with real stop losses and take profits."""
    
    def __init__(self):
        """Initialize the tester."""
        self.phemex_service = PersonalPhemexService()
        self.data_service = LivePhemexDataService()
        self.execution_engine = TradeExecutionEngine()
        
    def create_test_signal(self, symbol: str) -> Dict[str, Any]:
        """Create a test signal with proper risk management."""
        try:
            # Get current price
            current_price = self.data_service.get_current_price(symbol)
            
            if symbol == "BTC":
                # BTC test signal
                atr = current_price * 0.025  # 2.5% ATR estimate
                position_size = 0.001  # Minimum BTC size
                
                signal = {
                    "symbol": symbol,
                    "action": "BUY",
                    "confidence": 0.65,
                    "entry_price": current_price,
                    "atr": atr,
                    "timeframe_alignment": 0.75,
                    "position_size": position_size,
                    "stop_loss": current_price - (atr * 2.5),  # 2.5 ATR stop
                    "take_profit_1": current_price + (atr * 1.5),  # 1.5:1 R:R
                    "take_profit_2": current_price + (atr * 2.5),  # 2.5:1 R:R
                    "take_profit_3": current_price + (atr * 4.0),  # 4:1 R:R
                    "model": "TITAN2K"
                }
                
            else:  # ADA
                # ADA test signal
                atr = current_price * 0.03  # 3% ATR estimate
                position_size = 50.0  # 50 ADA for testing
                
                signal = {
                    "symbol": symbol,
                    "action": "BUY",
                    "confidence": 0.55,
                    "entry_price": current_price,
                    "atr": atr,
                    "timeframe_alignment": 0.70,
                    "position_size": position_size,
                    "stop_loss": current_price - (atr * 2.0),  # 2 ATR stop
                    "take_profit_1": current_price + (atr * 1.2),  # 1.2:1 R:R
                    "take_profit_2": current_price + (atr * 2.0),  # 2:1 R:R
                    "take_profit_3": current_price + (atr * 3.5),  # 3.5:1 R:R
                    "model": "TrendTuned"
                }
            
            logger.info(f"📊 Created {symbol} test signal:")
            logger.info(f"   Entry: ${signal['entry_price']:.4f}")
            logger.info(f"   Stop Loss: ${signal['stop_loss']:.4f}")
            logger.info(f"   Take Profit 1: ${signal['take_profit_1']:.4f}")
            logger.info(f"   Take Profit 2: ${signal['take_profit_2']:.4f}")
            logger.info(f"   Take Profit 3: ${signal['take_profit_3']:.4f}")
            logger.info(f"   Position Size: {signal['position_size']}")
            
            return signal
            
        except Exception as e:
            logger.error(f"Error creating test signal: {str(e)}")
            return {}
    
    async def execute_entry_with_risk_management(self, signal: Dict[str, Any]) -> Optional[str]:
        """Execute entry order and immediately place stop loss and take profits."""
        try:
            symbol = signal['symbol']
            logger.info(f"🚀 Executing {symbol} entry with full risk management")
            
            # Step 1: Place entry order
            logger.info(f"📈 Step 1: Placing {symbol} entry order")
            entry_result = self.phemex_service.place_order(
                symbol=symbol,
                side=signal['action'].lower(),
                amount=signal['position_size'],
                order_type="market"
            )
            
            if not entry_result.get('success'):
                logger.error(f"❌ Entry order failed: {entry_result.get('error')}")
                return None
            
            entry_order_id = entry_result['order_id']
            logger.info(f"✅ Entry order placed: {entry_order_id}")
            
            # Wait a moment for order to fill
            await asyncio.sleep(3)
            
            # Step 2: Place stop loss
            logger.info(f"🛡️ Step 2: Placing {symbol} stop loss")
            stop_result = self.phemex_service.place_stop_loss_order(
                symbol=symbol,
                side='sell',  # Close long position
                amount=signal['position_size'],
                stop_price=signal['stop_loss']
            )
            
            if stop_result.get('success'):
                logger.info(f"✅ Stop loss placed: {stop_result['order_id']}")
                logger.info(f"   Stop Price: ${signal['stop_loss']:.4f}")
            else:
                logger.error(f"❌ Stop loss failed: {stop_result.get('error')}")
            
            # Step 3: Place take profit orders
            logger.info(f"🎯 Step 3: Placing {symbol} take profit orders")
            
            # Take Profit 1 (33% of position)
            tp1_size = signal['position_size'] * 0.33
            tp1_result = self.phemex_service.place_take_profit_order(
                symbol=symbol,
                side='sell',
                amount=tp1_size,
                price=signal['take_profit_1']
            )
            
            if tp1_result.get('success'):
                logger.info(f"✅ Take Profit 1 placed: {tp1_result['order_id']}")
                logger.info(f"   TP1 Price: ${signal['take_profit_1']:.4f} (Size: {tp1_size:.6f})")
            
            # Take Profit 2 (50% of remaining)
            tp2_size = signal['position_size'] * 0.33
            tp2_result = self.phemex_service.place_take_profit_order(
                symbol=symbol,
                side='sell',
                amount=tp2_size,
                price=signal['take_profit_2']
            )
            
            if tp2_result.get('success'):
                logger.info(f"✅ Take Profit 2 placed: {tp2_result['order_id']}")
                logger.info(f"   TP2 Price: ${signal['take_profit_2']:.4f} (Size: {tp2_size:.6f})")
            
            # Take Profit 3 (Remaining position)
            tp3_size = signal['position_size'] * 0.34  # Remaining
            tp3_result = self.phemex_service.place_take_profit_order(
                symbol=symbol,
                side='sell',
                amount=tp3_size,
                price=signal['take_profit_3']
            )
            
            if tp3_result.get('success'):
                logger.info(f"✅ Take Profit 3 placed: {tp3_result['order_id']}")
                logger.info(f"   TP3 Price: ${signal['take_profit_3']:.4f} (Size: {tp3_size:.6f})")
            
            logger.info(f"🎉 {symbol} complete trade setup finished!")
            logger.info(f"   Entry: {entry_order_id}")
            logger.info(f"   Stop Loss: {stop_result.get('order_id', 'Failed')}")
            logger.info(f"   Take Profits: {len([r for r in [tp1_result, tp2_result, tp3_result] if r.get('success')])} placed")
            
            return entry_order_id
            
        except Exception as e:
            logger.error(f"Error executing entry with risk management: {str(e)}")
            return None
    
    async def monitor_and_demonstrate_exit(self, symbol: str, wait_time: int = 60):
        """Monitor position and demonstrate algorithm exit."""
        try:
            logger.info(f"🔍 Monitoring {symbol} position for {wait_time} seconds")
            
            # Wait and monitor
            for i in range(wait_time):
                if i % 10 == 0:  # Log every 10 seconds
                    # Check position status
                    positions = self.phemex_service.get_open_positions()
                    
                    for pos in positions.get('positions', []):
                        if pos['symbol'] == f"{symbol}/USDT:USDT" and pos.get('contracts', 0) != 0:
                            current_price = pos.get('markPrice', 0)
                            unrealized_pnl = pos.get('unrealizedPnl', 0)
                            logger.info(f"📊 {symbol} Status: Price ${current_price:.4f}, PnL ${unrealized_pnl:.2f}")
                
                await asyncio.sleep(1)
            
            # Simulate algorithm exit signal
            logger.info(f"🚨 Simulating algorithm exit signal for {symbol}")
            
            # Create simulated exit signal
            exit_signal = {
                "symbol": symbol,
                "signal": "CLOSE",
                "exit_signal": "ALGORITHM_EXIT",
                "close": self.data_service.get_current_price(symbol),
                "reason": "Test algorithm exit demonstration"
            }
            
            # Demonstrate exit detection
            exit_detected = await self.execution_engine.monitor.check_algorithm_exits(symbol, exit_signal)
            
            if exit_detected:
                logger.info(f"✅ {symbol} algorithm exit detected!")
                
                # Execute emergency exit
                success = await self.execution_engine.monitor.execute_emergency_exit(symbol, "ALGORITHM_EXIT")
                
                if success:
                    logger.info(f"🎉 {symbol} position closed by algorithm exit!")
                else:
                    logger.error(f"❌ {symbol} algorithm exit failed")
            
        except Exception as e:
            logger.error(f"Error monitoring position: {str(e)}")
    
    async def check_final_status(self, symbol: str):
        """Check final status after trade completion."""
        try:
            logger.info(f"📋 Checking final {symbol} status")
            
            # Check positions
            positions = self.phemex_service.get_open_positions()
            position_found = False
            
            for pos in positions.get('positions', []):
                if pos['symbol'] == f"{symbol}/USDT:USDT" and pos.get('contracts', 0) != 0:
                    position_found = True
                    logger.info(f"⚠️ {symbol} position still open:")
                    logger.info(f"   Size: {pos.get('contracts', 0)}")
                    logger.info(f"   PnL: ${pos.get('unrealizedPnl', 0):.2f}")
            
            if not position_found:
                logger.info(f"✅ {symbol} position successfully closed")
            
            # Check open orders
            try:
                orders = self.phemex_service.exchange.fetch_open_orders(f"{symbol}/USDT:USDT")
                if orders:
                    logger.info(f"📋 {symbol} remaining orders: {len(orders)}")
                    for order in orders:
                        logger.info(f"   Order: {order['type']} {order['side']} @ ${order.get('price', 'market')}")
                else:
                    logger.info(f"✅ {symbol} no remaining orders")
            except Exception as e:
                logger.warning(f"⚠️ Error checking {symbol} orders: {str(e)}")
                
        except Exception as e:
            logger.error(f"Error checking final status: {str(e)}")
    
    async def run_complete_lifecycle_test(self, symbol: str = "BTC"):
        """Run complete trade lifecycle test."""
        logger.info("🚀 Starting Complete Trade Lifecycle Test")
        logger.info("="*60)
        logger.info(f"SYMBOL: {symbol}")
        logger.info("FEATURES: Entry + Stop Loss + Take Profits + Algorithm Exit")
        logger.info("PURPOSE: Demonstrate complete trade management")
        logger.info("="*60)
        
        try:
            # Step 1: Create test signal
            logger.info("\n📊 STEP 1: Creating test signal")
            signal = self.create_test_signal(symbol)
            
            if not signal:
                logger.error("❌ Failed to create test signal")
                return False
            
            # Ask for user confirmation
            logger.info(f"\n🚨 READY TO EXECUTE REAL {symbol} TRADE")
            logger.info(f"   Entry: ${signal['entry_price']:.4f}")
            logger.info(f"   Size: {signal['position_size']}")
            logger.info(f"   Stop Loss: ${signal['stop_loss']:.4f}")
            logger.info(f"   Take Profits: 3 levels")
            logger.info(f"   Estimated Value: ${signal['entry_price'] * signal['position_size']:.2f}")
            
            user_input = input(f"\nExecute complete {symbol} trade lifecycle? (y/N): ")
            if user_input.lower() != 'y':
                logger.info("❌ Trade execution cancelled by user")
                return False
            
            # Step 2: Execute entry with risk management
            logger.info(f"\n🚀 STEP 2: Executing {symbol} entry with risk management")
            entry_order_id = await self.execute_entry_with_risk_management(signal)
            
            if not entry_order_id:
                logger.error("❌ Entry execution failed")
                return False
            
            # Step 3: Monitor position
            logger.info(f"\n🔍 STEP 3: Monitoring {symbol} position")
            logger.info("You can now check your Phemex account to see:")
            logger.info("✅ Open position")
            logger.info("✅ Stop loss order")
            logger.info("✅ Multiple take profit orders")
            
            monitor_time = int(input("\nHow many seconds to monitor before algorithm exit? (default 30): ") or "30")
            await self.monitor_and_demonstrate_exit(symbol, monitor_time)
            
            # Step 4: Check final status
            logger.info(f"\n📋 STEP 4: Checking final {symbol} status")
            await asyncio.sleep(3)  # Wait for orders to process
            await self.check_final_status(symbol)
            
            logger.info("\n" + "="*60)
            logger.info("🎉 COMPLETE TRADE LIFECYCLE TEST FINISHED!")
            logger.info("✅ Entry executed")
            logger.info("✅ Stop loss placed")
            logger.info("✅ Take profits placed")
            logger.info("✅ Algorithm exit demonstrated")
            logger.info("✅ Position management complete")
            logger.info("="*60)
            
            return True
            
        except Exception as e:
            logger.error(f"Error in complete lifecycle test: {str(e)}")
            return False

async def main():
    """Main function."""
    tester = CompleteTradeLifecycleTester()
    
    print("🚀 Complete Trade Lifecycle Tester")
    print("This will demonstrate the full trade management system:")
    print("1. Entry order execution")
    print("2. Stop loss placement")
    print("3. Multiple take profit levels")
    print("4. Algorithm exit monitoring")
    print("5. Position closure")
    
    symbol = input("\nChoose symbol (BTC/ADA) [default: BTC]: ").upper() or "BTC"
    
    if symbol not in ["BTC", "ADA"]:
        print("❌ Invalid symbol. Using BTC.")
        symbol = "BTC"
    
    success = await tester.run_complete_lifecycle_test(symbol)
    
    if success:
        print("\n🎉 Complete trade lifecycle demonstrated successfully!")
        print("You've seen the full system in action with real orders.")
    else:
        print("\n🔧 Test incomplete - check logs for details.")

if __name__ == "__main__":
    asyncio.run(main())
