# 🤖 AXON AI Quant Agent Integration Guide

## Quick Reference Card

### Base URL
```
https://axonai-production.up.railway.app/api/v1
```

### Primary Endpoints
| Method | Endpoint | Purpose |
|--------|----------|---------|
| POST | `/signals/tradingview-webhook` | Submit trading signal |
| POST | `/signals/bot-analysis-with-signal` | Submit signal + analysis |
| POST | `/signals/clear-all` | Clear all signals |
| GET | `/signals/bot-analysis` | Get bot analysis |

---

## 🚀 Essential Integration Examples

### 1. Send BTC Signal (TITAN 2K)
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/tradingview-webhook" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTC",
    "type": "BUY",
    "price": 104539.00,
    "target": 146354.60,
    "stop_loss": 90949.23,
    "confidence": "High",
    "strategy": "TITAN2K Multi-Timeframe"
  }'
```

### 2. Send ADA Signal (TITAN Trend Tuned)
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/tradingview-webhook" \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "ADA",
    "type": "SELL",
    "price": 0.663231,
    "target": 0.397939,
    "stop_loss": 0.749451,
    "confidence": "Medium",
    "strategy": "TITAN Trend Tuned"
  }'
```

### 3. Send Signal with Bot Reasoning (RECOMMENDED)
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/bot-analysis-with-signal" \
  -H "Content-Type: application/json" \
  -d '{
    "signal": {
      "symbol": "BTC",
      "type": "BUY",
      "price": 104539.00,
      "confidence": "High",
      "strategy": "TITAN2K"
    },
    "analysis": {
      "bot_name": "titan2k",
      "timestamp": '$(date +%s000)',
      "status": "active",
      "market_condition": "Strong bullish momentum detected",
      "reasoning": "EMA alignment complete, MACD bullish, volatility optimal",
      "confidence_level": 89.5,
      "next_action": "Monitoring trailing stops",
      "risk_assessment": "Medium",
      "timeframe": "1h-4h-daily",
      "symbols_monitored": ["BTC"]
    }
  }'
```

---

## 📊 Bot Specifications

### TITAN 2K (BTC Specialist)
- **Symbol**: BTC only
- **Bot Name**: `"titan2k"`
- **Strategy**: Multi-timeframe analysis
- **Current Price**: $104,539
- **Key Levels**: Support $98,000 | Resistance $110,000

### TITAN Trend Tuned (ADA Specialist)
- **Symbol**: ADA only
- **Bot Name**: `"titan_trend_tuned"`
- **Strategy**: Enhanced trend following
- **Current Price**: $0.663231
- **Key Levels**: Support $0.60 | Resistance $0.75

---

## 🛠️ Testing Commands

### Clear All Signals
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/clear-all"
```

### Add Test Signals
```bash
curl -X POST "https://axonai-production.up.railway.app/api/v1/signals/add-test-signals"
```

### Check Current Bot Analysis
```bash
curl -X GET "https://axonai-production.up.railway.app/api/v1/signals/bot-analysis"
```

### Get Recent Signals
```bash
curl -X GET "https://axonai-production.up.railway.app/api/v1/signals/recent-webhook?hours=24"
```

---

## 📝 Required Fields

### Minimum Signal
```json
{
  "symbol": "BTC",
  "type": "BUY",
  "price": 104539.00
}
```

### Complete Signal
```json
{
  "symbol": "BTC",
  "type": "BUY",
  "price": 104539.00,
  "target": 146354.60,
  "stop_loss": 90949.23,
  "stop_loss_percent": 13.0,
  "take_profit_percent": 40.0,
  "confidence": "High",
  "strategy": "TITAN2K Multi-Timeframe",
  "timeframe": "1h",
  "notes": "Strong bullish alignment"
}
```

---

## ⚡ Response Codes

| Code | Meaning |
|------|---------|
| 200 | Success |
| 400 | Bad Request (invalid data) |
| 500 | Server Error |

### Success Response
```json
{
  "status": "success",
  "message": "Signal received for BTC",
  "signal_id": "BTC-1749233400000"
}
```

---

## 🎯 Integration Checklist

- [ ] Test connection with simple BTC signal
- [ ] Test connection with simple ADA signal  
- [ ] Verify signals appear on AXON AI platform
- [ ] Test signal clearing functionality
- [ ] Test bot analysis submission
- [ ] Implement error handling
- [ ] Set up monitoring/logging

---

## 📞 Quick Support

**API Status**: Production Ready ✅
**Rate Limits**: None currently
**Authentication**: Not required
**Content-Type**: `application/json`

**Test First**: Always test with `/signals/clear-all` and `/signals/add-test-signals`

---

*This guide provides everything needed for quant agents to integrate with AXON AI. Keep this reference handy during development.*
