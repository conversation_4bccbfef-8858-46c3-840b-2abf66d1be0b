# 🚀 NIKE ROCKET MONITORING & AXON INTEGRATION GUIDE

**Generated:** 2025-01-15 13:30:00  
**Purpose:** Complete monitoring system for CALEB's Nike Rocket trading  
**Status:** Ready for deployment  

## 📋 SYSTEM OVERVIEW

### **Three-Component System:**

#### **1. 🚀 Pure Execution Bot** (`nike_rocket_pure_execution_bot.py`)
- **Purpose:** Executes trades on CALEB's accounts
- **Function:** Pure algorithm execution (NO external logic)
- **Accounts:** CALEB_MAIN (BTC), CALEB_SUB1 (ADA)
- **Status:** Untouched and pure

#### **2. 👁️ Signal Monitor Bot** (`nike_rocket_signal_monitor.py`)
- **Purpose:** Parallel monitoring and AXON integration
- **Function:** Watches execution bot trades, sends signals to AXON
- **Operation:** READ-ONLY monitoring, does NOT trade
- **Integration:** Sends signals to AX<PERSON> when execution bot trades

#### **3. 📊 Real-Time Dashboard** (`nike_rocket_dashboard.py`)
- **Purpose:** Visual monitoring of trading activity
- **Function:** Shows positions, P&L, recent trades
- **Operation:** READ-ONLY display, real-time updates
- **Features:** Auto-refresh, system status

## 🎯 HOW TO SEE CALEB'S ACCOUNT RUNNING

### **Option 1: Run Everything Together (RECOMMENDED)**
```bash
cd /Users/<USER>/TomorrowTech/python-backend
source fresh_venv2/bin/activate

# Run both execution bot and signal monitor
python run_nike_rocket_system.py both
```

**What you'll see:**
```
🚀 NIKE ROCKET SYSTEM LAUNCHER
==================================================
⏰ Started: 2025-01-15 13:30:00
🎯 Running both execution bot and signal monitor
📡 Signals will be sent to AXON when trades execute
👁️ Full visibility into CALEB's trading activity
==================================================

🚀 Starting Nike Rocket Pure Execution Bot...
✅ Execution bot started (PID: 12345)

👁️ Starting Nike Rocket Signal Monitor...
✅ Signal monitor started (PID: 12346)

✅ NIKE ROCKET SYSTEM RUNNING
==================================================
🚀 Execution Bot: Trading on CALEB's accounts
👁️ Signal Monitor: Watching trades and sending to AXON
📊 Both bots running in parallel
⏹️ Press Ctrl+C to stop both bots
==================================================
```

### **Option 2: Run Components Separately**

#### **A. Start Execution Bot Only:**
```bash
python run_nike_rocket_system.py execution
# OR
python nike_rocket_pure_execution_bot.py
```

#### **B. Start Signal Monitor Only:**
```bash
python run_nike_rocket_system.py monitor
# OR
python nike_rocket_signal_monitor.py
```

#### **C. View Dashboard:**
```bash
python nike_rocket_dashboard.py
```

## 📡 AXON INTEGRATION

### **How Signals Are Sent to AXON:**

#### **1. Execution Bot Trades:**
```
Execution Bot → Places trade on Phemex → Signal Monitor detects → Sends to AXON
```

#### **2. AXON Signal Format:**
```json
{
    "symbol": "BTC",
    "action": "BUY",
    "price": 45250.00,
    "timestamp": *************,
    "target": 46837.50,
    "stop_loss": 44497.50,
    "source": "Nike_Rocket_massive_rocket",
    "account": "CALEB_MAIN",
    "mode": "live_trade",
    "confidence": 0.8,
    "position_size": 0.001234
}
```

#### **3. AXON Webhook URL:**
```
https://axonai-production.up.railway.app/api/v1/signals/tradingview-webhook
```

### **Signal Monitor Logs:**
```
🚀 EXECUTION BOT TRADE DETECTED: CALEB_MAIN
   Symbol: BTC/USDT:USDT
   Side: long
   Size: 0.001234
   Entry: $45,250.0000

📡 Sending signal to AXON: CALEB_MAIN BUY BTC
✅ Signal sent to AXON successfully: CALEB_MAIN
```

## 👁️ REAL-TIME MONITORING

### **Dashboard Features:**

#### **Account Overview:**
```
📊 CALEB_MAIN - Massive Rocket BTC
------------------------------------------------------------
💰 Balance: $485.50 USDT (Free: $450.00, Used: $35.50)
📈 Position: 🟢 LONG 0.001234 @ $45,250.0000
💵 P&L: $12.50 (+2.57%) | Mark: $45,500.0000
📋 Recent Trades:
   🟢 BUY 0.001234 @ $45,250.0000 (14:25:30)
   🔴 SELL 0.001100 @ $44,800.0000 (13:45:15)
```

#### **System Status:**
```
🎯 System Status:
   🚀 Execution Bot: Running (trading on accounts)
   👁️ Signal Monitor: Running (sending to AXON)
   📡 AXON Integration: Active
   ⏹️ Press Ctrl+C to stop dashboard
```

## 🔧 DEPLOYMENT STEPS

### **Step 1: Ensure Accounts Are Funded**
```
CALEB_MAIN: 500 USDT (for BTC trading)
CALEB_SUB1: 500 USDT (for ADA trading)
```

### **Step 2: Start Complete System**
```bash
cd /Users/<USER>/TomorrowTech/python-backend
source fresh_venv2/bin/activate

# Start both bots
python run_nike_rocket_system.py both
```

### **Step 3: Open Dashboard (New Terminal)**
```bash
cd /Users/<USER>/TomorrowTech/python-backend
source fresh_venv2/bin/activate

# View real-time dashboard
python nike_rocket_dashboard.py
# Choose option 1 for real-time updates
```

### **Step 4: Monitor AXON Integration**
- Check AXON frontend for incoming signals
- Verify signals match execution bot trades
- Confirm timestamp and price accuracy

## 📊 WHAT YOU'LL SEE

### **Execution Bot Logs:**
```
🚀 NIKE ROCKET PURE EXECUTION BOT
💰 CALEB's Configuration: 500 USDT per account
🎯 Pure execution - NO external trading logic

✅ CALEB_MAIN: massive_rocket BTC ($500 USDT)
✅ CALEB_SUB1: massive_rocket ADA ($500 USDT)

🎯 Algorithm signal: CALEB_MAIN BUY (mode: aggressive, size: 0.001234, leverage: 5.2x)

🚀 Executing PURE algorithm trade: CALEB_MAIN
   Signal: BUY
   Mode: aggressive
   Position Size: 0.001234
   Entry Price: $45,250.00
   Stop Loss: $44,500.00 (HARD)
   Take Profit: $46,750.00 (HARD)
   Leverage: 5.2x

✅ Pure algorithm trade executed: CALEB_MAIN
   Entry Order: ********
   Hard Stop Loss: ********
   Hard Take Profit: ********
```

### **Signal Monitor Logs:**
```
👁️ NIKE ROCKET SIGNAL MONITOR
📡 Parallel monitoring bot - does NOT execute trades
🎯 Watches execution bot and sends signals to AXON

👁️ Monitoring execution bot trades: CALEB_MAIN
📊 Generating parallel signals: CALEB_MAIN BTC

🚀 EXECUTION BOT TRADE DETECTED: CALEB_MAIN
   Symbol: BTC/USDT:USDT
   Side: long
   Size: 0.001234
   Entry: $45,250.0000

📡 Sending signal to AXON: CALEB_MAIN BUY BTC
✅ Signal sent to AXON successfully: CALEB_MAIN
```

### **Dashboard Display:**
```
🚀 NIKE ROCKET REAL-TIME DASHBOARD
================================================================================
⏰ 2025-01-15 14:25:30 | 🔄 Auto-refresh every 10s
================================================================================

📊 CALEB_MAIN - Massive Rocket BTC
------------------------------------------------------------
💰 Balance: $485.50 USDT (Free: $450.00, Used: $35.50)
📈 Position: 🟢 LONG 0.001234 @ $45,250.0000
💵 P&L: $12.50 (+2.57%) | Mark: $45,500.0000
📋 Recent Trades:
   🟢 BUY 0.001234 @ $45,250.0000 (14:25:30)

📊 CALEB_SUB1 - Massive Rocket ADA
------------------------------------------------------------
💰 Balance: $500.00 USDT (Free: $500.00, Used: $0.00)
💤 No active positions
📋 No recent trades

================================================================================
🎯 System Status:
   🚀 Execution Bot: Running (trading on accounts)
   👁️ Signal Monitor: Running (sending to AXON)
   📡 AXON Integration: Active
   ⏹️ Press Ctrl+C to stop dashboard
================================================================================
```

## 🛡️ SAFETY FEATURES

### **Execution Bot Protection:**
- **Pure algorithm execution** - no external interference
- **Hard TP/SL levels** - no trailing stops
- **Atomic order placement** - Entry + SL + TP together
- **500 USDT optimized** - position sizing for smaller accounts

### **Signal Monitor Safety:**
- **READ-ONLY monitoring** - cannot execute trades
- **Parallel operation** - does not interfere with execution bot
- **Error isolation** - monitor failures don't affect trading
- **AXON integration** - only sends signals when trades actually execute

### **System Monitoring:**
- **Process monitoring** - automatic restart if components fail
- **Real-time dashboard** - visual confirmation of activity
- **Complete logging** - full audit trail of all activity
- **Graceful shutdown** - Ctrl+C stops all components safely

## 🎉 CONCLUSION

**This system gives you complete visibility into CALEB's Nike Rocket trading while maintaining the purity of the execution bot:**

### ✅ **Perfect Solution:**
- **See execution bot running** - Real-time dashboard and logs
- **AXON integration** - Signals sent when trades execute
- **Parallel monitoring** - No interference with execution bot
- **Complete visibility** - Positions, P&L, trades, system status
- **Pure execution maintained** - No external logic added to trading bot

### 🚀 **Ready to Deploy:**
```bash
# Start everything
python run_nike_rocket_system.py both

# View dashboard (new terminal)
python nike_rocket_dashboard.py
```

**You'll have full visibility into CALEB's trading activity while the execution bot remains pure and the signals flow to AXON automatically!** 🚀
