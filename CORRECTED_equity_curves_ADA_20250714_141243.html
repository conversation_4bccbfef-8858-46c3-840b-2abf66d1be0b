
<!DOCTYPE html>
<html>
<head>
    <title>CORRECTED CALEB's ADA Equity Curves - PT Adjusted WINS!</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .chart-container { width: 100%; height: 600px; margin: 20px 0; }
        .summary { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .winner { background: #d4edda; border: 2px solid #28a745; }
        .metric { display: inline-block; margin: 10px 20px; }
        .highlight { color: #28a745; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🏆 CORRECTED CALEB's ADA Algorithm Comparison - PT ADJUSTED WINS!</h1>
    
    <div class="summary winner">
        <h3>🚀 NEW CHAMPION DISCOVERED - CONSERVATIVE 0.2 VOL15!</h3>
        <p><strong>CALEB's latest algorithm is REVOLUTIONARY!</strong> Conservative 0.2 Vol15 delivers astronomical returns with reasonable drawdowns - the perfect balance!</p>
    </div>
    
    <div class="summary">
        <h3>📊 CORRECTED Performance Summary:</h3>

        <div class="metric">
            <strong class="highlight">🏆 Conservative 02 Vol15:</strong><br>
            <span class="highlight">Return: 520,270,414.15%</span><br>
            Max DD: -30.80%<br>
            Trades: 2140
        </div>

        <div class="metric">
            <strong class="">🥈 Aggressive Only:</strong><br>
            <span class="">Return: 54,200,898.22%</span><br>
            Max DD: -55.92%<br>
            Trades: 1307
        </div>

        <div class="metric">
            <strong class="">PT Adjusted:</strong><br>
            <span class="">Return: 386,559.22%</span><br>
            Max DD: -29.80%<br>
            Trades: 925
        </div>

        <div class="metric">
            <strong class="">Confidence Mode:</strong><br>
            <span class="">Return: 364,374.49%</span><br>
            Max DD: -35.98%<br>
            Trades: 855
        </div>

    </div>
    
    <div class="chart-container" id="equityChart"></div>
    <div class="chart-container" id="drawdownChart"></div>
    
    <script>

        var PT_Adjusted_dates = ['2020-10-15 17:00:00', '2020-10-28 05:00:00', '2020-11-03 21:00:00', '2020-11-09 23:00:00', '2020-11-17 05:00:00', '2020-11-22 17:00:00', '2020-11-27 01:00:00', '2020-12-01 17:00:00', '2020-12-06 16:00:00', '2020-12-11 16:00:00', '2020-12-17 12:00:00', '2020-12-22 01:00:00', '2020-12-26 15:00:00', '2020-12-30 23:00:00', '2021-01-04 12:00:00', '2021-01-08 16:00:00', '2021-01-12 20:00:00', '2021-01-17 00:00:00', '2021-01-21 04:00:00', '2021-01-25 08:00:00', '2021-01-29 12:00:00', '2021-02-02 19:00:00', '2021-02-06 23:00:00', '2021-02-11 03:00:00', '2021-02-15 07:00:00', '2021-02-19 11:00:00', '2021-02-23 15:00:00', '2021-02-27 19:00:00', '2021-03-03 23:00:00', '2021-03-08 03:00:00', '2021-03-12 07:00:00', '2021-03-16 11:00:00', '2021-03-20 15:00:00', '2021-03-24 19:00:00', '2021-03-28 23:00:00', '2021-04-02 03:00:00', '2021-04-06 08:00:00', '2021-04-10 12:00:00', '2021-04-14 16:00:00', '2021-04-18 20:00:00', '2021-04-23 00:00:00', '2021-04-27 04:00:00', '2021-05-01 08:00:00', '2021-05-05 12:00:00', '2021-05-09 16:00:00', '2021-05-13 20:00:00', '2021-05-18 00:00:00', '2021-05-22 04:00:00', '2021-05-26 08:00:00', '2021-05-30 12:00:00', '2021-06-03 16:00:00', '2021-06-07 20:00:00', '2021-06-12 00:00:00', '2021-06-16 04:00:00', '2021-06-20 08:00:00', '2021-06-24 12:00:00', '2021-06-28 16:00:00', '2021-07-02 20:00:00', '2021-07-07 00:00:00', '2021-07-11 04:00:00', '2021-07-15 08:00:00', '2021-07-19 13:00:00', '2021-07-23 22:00:00', '2021-07-28 02:00:00', '2021-08-01 06:00:00', '2021-08-05 10:00:00', '2021-08-09 14:00:00', '2021-08-13 18:00:00', '2021-08-17 22:00:00', '2021-08-22 02:00:00', '2021-08-26 06:00:00', '2021-08-30 10:00:00', '2021-09-03 14:00:00', '2021-09-07 18:00:00', '2021-09-11 22:00:00', '2021-09-16 02:00:00', '2021-09-20 06:00:00', '2021-09-24 10:00:00', '2021-09-28 14:00:00', '2021-10-02 18:00:00', '2021-10-06 22:00:00', '2021-10-11 02:00:00', '2021-10-15 06:00:00', '2021-10-19 10:00:00', '2021-10-23 15:00:00', '2021-10-27 19:00:00', '2021-10-31 23:00:00', '2021-11-05 03:00:00', '2021-11-09 07:00:00', '2021-11-13 11:00:00', '2021-11-17 15:00:00', '2021-11-21 19:00:00', '2021-11-25 23:00:00', '2021-11-30 03:00:00', '2021-12-04 07:00:00', '2021-12-08 11:00:00', '2021-12-12 15:00:00', '2021-12-16 20:00:00', '2021-12-21 00:00:00', '2021-12-25 04:00:00', '2021-12-29 08:00:00', '2022-01-02 12:00:00', '2022-01-06 16:00:00', '2022-01-10 20:00:00', '2022-01-15 00:00:00', '2022-01-19 04:00:00', '2022-01-23 08:00:00', '2022-01-27 12:00:00', '2022-01-31 16:00:00', '2022-02-04 20:00:00', '2022-02-09 00:00:00', '2022-02-13 04:00:00', '2022-02-17 08:00:00', '2022-02-21 12:00:00', '2022-02-25 16:00:00', '2022-03-01 20:00:00', '2022-03-06 00:00:00', '2022-03-10 04:00:00', '2022-03-14 08:00:00', '2022-03-18 12:00:00', '2022-03-22 16:00:00', '2022-03-26 20:00:00', '2022-03-31 00:00:00', '2022-04-04 04:00:00', '2022-04-08 08:00:00', '2022-04-12 12:00:00', '2022-04-16 16:00:00', '2022-04-20 20:00:00', '2022-04-25 01:00:00', '2022-04-29 06:00:00', '2022-05-03 10:00:00', '2022-05-07 14:00:00', '2022-05-11 18:00:00', '2022-05-15 22:00:00', '2022-05-20 02:00:00', '2022-05-24 06:00:00', '2022-05-28 11:00:00', '2022-06-01 15:00:00', '2022-06-05 19:00:00', '2022-06-09 23:00:00', '2022-06-14 03:00:00', '2022-06-18 07:00:00', '2022-06-22 11:00:00', '2022-06-26 15:00:00', '2022-06-30 19:00:00', '2022-07-04 23:00:00', '2022-07-09 03:00:00', '2022-07-13 07:00:00', '2022-07-17 13:00:00', '2022-07-21 17:00:00', '2022-07-25 21:00:00', '2022-07-30 01:00:00', '2022-08-03 05:00:00', '2022-08-07 10:00:00', '2022-08-11 15:00:00', '2022-08-15 19:00:00', '2022-08-19 23:00:00', '2022-08-24 04:00:00', '2022-08-28 12:00:00', '2022-09-01 17:00:00', '2022-09-05 22:00:00', '2022-09-10 02:00:00', '2022-09-14 07:00:00', '2022-09-18 14:00:00', '2022-09-22 20:00:00', '2022-09-27 01:00:00', '2022-10-01 05:00:00', '2022-10-05 11:00:00', '2022-10-09 16:00:00', '2022-10-13 22:00:00', '2022-10-18 08:00:00', '2022-10-22 16:00:00', '2022-10-26 20:00:00', '2022-10-31 02:00:00', '2022-11-04 07:00:00', '2022-11-08 11:00:00', '2022-11-12 15:00:00', '2022-11-16 21:00:00', '2022-11-21 04:00:00', '2022-11-25 09:00:00', '2022-11-29 16:00:00', '2022-12-03 20:00:00', '2022-12-08 04:00:00', '2022-12-12 18:00:00', '2022-12-17 00:00:00', '2022-12-21 06:00:00', '2022-12-25 17:00:00', '2022-12-30 04:00:00', '2023-01-03 12:00:00', '2023-01-07 16:00:00', '2023-01-11 20:00:00', '2023-01-16 00:00:00', '2023-01-20 05:00:00', '2023-01-24 09:00:00', '2023-01-28 13:00:00', '2023-02-01 17:00:00', '2023-02-05 22:00:00', '2023-02-10 02:00:00', '2023-02-14 07:00:00', '2023-02-18 14:00:00', '2023-02-22 20:00:00', '2023-02-27 01:00:00', '2023-03-03 06:00:00', '2023-03-07 15:00:00', '2023-03-11 20:00:00', '2023-03-16 00:00:00', '2023-03-20 04:00:00', '2023-03-24 08:00:00', '2023-03-28 14:00:00', '2023-04-01 18:00:00', '2023-04-05 22:00:00', '2023-04-10 02:00:00', '2023-04-14 06:00:00', '2023-04-18 10:00:00', '2023-04-22 15:00:00', '2023-04-26 20:00:00', '2023-05-01 02:00:00', '2023-05-05 07:00:00', '2023-05-09 15:00:00', '2023-05-13 20:00:00', '2023-05-18 00:00:00', '2023-05-22 05:00:00', '2023-05-26 09:00:00', '2023-05-30 14:00:00', '2023-06-03 19:00:00', '2023-06-08 00:00:00', '2023-06-12 04:00:00', '2023-06-16 09:00:00', '2023-06-20 16:00:00', '2023-06-24 22:00:00', '2023-06-29 03:00:00', '2023-07-03 08:00:00', '2023-07-07 13:00:00', '2023-07-11 18:00:00', '2023-07-16 00:00:00', '2023-07-20 04:00:00', '2023-07-24 10:00:00', '2023-07-28 16:00:00', '2023-08-01 22:00:00', '2023-08-06 04:00:00', '2023-08-10 10:00:00', '2023-08-14 17:00:00', '2023-08-18 21:00:00', '2023-08-23 01:00:00', '2023-08-27 07:00:00', '2023-08-31 11:00:00', '2023-09-04 17:00:00', '2023-09-08 23:00:00', '2023-09-13 06:00:00', '2023-09-17 14:00:00', '2023-09-21 21:00:00', '2023-09-26 06:00:00', '2023-09-30 11:00:00', '2023-10-04 15:00:00', '2023-10-08 21:00:00', '2023-10-13 02:00:00', '2023-10-17 12:00:00', '2023-10-21 17:00:00', '2023-10-25 22:00:00', '2023-10-30 02:00:00', '2023-11-03 06:00:00', '2023-11-07 10:00:00', '2023-11-11 14:00:00', '2023-11-15 18:00:00', '2023-11-19 22:00:00', '2023-11-24 02:00:00', '2023-11-28 06:00:00', '2023-12-02 10:00:00', '2023-12-06 14:00:00', '2023-12-10 18:00:00', '2023-12-14 22:00:00', '2023-12-19 02:00:00', '2023-12-23 06:00:00', '2023-12-27 10:00:00', '2023-12-31 14:00:00', '2024-01-04 18:00:00', '2024-01-09 00:00:00', '2024-01-13 04:00:00', '2024-01-17 09:00:00', '2024-01-21 20:00:00', '2024-01-26 00:00:00', '2024-01-30 05:00:00', '2024-02-03 09:00:00', '2024-02-07 14:00:00', '2024-02-11 18:00:00', '2024-02-15 22:00:00', '2024-02-20 02:00:00', '2024-02-24 06:00:00', '2024-02-28 10:00:00', '2024-03-03 14:00:00', '2024-03-07 18:00:00', '2024-03-11 22:00:00', '2024-03-16 02:00:00', '2024-03-20 06:00:00', '2024-03-24 10:00:00', '2024-03-28 14:00:00', '2024-04-01 18:00:00', '2024-04-05 22:00:00', '2024-04-10 02:00:00', '2024-04-14 12:00:00', '2024-04-18 16:00:00', '2024-04-22 20:00:00', '2024-04-27 00:00:00', '2024-05-01 04:00:00', '2024-05-05 08:00:00', '2024-05-09 12:00:00', '2024-05-13 20:00:00', '2024-05-18 00:00:00', '2024-05-22 04:00:00', '2024-05-26 08:00:00', '2024-05-30 12:00:00', '2024-06-03 16:00:00', '2024-06-07 21:00:00', '2024-06-12 01:00:00', '2024-06-16 05:00:00', '2024-06-20 09:00:00', '2024-06-24 15:00:00', '2024-06-28 19:00:00', '2024-07-03 00:00:00', '2024-07-07 04:00:00', '2024-07-11 09:00:00', '2024-07-15 13:00:00', '2024-07-19 17:00:00', '2024-07-23 21:00:00', '2024-07-28 02:00:00', '2024-08-01 06:00:00', '2024-08-05 12:00:00', '2024-08-09 16:00:00', '2024-08-13 20:00:00', '2024-08-18 00:00:00', '2024-08-22 04:00:00', '2024-08-26 08:00:00', '2024-08-30 12:00:00', '2024-09-03 18:00:00', '2024-09-07 22:00:00', '2024-09-12 02:00:00', '2024-09-16 07:00:00', '2024-09-20 13:00:00', '2024-09-24 18:00:00', '2024-09-28 22:00:00', '2024-10-03 02:00:00', '2024-10-07 06:00:00', '2024-10-11 10:00:00', '2024-10-15 14:00:00', '2024-10-19 18:00:00', '2024-10-23 22:00:00', '2024-10-28 02:00:00', '2024-11-01 06:00:00', '2024-11-05 10:00:00', '2024-11-09 14:00:00', '2024-11-13 18:00:00', '2024-11-17 22:00:00', '2024-11-22 02:00:00', '2024-11-26 06:00:00', '2024-11-30 10:00:00', '2024-12-04 14:00:00', '2024-12-08 18:00:00', '2024-12-12 22:00:00', '2024-12-17 02:00:00', '2024-12-21 06:00:00', '2024-12-25 10:00:00', '2024-12-29 14:00:00', '2025-01-02 18:00:00', '2025-01-06 22:00:00', '2025-01-11 02:00:00', '2025-01-15 06:00:00', '2025-01-19 10:00:00', '2025-01-23 14:00:00', '2025-01-27 21:00:00', '2025-02-01 01:00:00', '2025-02-05 05:00:00', '2025-02-09 09:00:00', '2025-02-13 13:00:00', '2025-02-17 17:00:00', '2025-02-21 21:00:00', '2025-02-26 01:00:00', '2025-03-02 05:00:00', '2025-03-06 09:00:00', '2025-03-10 13:00:00', '2025-03-14 17:00:00', '2025-03-18 21:00:00', '2025-03-23 01:00:00', '2025-03-27 05:00:00', '2025-03-31 10:00:00'];
        var PT_Adjusted_equity = [10000.0, 10000.0, 10000.0, 10000.0, 10933.333333333338, 13529.732503802788, 12474.196892786107, 13429.841457644694, 13161.2446284918, 13161.2446284918, 13949.9249010516, 13670.92640303057, 13670.92640303057, 13910.543109862145, 15462.159216986078, 17401.361584055492, 16712.267665326894, 17086.816298378148, 17575.635939042146, 17575.635939042146, 17575.635939042146, 17933.81221413037, 19361.82710456424, 23710.045677648923, 23235.844764095946, 23235.844764095946, 23917.675410061493, 27667.59132663468, 27667.59132663468, 27667.59132663468, 27114.239500101983, 27114.239500101983, 29072.78898983806, 28491.3332100413, 26804.646284006856, 26268.55335832672, 26268.55335832672, 24713.45499951378, 26625.78125813112, 25571.400320309127, 25571.400320309127, 26764.73233525689, 28103.968168693584, 27890.752730187098, 28688.65138429229, 28471.000149123454, 31237.86508948223, 30613.10778769259, 31400.885094762543, 30157.410045009947, 28951.113643209548, 27804.64954293845, 27938.11186074456, 29241.89041424597, 29754.426673752, 30131.31607828619, 29528.689756720465, 29528.689756720465, 28938.115961586056, 28938.115961586056, 31298.18008556429, 36119.10324232226, 36307.59680244285, 36307.59680244285, 38001.95131989018, 36220.18291251457, 43272.5837122018, 47909.2724953426, 48294.26374363156, 57268.05055588997, 64740.40786519271, 67979.84523367931, 72838.13817304629, 71381.37540958537, 71381.37540958537, 71381.37540958537, 73760.75458990489, 73760.75458990489, 73760.75458990489, 74353.48421206867, 74353.48421206867, 74353.48421206867, 74353.48421206867, 74353.48421206867, 72866.41452782729, 72866.41452782729, 71409.08623727075, 71409.08623727075, 69980.90451252533, 69980.90451252533, 69980.90451252533, 70227.23729640942, 79978.74505655558, 83075.99838300486, 89830.32320264947, 93902.63118783625, 92764.07335348793, 102706.73287789992, 102706.73287789992, 102706.73287789992, 102706.73287789992, 102706.73287789992, 99113.0160779636, 111470.87875810076, 102774.36668090876, 102774.36668090876, 107570.5037926845, 105790.16892671405, 103674.36554817976, 99568.86067247184, 99568.86067247184, 97577.4834590224, 102000.9960424981, 114939.97467570948, 124195.72686350136, 119277.5760797067, 112216.34357578808, 114183.21179726788, 115629.53248003327, 113316.9418304326, 118577.36608562755, 120079.3460560455, 117677.7591349246, 117677.7591349246, 108542.2181662036, 121726.67256340184, 119292.1391121338, 114520.45354764846, 138008.17442044325, 169217.1818721174, 213419.4531794383, 204968.04283353253, 269441.0960934102, 258771.2286881112, 266174.1559984207, 269545.6953077341, 283051.4517656228, 277390.42273031035, 277390.42273031035, 277390.42273031035, 277390.42273031035, 266405.7619901901, 268546.5560686213, 263175.62494724884, 264212.0456483745, 258927.804735407, 258927.804735407, 317576.2539750224, 292800.224944907, 292800.224944907, 286944.22044600884, 275581.2293163469, 270069.6047300199, 270069.6047300199, 300451.5431922353, 330207.98627923446, 357138.2820491186, 333090.1540339507, 333090.1540339507, 348190.2410168232, 348190.2410168232, 364439.1189309416, 343007.18322485074, 316247.1348183808, 306574.8781041908, 300443.380542107, 302011.2943973627, 284132.22576903884, 267422.9778360132, 437290.5163886159, 414708.8858415318, 651278.6337264722, 638253.0610519428, 638253.0610519428, 638253.0610519428, 646337.5998252673, 707168.0221092668, 716272.394601756, 847166.3950506508, 817864.610104148, 817864.610104148, 817864.610104148, 817864.610104148, 801507.3179020651, 924666.3386617705, 859020.4195546906, 792003.0825027118, 764290.8312856958, 770432.5501193742, 770432.5501193742, 796113.6351233535, 843820.3024227676, 843820.3024227676, 788171.6107927386, 794836.2755961734, 778939.5500842499, 748093.5439009137, 748093.5439009137, 733131.6730228954, 733131.6730228954, 733131.6730228954, 703806.4061019795, 723940.8997677447, 869536.7017540442, 926236.5851508498, 950071.7399420652, 931070.305143224, 912448.8990403597, 912448.8990403597, 958105.4087512746, 938943.3005762493, 901761.1458734296, 1071898.0606128047, 980366.678623908, 1005594.78115383, 1005594.78115383, 1005594.78115383, 1005594.78115383, 1065854.4897507057, 1146047.4942865332, 1123126.5444008026, 1056637.452972275, 1056637.452972275, 1056637.452972275, 1056637.452972275, 1132961.8044019763, 1254129.9493483857, 1343758.43639515, 1337575.5083882683, 1337575.5083882683, 1399995.6987797203, 1371995.784804126, 1389374.398078312, 1361586.9101167456, 1361586.9101167456, 1361586.9101167456, 1334355.1719144108, 1334355.1719144108, 1334355.1719144108, 1341318.7267715742, 1437178.3051115165, 1578857.2586671514, 2032525.5913149675, 2032525.5913149675, 2032525.5913149675, 1991875.079488668, 1912996.8263409168, 1874736.889814098, 1815507.8677906855, 1742887.5530790584, 1673869.205977128, 1640391.821857585, 1575432.305712025, 1595387.7815843774, 1563480.0259526898, 1601670.631386628, 1507479.5848960432, 1635327.6715435025, 1766444.6101863682, 1788819.575248729, 1803194.243144297, 1994330.3888646923, 2092078.3180766143, 2108889.924708147, 2050178.4292042723, 1928807.8661953795, 1928807.8661953795, 1928807.8661953795, 2123561.585764031, 2669049.442221443, 2978870.997048478, 2919293.5771075087, 2839202.9524391354, 2782418.893390353, 2854019.806246932, 2796939.410121993, 2796939.410121993, 2796939.410121993, 2796939.410121993, 3162180.7471128786, 3279088.677454141, 3279088.677454141, 3294828.30310592, 3294828.30310592, 3447781.5547767705, 3438048.182431026, 3186367.677665146, 3122640.324111843, 3732734.851436098, 3871530.616749765, 3904267.71801496, 4138228.791979385, 3862099.682314253, 3961484.3808058063, 3882254.6931896894, 3882254.6931896894, 4011663.1829626793, 4552979.321139862, 4552979.321139862, 5592398.1469248, 5754782.300392489, 5414099.188209253, 5414099.188209253, 5553422.00731917, 5553422.00731917, 5468476.864295214, 5537744.23790962, 5680248.856298493, 5680248.856298493, 5680248.856298493, 5680248.856298493, 5680248.856298493, 5680248.856298493, 5943938.630983106, 5974958.065385331, 6892421.323142067, 8244941.887701182, 8182390.261913155, 7858367.607541394, 7795500.666681061, 7242065.568238144, 7822718.292020436, 7822718.292020436, 7822718.292020436, 8449926.460856207, 9282931.47793603, 10293786.90305315, 9688429.8828584, 10459890.07060059, 10501082.512350624, 10501082.512350624, 11343035.97245198, 11343035.97245198, 14231750.105449593, 14414975.520243607, 14414975.520243607, 14414975.520243607, 14414975.520243607, 14414975.520243607, 16548980.284507113, 16758600.701444205, 17160807.118278865, 20103374.528362826, 19701307.03779557, 21270062.162197664, 20427767.70057464, 21054018.77889856, 19411468.44984401, 20291455.01957027, 20291455.01957027, 21313148.718496986, 23614422.153086346, 28002330.233047776, 30147580.53818469, 31794786.45543648, 30535712.9118012, 29326498.68049387, 31422365.78619316, 31422365.78619316, 32469777.97906626, 32469777.97906626, 31820382.419484936, 30947392.349165197, 34706523.957430184, 33478190.66152882, 32808626.84829824, 39612604.9836962, 39612604.9836962, 39612604.9836962, 39612604.9836962, 40323708.14445989, 37177813.72986171, 39834788.15108915, 39834788.15108915, 39834788.15108915, 41684007.53881416, 42149318.12497936, 40480205.127230175, 38083776.98369815, 40365926.16179237, 35728068.543699816, 35013507.17282582, 37515805.81877711, 38665922.37544516];
        var PT_Adjusted_drawdown = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -7.801600000000008, -2.0000000000000058, -3.960000000000002, -3.960000000000002, -1.9999999999999976, -3.9599999999999977, -3.9599999999999977, -5.919999999999996, -2.000000000000001, -3.959999999999993, -7.763183999999998, -5.696009512274709, -5.920000000000014, -5.920000000000014, -5.920000000000014, -4.002730885120041, -5.919999999999993, -2.000000000000004, -3.959999999999998, -3.959999999999998, -7.801599999999997, -3.959999999999996, -3.959999999999996, -3.959999999999996, -5.880799999999994, -5.880799999999994, -3.959999999999994, -5.880799999999994, -11.452656639999988, -13.223603507199982, -13.223603507199982, -18.360766179573744, -12.043525204110232, -15.526601606027468, -15.526601606027468, -11.584509680975406, -7.160434320052265, -7.864777158344125, -5.228972703290034, -5.947968897047746, -5.919999999999997, -7.801599999999992, -5.4290278399999945, -9.174038337535997, -12.807076804034557, -16.259916562594782, -15.857964162095236, -11.931335822993006, -10.387715247252627, -9.252626307051164, -11.06757378091014, -11.06757378091014, -12.846222305291937, -12.846222305291937, -5.738347546634651, 0.0, -3.96, -3.96, 0.0, -4.688623466666656, 0.0, -4.000000000000004, -7.801600000000007, 0.0, -5.880799999999999, -3.960000000000001, -2.0000000000000027, -3.959999999999999, -3.959999999999999, -3.959999999999999, -0.7586666666666553, -0.7586666666666553, -0.7586666666666553, -7.801599999999992, -7.801599999999992, -7.801599999999992, -7.801599999999992, -7.801599999999992, -9.645567999999994, -9.645567999999994, -11.452656639999988, -11.452656639999988, -13.223603507199991, -13.223603507199991, -13.223603507199991, -12.918150591545333, -3.959999999999993, -2.0000000000000084, 0.0, -2.000000000000004, -7.801600000000001, 0.0, 0.0, 0.0, 0.0, 0.0, -4.000000000000002, 0.0, -7.801600000000001, -7.801600000000001, -3.499008000000006, -7.801599999999999, -9.645568, -13.223603507200004, -13.223603507200004, -14.959131437056003, -11.103945395535868, -2.000000000000003, -5.880799999999999, -9.60792032, -14.959131437056003, -13.468580448014636, -12.372515800356156, -14.125065484349031, -10.138560190944723, -9.000315286696694, -10.820308980962755, -10.820308980962755, -17.743492485402083, -7.751922472171134, -9.596884022727716, -13.213008661818604, 0.0, -2.0000000000000004, -1.9999999999999931, -5.880800000000004, 0.0, -3.959999999999998, -4.000000000000009, -2.784000000000004, -1.5525973333333418, -3.521545386666681, -3.521545386666681, -3.521545386666681, -3.521545386666681, -7.342092189354677, -7.801600000000006, -9.645568000000011, -9.289740199526396, -11.103945395535863, -11.103945395535863, 0.0, -7.801600000000016, -7.801600000000016, -9.645568000000026, -13.22360350720003, -14.959131437056032, -14.959131437056032, -5.392314623162598, -2.0000000000000058, 0.0, -12.562714471510835, -12.562714471510835, -8.598890860885987, -8.598890860885987, -4.333505767727335, -9.959460960538827, -16.984063654241425, -19.523063566971587, -21.13260229563216, -20.721019636145645, -25.41433527368582, -29.8005690449109, 0.0, -11.489535999999994, -3.959999999999996, -5.880799999999988, -5.880799999999988, -5.880799999999988, -4.68862346666666, -1.999999999999995, -11.489536000000005, 0.0, -5.880799999999992, -5.880799999999992, -5.880799999999992, -5.880799999999992, -7.763183999999988, 0.0, -9.6832, -16.729355468800005, -19.643001982598207, -18.997266003650097, -18.997266003650097, -16.29717487043842, -11.28132957611895, -11.28132957611895, -17.132193697389138, -16.431475548650727, -18.10284603767771, -21.34597333458567, -21.34597333458567, -22.91905386789396, -22.91905386789396, -22.91905386789396, -26.0022917131782, -23.885365274508832, -8.577525519485388, -2.616139838540488, -0.1101285037189048, -2.1079259336445206, -4.065767414971624, -4.065767414971624, -1.999999999999999, -3.959999999999993, -7.763184000000001, 0.0, -9.683200000000005, -7.359047680000002, -7.359047680000002, -7.359047680000002, -7.359047680000002, -1.9999999999999896, -5.919999999999996, -7.801599999999993, -13.25974528, -13.25974528, -13.25974528, -13.25974528, -6.994215257636882, -3.999999999999997, 0.0, -3.999999999999999, -3.999999999999999, 0.0, -1.999999999999995, -1.999999999999996, -3.960000000000001, -3.960000000000001, -3.960000000000001, -5.880799999999996, -5.880799999999996, -5.880799999999996, -5.389623268266702, -1.9999999999999976, -3.9999999999999982, 0.0, 0.0, 0.0, -2.000000000000001, -5.880800000000005, -7.763184000000009, -10.677244333434407, -14.250154560097023, -17.645848439517174, -19.292931470726828, -22.48893138448605, -21.5071245153562, -23.07698202504908, -21.198009105980912, -25.832196586476392, -19.542087020635872, -13.091150353312631, -11.99030492445459, -11.283073096378654, -7.801599999999997, -4.0, -7.801600000000006, -10.368403456, -15.674593971404796, -15.674593971404796, -15.674593971404796, -7.160170753814003, -4.000000000000002, -3.999999999999996, -5.919999999999992, -8.501078528000011, -10.331056957440008, -8.023576156478116, -9.863104633348549, -9.863104633348549, -9.863104633348549, -9.863104633348549, -2.000000000000004, -2.000000000000004, -2.000000000000004, -1.529600000000026, -1.529600000000026, -2.0000000000000053, -2.276662101333375, -9.430447535588696, -11.241838584876923, 0.0, -9.683200000000012, -8.919494241157127, -3.4615455338394434, -9.903209061930244, -7.584718308457242, -9.433023942288091, -9.433023942288091, -6.414124740364378, -4.000000000000008, -4.000000000000008, 0.0, -2.000000000000002, -7.801600000000006, -7.801600000000006, -5.429027840000031, -5.429027840000031, -6.8755854301594175, -5.696009512274784, -3.269253490390666, -3.269253490390666, -3.269253490390666, -3.269253490390666, -3.269253490390666, -3.269253490390666, 0.0, -1.9999999999999936, -5.92, 0.0, -2.0000000000000013, -5.880799999999999, -6.633753600000021, -13.262212755738782, -6.307769725132253, -6.307769725132253, -6.307769725132253, -2.0000000000000027, 0.0, -2.317363200000018, -8.061883704934424, -9.683199999999994, -9.327520399359994, -9.327520399359994, -2.0576024793798062, -2.0576024793798062, 0.0, -3.499008000000001, -3.499008000000001, -3.499008000000001, -3.499008000000001, -3.499008000000001, -1.999999999999997, -2.000000000000004, -4.000000000000005, -2.000000000000003, -3.96, -5.919999999999989, -9.645567999999992, -6.875585430159339, -14.140779757240027, -10.24849510623492, -10.24849510623492, -5.919999999999996, -1.9999999999999936, 0.0, -2.000000000000005, -4.000000000000002, -7.801599999999996, -11.452656639999995, -5.124473167872014, -5.124473167872014, -1.9619556068010924, -1.9619556068010924, -3.922716494665068, -6.5585903624817, -2.0000000000000093, -7.839999999999993, -9.683199999999996, 0.0, 0.0, 0.0, 0.0, -3.960000000000013, -11.45265664000001, -5.124473167872034, -5.124473167872034, -5.124473167872034, -1.999999999999996, -5.920000000000005, -9.645568, -14.994550374400006, -9.90064603083574, -20.252643740890296, -21.847590866072487, -16.26229869330113, -13.695164265871217];

        var Confidence_Mode_dates = ['2020-10-15 17:00:00', '2020-10-28 05:00:00', '2020-11-03 21:00:00', '2020-11-09 23:00:00', '2020-11-17 05:00:00', '2020-11-22 17:00:00', '2020-11-27 01:00:00', '2020-12-01 17:00:00', '2020-12-06 16:00:00', '2020-12-11 16:00:00', '2020-12-17 12:00:00', '2020-12-22 01:00:00', '2020-12-26 15:00:00', '2020-12-30 23:00:00', '2021-01-04 12:00:00', '2021-01-08 16:00:00', '2021-01-12 20:00:00', '2021-01-17 00:00:00', '2021-01-21 04:00:00', '2021-01-25 08:00:00', '2021-01-29 12:00:00', '2021-02-02 19:00:00', '2021-02-06 23:00:00', '2021-02-11 03:00:00', '2021-02-15 07:00:00', '2021-02-19 11:00:00', '2021-02-23 15:00:00', '2021-02-27 19:00:00', '2021-03-03 23:00:00', '2021-03-08 03:00:00', '2021-03-12 07:00:00', '2021-03-16 11:00:00', '2021-03-20 15:00:00', '2021-03-24 19:00:00', '2021-03-28 23:00:00', '2021-04-02 03:00:00', '2021-04-06 08:00:00', '2021-04-10 12:00:00', '2021-04-14 16:00:00', '2021-04-18 20:00:00', '2021-04-23 00:00:00', '2021-04-27 04:00:00', '2021-05-01 08:00:00', '2021-05-05 12:00:00', '2021-05-09 16:00:00', '2021-05-13 20:00:00', '2021-05-18 00:00:00', '2021-05-22 04:00:00', '2021-05-26 08:00:00', '2021-05-30 12:00:00', '2021-06-03 16:00:00', '2021-06-07 20:00:00', '2021-06-12 00:00:00', '2021-06-16 04:00:00', '2021-06-20 08:00:00', '2021-06-24 12:00:00', '2021-06-28 16:00:00', '2021-07-02 20:00:00', '2021-07-07 00:00:00', '2021-07-11 04:00:00', '2021-07-15 08:00:00', '2021-07-19 13:00:00', '2021-07-23 22:00:00', '2021-07-28 02:00:00', '2021-08-01 06:00:00', '2021-08-05 10:00:00', '2021-08-09 14:00:00', '2021-08-13 18:00:00', '2021-08-17 22:00:00', '2021-08-22 02:00:00', '2021-08-26 06:00:00', '2021-08-30 10:00:00', '2021-09-03 14:00:00', '2021-09-07 18:00:00', '2021-09-11 22:00:00', '2021-09-16 02:00:00', '2021-09-20 06:00:00', '2021-09-24 10:00:00', '2021-09-28 14:00:00', '2021-10-02 18:00:00', '2021-10-06 22:00:00', '2021-10-11 02:00:00', '2021-10-15 06:00:00', '2021-10-19 10:00:00', '2021-10-23 15:00:00', '2021-10-27 19:00:00', '2021-10-31 23:00:00', '2021-11-05 03:00:00', '2021-11-09 07:00:00', '2021-11-13 11:00:00', '2021-11-17 15:00:00', '2021-11-21 19:00:00', '2021-11-25 23:00:00', '2021-11-30 03:00:00', '2021-12-04 07:00:00', '2021-12-08 11:00:00', '2021-12-12 15:00:00', '2021-12-16 20:00:00', '2021-12-21 00:00:00', '2021-12-25 04:00:00', '2021-12-29 08:00:00', '2022-01-02 12:00:00', '2022-01-06 16:00:00', '2022-01-10 20:00:00', '2022-01-15 00:00:00', '2022-01-19 04:00:00', '2022-01-23 08:00:00', '2022-01-27 12:00:00', '2022-01-31 16:00:00', '2022-02-04 20:00:00', '2022-02-09 00:00:00', '2022-02-13 04:00:00', '2022-02-17 08:00:00', '2022-02-21 12:00:00', '2022-02-25 16:00:00', '2022-03-01 20:00:00', '2022-03-06 00:00:00', '2022-03-10 04:00:00', '2022-03-14 08:00:00', '2022-03-18 12:00:00', '2022-03-22 16:00:00', '2022-03-26 20:00:00', '2022-03-31 00:00:00', '2022-04-04 04:00:00', '2022-04-08 08:00:00', '2022-04-12 12:00:00', '2022-04-16 16:00:00', '2022-04-20 20:00:00', '2022-04-25 01:00:00', '2022-04-29 06:00:00', '2022-05-03 10:00:00', '2022-05-07 14:00:00', '2022-05-11 18:00:00', '2022-05-15 22:00:00', '2022-05-20 02:00:00', '2022-05-24 06:00:00', '2022-05-28 11:00:00', '2022-06-01 15:00:00', '2022-06-05 19:00:00', '2022-06-09 23:00:00', '2022-06-14 03:00:00', '2022-06-18 07:00:00', '2022-06-22 11:00:00', '2022-06-26 15:00:00', '2022-06-30 19:00:00', '2022-07-04 23:00:00', '2022-07-09 03:00:00', '2022-07-13 07:00:00', '2022-07-17 13:00:00', '2022-07-21 17:00:00', '2022-07-25 21:00:00', '2022-07-30 01:00:00', '2022-08-03 05:00:00', '2022-08-07 10:00:00', '2022-08-11 15:00:00', '2022-08-15 19:00:00', '2022-08-19 23:00:00', '2022-08-24 04:00:00', '2022-08-28 12:00:00', '2022-09-01 17:00:00', '2022-09-05 22:00:00', '2022-09-10 02:00:00', '2022-09-14 07:00:00', '2022-09-18 14:00:00', '2022-09-22 20:00:00', '2022-09-27 01:00:00', '2022-10-01 05:00:00', '2022-10-05 11:00:00', '2022-10-09 16:00:00', '2022-10-13 22:00:00', '2022-10-18 08:00:00', '2022-10-22 16:00:00', '2022-10-26 20:00:00', '2022-10-31 02:00:00', '2022-11-04 07:00:00', '2022-11-08 11:00:00', '2022-11-12 15:00:00', '2022-11-16 21:00:00', '2022-11-21 04:00:00', '2022-11-25 09:00:00', '2022-11-29 16:00:00', '2022-12-03 20:00:00', '2022-12-08 04:00:00', '2022-12-12 18:00:00', '2022-12-17 00:00:00', '2022-12-21 06:00:00', '2022-12-25 17:00:00', '2022-12-30 04:00:00', '2023-01-03 12:00:00', '2023-01-07 16:00:00', '2023-01-11 20:00:00', '2023-01-16 00:00:00', '2023-01-20 05:00:00', '2023-01-24 09:00:00', '2023-01-28 13:00:00', '2023-02-01 17:00:00', '2023-02-05 22:00:00', '2023-02-10 02:00:00', '2023-02-14 07:00:00', '2023-02-18 14:00:00', '2023-02-22 20:00:00', '2023-02-27 01:00:00', '2023-03-03 06:00:00', '2023-03-07 15:00:00', '2023-03-11 20:00:00', '2023-03-16 00:00:00', '2023-03-20 04:00:00', '2023-03-24 08:00:00', '2023-03-28 14:00:00', '2023-04-01 18:00:00', '2023-04-05 22:00:00', '2023-04-10 02:00:00', '2023-04-14 06:00:00', '2023-04-18 10:00:00', '2023-04-22 15:00:00', '2023-04-26 20:00:00', '2023-05-01 02:00:00', '2023-05-05 07:00:00', '2023-05-09 15:00:00', '2023-05-13 20:00:00', '2023-05-18 00:00:00', '2023-05-22 05:00:00', '2023-05-26 09:00:00', '2023-05-30 14:00:00', '2023-06-03 19:00:00', '2023-06-08 00:00:00', '2023-06-12 04:00:00', '2023-06-16 09:00:00', '2023-06-20 16:00:00', '2023-06-24 22:00:00', '2023-06-29 03:00:00', '2023-07-03 08:00:00', '2023-07-07 13:00:00', '2023-07-11 18:00:00', '2023-07-16 00:00:00', '2023-07-20 04:00:00', '2023-07-24 10:00:00', '2023-07-28 16:00:00', '2023-08-01 22:00:00', '2023-08-06 04:00:00', '2023-08-10 10:00:00', '2023-08-14 17:00:00', '2023-08-18 21:00:00', '2023-08-23 01:00:00', '2023-08-27 07:00:00', '2023-08-31 11:00:00', '2023-09-04 17:00:00', '2023-09-08 23:00:00', '2023-09-13 06:00:00', '2023-09-17 14:00:00', '2023-09-21 21:00:00', '2023-09-26 06:00:00', '2023-09-30 11:00:00', '2023-10-04 15:00:00', '2023-10-08 21:00:00', '2023-10-13 02:00:00', '2023-10-17 12:00:00', '2023-10-21 17:00:00', '2023-10-25 22:00:00', '2023-10-30 02:00:00', '2023-11-03 06:00:00', '2023-11-07 10:00:00', '2023-11-11 14:00:00', '2023-11-15 18:00:00', '2023-11-19 22:00:00', '2023-11-24 02:00:00', '2023-11-28 06:00:00', '2023-12-02 10:00:00', '2023-12-06 14:00:00', '2023-12-10 18:00:00', '2023-12-14 22:00:00', '2023-12-19 02:00:00', '2023-12-23 06:00:00', '2023-12-27 10:00:00', '2023-12-31 14:00:00', '2024-01-04 18:00:00', '2024-01-09 00:00:00', '2024-01-13 04:00:00', '2024-01-17 09:00:00', '2024-01-21 20:00:00', '2024-01-26 00:00:00', '2024-01-30 05:00:00', '2024-02-03 09:00:00', '2024-02-07 14:00:00', '2024-02-11 18:00:00', '2024-02-15 22:00:00', '2024-02-20 02:00:00', '2024-02-24 06:00:00', '2024-02-28 10:00:00', '2024-03-03 14:00:00', '2024-03-07 18:00:00', '2024-03-11 22:00:00', '2024-03-16 02:00:00', '2024-03-20 06:00:00', '2024-03-24 10:00:00', '2024-03-28 14:00:00', '2024-04-01 18:00:00', '2024-04-05 22:00:00', '2024-04-10 02:00:00', '2024-04-14 12:00:00', '2024-04-18 16:00:00', '2024-04-22 20:00:00', '2024-04-27 00:00:00', '2024-05-01 04:00:00', '2024-05-05 08:00:00', '2024-05-09 12:00:00', '2024-05-13 20:00:00', '2024-05-18 00:00:00', '2024-05-22 04:00:00', '2024-05-26 08:00:00', '2024-05-30 12:00:00', '2024-06-03 16:00:00', '2024-06-07 21:00:00', '2024-06-12 01:00:00', '2024-06-16 05:00:00', '2024-06-20 09:00:00', '2024-06-24 15:00:00', '2024-06-28 19:00:00', '2024-07-03 00:00:00', '2024-07-07 04:00:00', '2024-07-11 09:00:00', '2024-07-15 13:00:00', '2024-07-19 17:00:00', '2024-07-23 21:00:00', '2024-07-28 02:00:00', '2024-08-01 06:00:00', '2024-08-05 12:00:00', '2024-08-09 16:00:00', '2024-08-13 20:00:00', '2024-08-18 00:00:00', '2024-08-22 04:00:00', '2024-08-26 08:00:00', '2024-08-30 12:00:00', '2024-09-03 18:00:00', '2024-09-07 22:00:00', '2024-09-12 02:00:00', '2024-09-16 07:00:00', '2024-09-20 13:00:00', '2024-09-24 18:00:00', '2024-09-28 22:00:00', '2024-10-03 02:00:00', '2024-10-07 06:00:00', '2024-10-11 10:00:00', '2024-10-15 14:00:00', '2024-10-19 18:00:00', '2024-10-23 22:00:00', '2024-10-28 02:00:00', '2024-11-01 06:00:00', '2024-11-05 10:00:00', '2024-11-09 14:00:00', '2024-11-13 18:00:00', '2024-11-17 22:00:00', '2024-11-22 02:00:00', '2024-11-26 06:00:00', '2024-11-30 10:00:00', '2024-12-04 14:00:00', '2024-12-08 18:00:00', '2024-12-12 22:00:00', '2024-12-17 02:00:00', '2024-12-21 06:00:00', '2024-12-25 10:00:00', '2024-12-29 14:00:00', '2025-01-02 18:00:00', '2025-01-06 22:00:00', '2025-01-11 02:00:00', '2025-01-15 06:00:00', '2025-01-19 10:00:00', '2025-01-23 14:00:00', '2025-01-27 21:00:00', '2025-02-01 01:00:00', '2025-02-05 05:00:00', '2025-02-09 09:00:00', '2025-02-13 13:00:00', '2025-02-17 17:00:00', '2025-02-21 21:00:00', '2025-02-26 01:00:00', '2025-03-02 05:00:00', '2025-03-06 09:00:00', '2025-03-10 13:00:00', '2025-03-14 17:00:00', '2025-03-18 21:00:00', '2025-03-23 01:00:00', '2025-03-27 05:00:00', '2025-03-31 10:00:00'];
        var Confidence_Mode_equity = [10000.0, 10000.0, 10000.0, 10000.0, 10000.0, 13737.725812801536, 12665.96339579001, 13187.193121453558, 12923.449259024486, 12923.449259024486, 14157.207881619355, 13874.06372398697, 13874.06372398697, 14298.818739859382, 15786.576961378183, 18323.893880410666, 17598.267682746402, 16741.88247857021, 18157.129610758675, 18157.129610758675, 18157.129610758675, 18874.08487760701, 20877.00059052409, 23484.802651244838, 23015.106598219936, 23015.106598219936, 24139.37150361221, 28393.031738482576, 28393.031738482576, 28393.031738482576, 27825.171103712924, 27825.171103712924, 30400.4196275395, 29792.41123498871, 28028.70048987738, 27468.126480079834, 27468.126480079834, 25842.01339245911, 26020.195068787318, 24989.795344063336, 24989.795344063336, 26322.58442908005, 27976.76582816192, 27943.641337421384, 29093.578065738944, 29059.13126930911, 32675.37045044802, 32021.863041439057, 31381.42578061028, 30138.721319698107, 28933.172466910182, 27787.41883722054, 28098.63792819741, 27536.665169633467, 27288.1743031427, 26742.410817079846, 26207.56260073825, 26207.56260073825, 25683.411348723483, 25683.411348723483, 28135.321018814942, 33477.703863839284, 33866.759419675574, 33866.759419675574, 35980.045207463314, 35218.695937258926, 40426.487322997054, 48236.6848846811, 51268.006793994144, 61275.52172018181, 70631.57696869613, 75070.25361628091, 81416.1923886439, 79787.86854087103, 79787.86854087103, 79787.86854087103, 82979.38328250588, 82979.38328250588, 82979.38328250588, 84666.26784608065, 84666.26784608065, 84666.26784608065, 84666.26784608065, 84666.26784608065, 82972.94248915903, 82972.94248915903, 81313.48363937585, 81313.48363937585, 79687.21396658833, 79687.21396658833, 79687.21396658833, 80967.30937174761, 94270.94378948434, 102995.81767898268, 113539.16170849816, 120169.84875227444, 120160.52126475023, 125157.49427210214, 125157.49427210214, 125157.49427210214, 125157.49427210214, 125157.49427210214, 121547.5115829149, 140701.79655259562, 135130.00540911284, 135130.00540911284, 142336.9390309322, 142336.9390309322, 142336.9390309322, 142336.9390309322, 139490.20025031356, 136700.3962453073, 144683.69938603323, 167134.2274023018, 185024.5831995763, 177697.60970487306, 167177.91121034455, 172296.06964916567, 179187.9124351323, 175604.15418642966, 186134.78410468143, 189708.57195949127, 185914.4005203015, 185914.4005203015, 171481.52352541356, 195896.2244908469, 191978.30000102991, 184299.16800098872, 207234.12315157056, 254454.27554342657, 305249.42676761723, 293161.5494676196, 407578.52957455406, 391438.41980340175, 368265.2653510404, 360899.9600440196, 388687.5701036727, 380913.8187015992, 380913.8187015992, 380913.8187015992, 380913.8187015992, 365829.6314810159, 373266.5674262082, 365801.236077684, 364395.686090435, 357107.7723686263, 357107.7723686263, 436429.40992845, 402380.933083472, 402380.933083472, 394333.3144218026, 378717.7151706992, 371143.3608672852, 371143.3608672852, 407025.54058455984, 455676.33020734007, 479979.06781839806, 441790.6739295379, 441790.6739295379, 432954.86045094713, 432954.86045094713, 456045.7863416644, 429226.6457384838, 395740.09974455024, 376036.4285358829, 368515.6999651652, 391674.2092595093, 368487.0960713464, 346817.1069255827, 547940.8195476485, 525982.3572668046, 703154.9238324875, 689091.8253558378, 689091.8253558378, 689091.8253558378, 702322.3884026699, 715806.9782600012, 738538.6058932838, 906373.2044986504, 880596.9734350822, 880596.9734350822, 880596.9734350822, 880596.9734350822, 862985.0339663806, 1021001.045625759, 960083.1913349312, 885181.3410797451, 859649.5953646852, 825607.4713882437, 825607.4713882437, 858631.7702437735, 886336.9553636393, 886336.9553636393, 833225.5432026105, 784225.2154579513, 768540.7111487923, 738106.4989873001, 738106.4989873001, 723344.3690075541, 723344.3690075541, 723344.3690075541, 694410.594247252, 722986.9790217148, 895367.228484191, 914832.7964894474, 896536.1405596584, 878605.4177484652, 861033.3093934959, 861033.3093934959, 915142.938644608, 896840.0798717159, 861325.2127087959, 1042962.3880977352, 960056.083012818, 991033.892624698, 991033.892624698, 991033.892624698, 991033.892624698, 1063931.7030091237, 1085560.3280423284, 1063849.1214814817, 1000869.253489778, 1000869.253489778, 1000869.253489778, 1000869.253489778, 1093500.7438042804, 1248050.8137074283, 1353552.709159496, 1325171.202678253, 1325171.202678253, 1395847.000154426, 1367930.0601513376, 1340571.458948311, 1313760.0297693447, 1313760.0297693447, 1313760.0297693447, 1287484.829173958, 1287484.829173958, 1287484.829173958, 1302447.119535398, 1412547.3160401245, 1580727.0828842856, 1838741.167004366, 1838741.167004366, 1838741.167004366, 1801966.3436642785, 1730608.4764551732, 1695996.3069260698, 1673536.4613631857, 1606595.0029086582, 1542973.8407934755, 1512114.363977606, 1452234.6351640928, 1480117.5401592434, 1450515.1893560586, 1504520.7708861644, 1416042.9133918907, 1583855.932344301, 1645259.872797221, 1676848.8623549272, 1818598.4861859984, 2086483.3905188448, 2164167.340114642, 2154953.873780919, 2108475.8286312125, 1983654.0595762448, 1983654.0595762448, 1983654.0595762448, 2154314.7630080525, 3116522.815030946, 3344112.846550088, 3277230.589619086, 3207883.3416289566, 3143725.6747963773, 3245163.2232364733, 3180259.958771744, 3180259.958771744, 3180259.958771744, 3180259.958771744, 3474596.325426423, 3671628.730429997, 3671628.730429997, 3712750.972210812, 3712750.972210812, 3784035.7908772593, 3918643.134624893, 3654913.292347736, 3581815.026500781, 4386688.660900249, 3945049.835229401, 4026925.7824425898, 4262098.248137236, 4003366.380596556, 4216879.254228372, 4132541.669143805, 4132541.669143805, 4297843.335909556, 4643043.472054865, 4643043.472054865, 5622173.409305666, 5399535.342297162, 5855976.063232683, 5855976.063232683, 6044928.89087299, 6044928.89087299, 5990379.452561752, 6105394.738050937, 5983286.843289918, 5983286.843289918, 5983286.843289918, 5983286.843289918, 5983286.843289918, 5983286.843289918, 6342092.588708329, 6415796.160712574, 6895354.497524227, 8507247.869080523, 8497175.287603533, 8160687.146214433, 8147630.046780488, 7661503.081355939, 8433292.255759409, 8433292.255759409, 8433292.255759409, 9282828.384435588, 10388054.308991004, 11636101.490908949, 10951805.634431574, 10946394.566303715, 11126871.714220408, 11126871.714220408, 12247748.263224116, 12247748.263224116, 13554174.744634682, 14567301.928630112, 14567301.928630112, 14567301.928630112, 14567301.928630112, 14567301.928630112, 17134243.364279825, 17463220.836873997, 18105867.36367096, 20027259.92250778, 19626714.724057622, 20434393.288382053, 19625191.314162124, 20485325.245756093, 18887142.111383185, 19249775.23992174, 19249775.23992174, 20602150.489609223, 23409256.981405437, 27978743.944175776, 27726263.758823533, 29786294.10268871, 28606756.85622224, 27473929.28471584, 27473929.28471584, 26924450.69902152, 28001428.72698238, 28001428.72698238, 27441400.15244273, 26860731.343968995, 30685030.95359229, 29787555.16826163, 29191804.0648964, 36572077.68233737, 36572077.68233737, 36572077.68233737, 36572077.68233737, 37707438.19234168, 34765654.69432794, 37704511.37115513, 37704511.37115513, 37704511.37115513, 39965575.50906057, 44040276.82255811, 42296281.860384814, 39792341.97425003, 41076308.20861916, 36356830.98951891, 35629694.369728535, 34917100.482333966, 36447449.02451904];
        var Confidence_Mode_drawdown = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, -7.801600000000004, -4.0074514432000194, -5.927302414336023, -5.927302414336023, 0.0, -1.999999999999998, -1.999999999999998, -5.920000000000006, -2.0000000000000058, -3.9600000000000017, -7.763184000000008, -12.251707866480634, -4.834052238127144, -4.834052238127144, -4.834052238127144, -3.999999999999999, -5.919999999999996, -2.000000000000004, -3.960000000000009, -3.960000000000009, -7.801600000000004, -3.959999999999996, -3.959999999999996, -3.959999999999996, -5.880799999999995, -5.880799999999995, -3.960000000000002, -5.880800000000002, -11.452656639999995, -13.223603507199991, -13.223603507199991, -18.360766179573748, -17.79786052221043, -21.053065245530902, -21.053065245530902, -16.84256205862588, -11.616726905222066, -11.721372700566263, -8.088530629939985, -8.197353809674135, -5.920000000000004, -7.801600000000007, -9.645568000000008, -13.223603507200016, -16.694659366912017, -19.9935508559823, -19.097478625569284, -20.7155290530579, -21.43099211888309, -23.002372276505422, -24.54232483097532, -24.54232483097532, -26.051478334355814, -26.051478334355814, -18.991859466009, -3.6098952791883305, -3.959999999999999, -3.959999999999999, -3.9999999999999942, -6.0313907199999885, 0.0, -4.000000000000003, -3.960000000000005, 0.0, -5.880800000000008, -3.9600000000000017, -2.0000000000000058, -3.96, -3.96, -3.96, -0.1183999999999918, -0.1183999999999918, -0.1183999999999918, -7.801599999999996, -7.801599999999996, -7.801599999999996, -7.801599999999996, -7.801599999999996, -9.645568, -9.645568, -11.452656640000008, -11.452656640000008, -13.223603507200004, -13.223603507200004, -13.223603507200004, -11.829627473939656, -3.9599999999999977, -2.0000000000000027, 0.0, -2.0000000000000004, -7.801599999999997, -5.880799999999994, -5.880799999999994, -5.880799999999994, -5.880799999999994, -5.880799999999994, -8.595529027583973, 0.0, -3.959999999999998, -3.959999999999998, 0.0, 0.0, 0.0, 0.0, -2.000000000000001, -3.96, -2.0000000000000027, -2.0, -5.880799999999995, -9.607920320000003, -14.959131437056016, -12.35560184436676, -8.849825918141422, -10.672829399778596, -5.316057633224542, -3.498125939782447, -5.428163420986791, -5.428163420986791, -12.769949106794884, -0.3505609186469677, -2.34354970027403, -6.249807712263069, 0.0, 0.0, -3.999999999999997, -7.801600000000004, 0.0, -3.959999999999994, -9.645567999999985, -11.452656639999978, -4.634925075813116, -6.542226574296859, -6.542226574296859, -6.542226574296859, -6.542226574296859, -10.243154401954705, -8.418491078065868, -10.250121256504546, -10.594975041790091, -12.38307554095429, -12.38307554095429, -4.0, -11.489535999999996, -11.489535999999996, -13.259745279999994, -16.69465936691199, -18.360766179573755, -18.360766179573755, -10.46787634562993, -2.000000000000002, 0.0, -11.496406111407293, -11.496406111407293, -13.266477989179142, -13.266477989179142, -8.640690148602017, -14.013348442343023, -20.721683050265195, -24.66890470448323, -26.175526610393568, -21.53619983732836, -26.181256806958515, -30.522389456654885, 0.0, -11.489535999999994, -9.6832, -11.489535999999998, -11.489535999999998, -11.489535999999998, -9.790135091199994, -8.058105684951038, -11.489536, 0.0, -5.8807999999999945, -5.8807999999999945, -5.8807999999999945, -5.8807999999999945, -7.76318399999999, 0.0, -9.683199999999994, -16.729355468799994, -19.131173969748502, -22.33357948054645, -22.33357948054645, -19.226922659768316, -16.620644697590166, -16.620644697590166, -21.6169333871104, -26.226484768481217, -27.701955073111588, -30.56495765221637, -30.56495765221637, -31.95365849917204, -31.95365849917204, -31.95365849917204, -34.675512159205155, -31.987278835580774, -15.771150217595958, -13.939988263835822, -15.661188498559106, -17.34796472858793, -19.001005434016168, -19.001005434016168, -13.910812618170922, -15.632596365807505, -18.97354554972153, -1.8866008034460893, -9.685750129956157, -6.771610334149411, -6.771610334149411, -6.771610334149411, -6.771610334149411, -2.0000000000000044, -5.91999999999999, -7.801599999999997, -13.259745280000004, -13.259745280000004, -13.259745280000004, -13.259745280000004, -5.231844495799103, -4.000000000000004, 0.0, -4.04743168000007, -4.04743168000007, 0.0, -1.9999999999999944, -3.959999999999992, -5.880799999999992, -5.880799999999992, -5.880799999999992, -7.763183999999984, -7.763183999999984, -7.763183999999984, -6.691269215658668, -2.000000000000007, -4.000000000000004, 0.0, 0.0, 0.0, -2.0000000000000058, -5.880799999999998, -7.763183999999996, -8.984663453765378, -12.625276915614764, -16.08531594975641, -17.763609630761284, -21.02017068938314, -19.503757966619293, -21.113682807286903, -18.17658744556775, -22.98845869106881, -13.861942030444574, -10.522486670723763, -8.804518414801679, -1.9999999999999984, -7.8016, -4.368869171200024, -7.801599999999999, -9.790135091199977, -15.130559093800931, -15.130559093800931, -15.130559093800931, -7.828943968424553, -3.999999999999997, -4.000000000000005, -5.920000000000006, -7.910762905600032, -9.752547647488036, -6.840563184913664, -8.70375192121539, -8.70375192121539, -8.70375192121539, -8.70375192121539, -2.0000000000000013, -2.0000000000000004, -2.0000000000000004, -0.9024000000000464, -0.9024000000000464, -1.9999999999999944, 0.0, -7.801600000000004, -9.645568000000004, 0.0, -10.06770390630399, -8.201240303747195, -2.840192737486063, -8.738306042102993, -3.8710156976818193, -5.79359538372818, -5.79359538372818, -2.025339199077337, -3.959999999999985, -3.959999999999985, 0.0, -3.959999999999989, -2.000000000000004, -2.000000000000004, -2.0000000000000027, -2.0000000000000027, -2.884352000000002, -1.999999999999998, -3.959999999999993, -3.959999999999993, -3.959999999999993, -3.959999999999993, -3.959999999999993, -3.959999999999993, 0.0, -2.000000000000001, -5.92, 0.0, -1.999999999999993, -5.880799999999993, -6.03139072000001, -11.63801186162906, -2.736778624522147, -2.736778624522147, -2.736778624522147, -2.000000000000008, 0.0, 0.0, -5.880799999999992, -9.683199999999996, -8.194114404351996, -8.194114404351996, 0.0, 0.0, 0.0, -2.884352000000004, -2.884352000000004, -2.884352000000004, -2.884352000000004, -2.884352000000004, -2.0000000000000044, -2.0000000000000018, -4.000000000000003, -5.920000000000006, -7.801600000000006, -5.920000000000004, -9.645568000000004, -5.685509135400957, -13.04354845469352, -11.37398458502364, -11.37398458502364, -5.919999999999996, -2.0, 0.0, -5.92, -3.999999999999999, -7.801599999999997, -11.452656639999995, -11.452656639999995, -13.223603507199996, -9.752547647488, -9.752547647488, -11.557496694538235, -13.428968366082875, -1.9999999999999944, -7.839999999999991, -9.683199999999989, 0.0, 0.0, 0.0, 0.0, -3.960000000000003, -11.45265664000002, -3.967454547968031, -3.967454547968031, -3.967454547968031, -1.9999999999999916, -5.92, -9.645567999999994, -14.994550374399992, -12.251707866480643, -22.333579480546515, -23.88690789093558, -25.409169733116865, -22.13999884600233];

        var Aggressive_Only_dates = ['2020-10-15 17:00:00', '2020-10-28 05:00:00', '2020-11-03 21:00:00', '2020-11-09 23:00:00', '2020-11-17 05:00:00', '2020-11-22 17:00:00', '2020-11-27 01:00:00', '2020-12-01 17:00:00', '2020-12-06 16:00:00', '2020-12-11 16:00:00', '2020-12-17 12:00:00', '2020-12-22 01:00:00', '2020-12-26 15:00:00', '2020-12-30 23:00:00', '2021-01-04 12:00:00', '2021-01-08 16:00:00', '2021-01-12 20:00:00', '2021-01-17 00:00:00', '2021-01-21 04:00:00', '2021-01-25 08:00:00', '2021-01-29 12:00:00', '2021-02-02 19:00:00', '2021-02-06 23:00:00', '2021-02-11 03:00:00', '2021-02-15 07:00:00', '2021-02-19 11:00:00', '2021-02-23 15:00:00', '2021-02-27 19:00:00', '2021-03-03 23:00:00', '2021-03-08 03:00:00', '2021-03-12 07:00:00', '2021-03-16 11:00:00', '2021-03-20 15:00:00', '2021-03-24 19:00:00', '2021-03-28 23:00:00', '2021-04-02 03:00:00', '2021-04-06 08:00:00', '2021-04-10 12:00:00', '2021-04-14 16:00:00', '2021-04-18 20:00:00', '2021-04-23 00:00:00', '2021-04-27 04:00:00', '2021-05-01 08:00:00', '2021-05-05 12:00:00', '2021-05-09 16:00:00', '2021-05-13 20:00:00', '2021-05-18 00:00:00', '2021-05-22 04:00:00', '2021-05-26 08:00:00', '2021-05-30 12:00:00', '2021-06-03 16:00:00', '2021-06-07 20:00:00', '2021-06-12 00:00:00', '2021-06-16 04:00:00', '2021-06-20 08:00:00', '2021-06-24 12:00:00', '2021-06-28 16:00:00', '2021-07-02 20:00:00', '2021-07-07 00:00:00', '2021-07-11 04:00:00', '2021-07-15 08:00:00', '2021-07-19 13:00:00', '2021-07-23 22:00:00', '2021-07-28 02:00:00', '2021-08-01 06:00:00', '2021-08-05 10:00:00', '2021-08-09 14:00:00', '2021-08-13 18:00:00', '2021-08-17 22:00:00', '2021-08-22 02:00:00', '2021-08-26 06:00:00', '2021-08-30 10:00:00', '2021-09-03 14:00:00', '2021-09-07 18:00:00', '2021-09-11 22:00:00', '2021-09-16 02:00:00', '2021-09-20 06:00:00', '2021-09-24 10:00:00', '2021-09-28 14:00:00', '2021-10-02 18:00:00', '2021-10-06 22:00:00', '2021-10-11 02:00:00', '2021-10-15 06:00:00', '2021-10-19 10:00:00', '2021-10-23 15:00:00', '2021-10-27 19:00:00', '2021-10-31 23:00:00', '2021-11-05 03:00:00', '2021-11-09 07:00:00', '2021-11-13 11:00:00', '2021-11-17 15:00:00', '2021-11-21 19:00:00', '2021-11-25 23:00:00', '2021-11-30 03:00:00', '2021-12-04 07:00:00', '2021-12-08 11:00:00', '2021-12-12 15:00:00', '2021-12-16 20:00:00', '2021-12-21 00:00:00', '2021-12-25 04:00:00', '2021-12-29 08:00:00', '2022-01-02 12:00:00', '2022-01-06 16:00:00', '2022-01-10 20:00:00', '2022-01-15 00:00:00', '2022-01-19 04:00:00', '2022-01-23 08:00:00', '2022-01-27 12:00:00', '2022-01-31 16:00:00', '2022-02-04 20:00:00', '2022-02-09 00:00:00', '2022-02-13 04:00:00', '2022-02-17 08:00:00', '2022-02-21 12:00:00', '2022-02-25 16:00:00', '2022-03-01 20:00:00', '2022-03-06 00:00:00', '2022-03-10 04:00:00', '2022-03-14 08:00:00', '2022-03-18 12:00:00', '2022-03-22 16:00:00', '2022-03-26 20:00:00', '2022-03-31 00:00:00', '2022-04-04 04:00:00', '2022-04-08 08:00:00', '2022-04-12 12:00:00', '2022-04-16 16:00:00', '2022-04-20 20:00:00', '2022-04-25 01:00:00', '2022-04-29 06:00:00', '2022-05-03 10:00:00', '2022-05-07 14:00:00', '2022-05-11 18:00:00', '2022-05-15 22:00:00', '2022-05-20 02:00:00', '2022-05-24 06:00:00', '2022-05-28 11:00:00', '2022-06-01 15:00:00', '2022-06-05 19:00:00', '2022-06-09 23:00:00', '2022-06-14 03:00:00', '2022-06-18 07:00:00', '2022-06-22 11:00:00', '2022-06-26 15:00:00', '2022-06-30 19:00:00', '2022-07-04 23:00:00', '2022-07-09 03:00:00', '2022-07-13 07:00:00', '2022-07-17 13:00:00', '2022-07-21 17:00:00', '2022-07-25 21:00:00', '2022-07-30 01:00:00', '2022-08-03 05:00:00', '2022-08-07 10:00:00', '2022-08-11 15:00:00', '2022-08-15 19:00:00', '2022-08-19 23:00:00', '2022-08-24 04:00:00', '2022-08-28 12:00:00', '2022-09-01 17:00:00', '2022-09-05 22:00:00', '2022-09-10 02:00:00', '2022-09-14 07:00:00', '2022-09-18 14:00:00', '2022-09-22 20:00:00', '2022-09-27 01:00:00', '2022-10-01 05:00:00', '2022-10-05 11:00:00', '2022-10-09 16:00:00', '2022-10-13 22:00:00', '2022-10-18 08:00:00', '2022-10-22 16:00:00', '2022-10-26 20:00:00', '2022-10-31 02:00:00', '2022-11-04 07:00:00', '2022-11-08 11:00:00', '2022-11-12 15:00:00', '2022-11-16 21:00:00', '2022-11-21 04:00:00', '2022-11-25 09:00:00', '2022-11-29 16:00:00', '2022-12-03 20:00:00', '2022-12-08 04:00:00', '2022-12-12 18:00:00', '2022-12-17 00:00:00', '2022-12-21 06:00:00', '2022-12-25 17:00:00', '2022-12-30 04:00:00', '2023-01-03 12:00:00', '2023-01-07 16:00:00', '2023-01-11 20:00:00', '2023-01-16 00:00:00', '2023-01-20 05:00:00', '2023-01-24 09:00:00', '2023-01-28 13:00:00', '2023-02-01 17:00:00', '2023-02-05 22:00:00', '2023-02-10 02:00:00', '2023-02-14 07:00:00', '2023-02-18 14:00:00', '2023-02-22 20:00:00', '2023-02-27 01:00:00', '2023-03-03 06:00:00', '2023-03-07 15:00:00', '2023-03-11 20:00:00', '2023-03-16 00:00:00', '2023-03-20 04:00:00', '2023-03-24 08:00:00', '2023-03-28 14:00:00', '2023-04-01 18:00:00', '2023-04-05 22:00:00', '2023-04-10 02:00:00', '2023-04-14 06:00:00', '2023-04-18 10:00:00', '2023-04-22 15:00:00', '2023-04-26 20:00:00', '2023-05-01 02:00:00', '2023-05-05 07:00:00', '2023-05-09 15:00:00', '2023-05-13 20:00:00', '2023-05-18 00:00:00', '2023-05-22 05:00:00', '2023-05-26 09:00:00', '2023-05-30 14:00:00', '2023-06-03 19:00:00', '2023-06-08 00:00:00', '2023-06-12 04:00:00', '2023-06-16 09:00:00', '2023-06-20 16:00:00', '2023-06-24 22:00:00', '2023-06-29 03:00:00', '2023-07-03 08:00:00', '2023-07-07 13:00:00', '2023-07-11 18:00:00', '2023-07-16 00:00:00', '2023-07-20 04:00:00', '2023-07-24 10:00:00', '2023-07-28 16:00:00', '2023-08-01 22:00:00', '2023-08-06 04:00:00', '2023-08-10 10:00:00', '2023-08-14 17:00:00', '2023-08-18 21:00:00', '2023-08-23 01:00:00', '2023-08-27 07:00:00', '2023-08-31 11:00:00', '2023-09-04 17:00:00', '2023-09-08 23:00:00', '2023-09-13 06:00:00', '2023-09-17 14:00:00', '2023-09-21 21:00:00', '2023-09-26 06:00:00', '2023-09-30 11:00:00', '2023-10-04 15:00:00', '2023-10-08 21:00:00', '2023-10-13 02:00:00', '2023-10-17 12:00:00', '2023-10-21 17:00:00', '2023-10-25 22:00:00', '2023-10-30 02:00:00', '2023-11-03 06:00:00', '2023-11-07 10:00:00', '2023-11-11 14:00:00', '2023-11-15 18:00:00', '2023-11-19 22:00:00', '2023-11-24 02:00:00', '2023-11-28 06:00:00', '2023-12-02 10:00:00', '2023-12-06 14:00:00', '2023-12-10 18:00:00', '2023-12-14 22:00:00', '2023-12-19 02:00:00', '2023-12-23 06:00:00', '2023-12-27 10:00:00', '2023-12-31 14:00:00', '2024-01-04 18:00:00', '2024-01-09 00:00:00', '2024-01-13 04:00:00', '2024-01-17 09:00:00', '2024-01-21 20:00:00', '2024-01-26 00:00:00', '2024-01-30 05:00:00', '2024-02-03 09:00:00', '2024-02-07 14:00:00', '2024-02-11 18:00:00', '2024-02-15 22:00:00', '2024-02-20 02:00:00', '2024-02-24 06:00:00', '2024-02-28 10:00:00', '2024-03-03 14:00:00', '2024-03-07 18:00:00', '2024-03-11 22:00:00', '2024-03-16 02:00:00', '2024-03-20 06:00:00', '2024-03-24 10:00:00', '2024-03-28 14:00:00', '2024-04-01 18:00:00', '2024-04-05 22:00:00', '2024-04-10 02:00:00', '2024-04-14 12:00:00', '2024-04-18 16:00:00', '2024-04-22 20:00:00', '2024-04-27 00:00:00', '2024-05-01 04:00:00', '2024-05-05 08:00:00', '2024-05-09 12:00:00', '2024-05-13 20:00:00', '2024-05-18 00:00:00', '2024-05-22 04:00:00', '2024-05-26 08:00:00', '2024-05-30 12:00:00', '2024-06-03 16:00:00', '2024-06-07 21:00:00', '2024-06-12 01:00:00', '2024-06-16 05:00:00', '2024-06-20 09:00:00', '2024-06-24 15:00:00', '2024-06-28 19:00:00', '2024-07-03 00:00:00', '2024-07-07 04:00:00', '2024-07-11 09:00:00', '2024-07-15 13:00:00', '2024-07-19 17:00:00', '2024-07-23 21:00:00', '2024-07-28 02:00:00', '2024-08-01 06:00:00', '2024-08-05 12:00:00', '2024-08-09 16:00:00', '2024-08-13 20:00:00', '2024-08-18 00:00:00', '2024-08-22 04:00:00', '2024-08-26 08:00:00', '2024-08-30 12:00:00', '2024-09-03 18:00:00', '2024-09-07 22:00:00', '2024-09-12 02:00:00', '2024-09-16 07:00:00', '2024-09-20 13:00:00', '2024-09-24 18:00:00', '2024-09-28 22:00:00', '2024-10-03 02:00:00', '2024-10-07 06:00:00', '2024-10-11 10:00:00', '2024-10-15 14:00:00', '2024-10-19 18:00:00', '2024-10-23 22:00:00', '2024-10-28 02:00:00', '2024-11-01 06:00:00', '2024-11-05 10:00:00', '2024-11-09 14:00:00', '2024-11-13 18:00:00', '2024-11-17 22:00:00', '2024-11-22 02:00:00', '2024-11-26 06:00:00', '2024-11-30 10:00:00', '2024-12-04 14:00:00', '2024-12-08 18:00:00', '2024-12-12 22:00:00', '2024-12-17 02:00:00', '2024-12-21 06:00:00', '2024-12-25 10:00:00', '2024-12-29 14:00:00', '2025-01-02 18:00:00', '2025-01-06 22:00:00', '2025-01-11 02:00:00', '2025-01-15 06:00:00', '2025-01-19 10:00:00', '2025-01-23 14:00:00', '2025-01-27 21:00:00', '2025-02-01 01:00:00', '2025-02-05 05:00:00', '2025-02-09 09:00:00', '2025-02-13 13:00:00', '2025-02-17 17:00:00', '2025-02-21 21:00:00', '2025-02-26 01:00:00', '2025-03-02 05:00:00', '2025-03-06 09:00:00', '2025-03-10 13:00:00', '2025-03-14 17:00:00', '2025-03-18 21:00:00', '2025-03-23 01:00:00', '2025-03-27 05:00:00', '2025-03-31 10:00:00'];
        var Aggressive_Only_equity = [10000.0, 10000.0, 10000.0, 8847.36, 9023.457853440004, 11974.406831108125, 10594.188802127275, 11255.26618338002, 9957.939182018905, 9957.939182018905, 13765.096219861342, 13214.492371066888, 14915.115080790796, 14894.174295720017, 15801.35451713535, 17121.494633316943, 16436.634847984264, 18551.92718831839, 19709.56744486945, 17437.763862904023, 16070.643176052348, 18138.834671996865, 19654.26192763633, 19243.64194276224, 18473.89626505175, 18473.89626505175, 18447.95895990789, 20822.096162784124, 19989.21231627276, 19989.21231627276, 18788.73071451572, 18788.73071451572, 23468.773581478952, 22530.022638219794, 22978.46020881092, 24412.31612584072, 20734.516723115343, 25899.231070475504, 37292.83669093035, 32994.31516258695, 30882.46284362984, 31497.147384069445, 32124.06660560197, 35550.63371019952, 36258.23352356732, 32717.46420400951, 30752.56867878247, 29522.465931631174, 30110.08109353436, 28301.77580324665, 30067.806613369245, 26602.070951885853, 29439.62518675367, 28262.04017928352, 33228.32124502772, 36004.4148907567, 36004.4148907567, 33181.66876332137, 33842.11669838652, 33842.11669838652, 41446.81634403281, 52801.15526286055, 51698.02507892403, 58351.23831090504, 71463.40990645772, 58269.31331512299, 59429.10572734719, 77216.61128344107, 72579.25390641563, 81919.75098962692, 81804.73585972455, 86909.35137737136, 94170.28088187492, 90403.46964659991, 90403.46964659991, 79983.20412125424, 90276.54339646596, 86665.48166060731, 83198.86239418302, 83082.05139499916, 83082.05139499916, 79758.76933919919, 76568.41856563122, 70565.45455008573, 67742.83636808231, 57537.14503387361, 50905.1835486892, 46914.21715847196, 47847.997736794176, 45934.077827322406, 50833.71279557009, 54005.73647401367, 64759.588278798896, 70169.9934628315, 82500.4518341028, 82500.4518341028, 82500.4518341028, 82500.4518341028, 84142.54082740878, 80776.83919431243, 74443.93500147833, 68607.53049736243, 67174.17099300669, 87402.53629912926, 71265.64179930295, 80437.08520126845, 85456.35931782759, 85456.35931782759, 85456.35931782759, 85456.35931782759, 82038.1049451145, 90788.83613926006, 87157.28269368966, 108867.09510051548, 108714.24596543131, 113085.0119064154, 122532.81339676486, 156100.62908701977, 156100.62908701977, 149856.603923539, 165841.30834204977, 207150.34852933764, 186921.2439279885, 179444.39417086894, 172024.75649339322, 175448.73724663767, 175448.73724663767, 178940.86891279474, 219150.87038937656, 257660.64573651893, 328246.6775671072, 355670.37764229684, 533476.1545430707, 544094.4639230958, 554924.120133021, 589551.3852293218, 799046.6794811692, 767084.8123019225, 736401.4198098456, 706945.3630174518, 678667.5484967538, 600441.612186824, 599598.5936328103, 575614.6498874979, 598756.7586747835, 574806.4883277921, 551814.2287946804, 762785.9355239967, 647869.6103536884, 688296.6740397585, 701996.5310398458, 792339.10412632, 714963.8511716255, 714963.8511716255, 857330.4922594272, 821881.7302526317, 966304.9795772374, 889296.3438343437, 853724.49008097, 853724.49008097, 906996.8982620224, 1132918.724944521, 962240.6217912151, 886800.957042784, 1020849.1517439109, 1041168.1332602218, 1224124.974085063, 1198550.3592664816, 1126569.6512569226, 1870002.523010153, 1830934.1310990225, 2478066.922419928, 2283786.4757022057, 2329242.961714582, 2329242.961714582, 2793051.720987226, 2967338.1483768285, 3279243.6942523755, 4438272.896471251, 4004856.84233734, 3613765.692589181, 3330446.462290189, 3396735.6686756145, 3993619.1183370873, 5412739.543268993, 5630354.2397887055, 4981377.088693701, 5734358.465813177, 5284784.762093424, 5284784.762093424, 5726307.440561216, 7450688.062614585, 7152660.540110002, 7750236.786259574, 7284784.895311825, 7132590.003713389, 6573394.94742226, 7728489.436636499, 7882317.290383308, 8039206.933731097, 7254144.494160512, 8187707.624697325, 9818078.769793589, 11081604.242654508, 12756691.567857446, 11286304.270979926, 10834852.100140728, 11050508.996341929, 10608488.63648825, 13250933.319252295, 13514679.896038692, 12703035.8796498, 17559708.68786519, 15211131.96130904, 14297605.016248563, 12649605.87165569, 12649605.87165569, 11889915.15057419, 14851547.494703349, 13401231.287681812, 11856551.764538456, 11144489.076504428, 11366308.98708317, 11128842.241115589, 11823281.9969612, 14682929.912272066, 17606657.328017976, 18705312.745286297, 18679050.531970527, 18679050.531970527, 20671482.58871405, 19050838.353758864, 22398502.47151698, 19816761.482640047, 19402746.53106289, 21472372.827709597, 21023768.57901344, 20182817.835852902, 21442225.668810125, 23233638.06038186, 22748237.176642284, 26745618.07528761, 34799608.5416721, 34799608.5416721, 34799608.5416721, 33407624.20000522, 34072569.55208214, 28939419.73942171, 36867362.313059606, 32617882.663407102, 35342976.60153579, 37548378.34147161, 38295741.26398027, 41495197.63125987, 39835389.72600948, 45856878.57937557, 35894828.91914988, 35844432.667194925, 46638377.23642854, 52640445.3632829, 54756810.736274, 60512458.42086432, 71145868.98113436, 65568032.85301342, 61630248.0189725, 57928952.65585962, 55611794.54962523, 53387322.76764023, 68012758.69113803, 97932973.21709624, 108226992.95569684, 101727261.41044568, 110226168.16882446, 119435124.67282815, 140422583.27400342, 149184952.4703012, 175400130.1449226, 175400130.1449226, 155182809.54389825, 214512889.2630064, 252207666.19867337, 242119359.55072644, 223137201.76194948, 214211713.6914715, 251853567.2525733, 334217450.24258715, 327234925.79732686, 277935858.5378148, 313704469.2118744, 271747109.41143507, 255426936.30919096, 255426936.30919096, 245209858.85682333, 271365577.1348845, 250090515.88750955, 250090515.88750955, 294037088.247305, 352587003.1002121, 338483522.9762036, 366762470.63621545, 359100010.4176882, 366247537.02504194, 366247537.02504194, 373537328.0019884, 413381309.6555338, 421609251.2429174, 430000961.7796563, 456833021.7947065, 404176620.3705615, 404176620.3705615, 447288793.210088, 429397241.4816845, 412221351.82241714, 456191629.3501416, 494304619.59526825, 592732656.5521575, 740375106.8838323, 615697327.6602529, 567426657.171689, 602834080.5792023, 601987703.0054214, 639551735.6729599, 613969666.2460415, 613969666.2460415, 626190118.4830028, 782166413.0980046, 1196536011.9509711, 1079689227.3962767, 1078173346.3634007, 1033593209.878834, 992249481.4836804, 1011999215.1631324, 1011999215.1631324, 1316745660.2805634, 1484115985.4382124, 1484115985.4382124, 1367761292.1798563, 1161703348.414116, 1234193637.355157, 1479951520.5184116, 1420753459.6976752, 1391070851.097118, 1507289269.7771585, 1389117791.026629, 1416766791.5392237, 1278413543.160759, 1276618653.6748984, 1223833232.8411057, 1438888472.0712323, 1797297980.004092, 2244982909.8129544, 2692013449.393147, 3165061231.423043, 3228058610.1732864, 3497750025.808186, 3223526423.784824, 2737891079.110742, 2680690620.6233883, 2470524475.9665146, 2519697795.136152, 2519697795.136152, 2467055865.6485205, 2415513739.7744536, 2896501103.0790195, 2835986938.593317, 3200960761.03178, 3468388259.5021644, 3068607955.158907, 3758158258.327063, 3758158258.327063, 4072137384.255575, 4596196055.471991, 4500171592.089702, 4500171592.089702, 4980189895.245938, 5855322144.144939, 5971866476.101999, 5782636291.747463, 5661824529.118469, 5774517484.546042, 5427720327.682669, 5002187053.992348, 4610015588.959348, 5420099821.626186];
        var Aggressive_Only_drawdown = [0.0, 0.0, 0.0, -11.526399999999994, -9.765421465599974, 0.0, -11.526400000000006, -6.005647360000004, -16.839812422696966, -16.839812422696966, 0.0, -3.9999999999999982, -7.84000000000001, -7.969392414451343, -4.134783765053437, -7.839999999999991, -11.5264, -4.000000000000002, -3.999999999999996, -15.065343999999994, -21.72422103039999, -11.65061671787325, -7.840000000000005, -9.765421465600008, -13.374804606976005, -13.374804606976005, -18.46273024000001, -7.969392414451317, -11.650616717873268, -11.650616717873268, -16.956569122532645, -16.956569122532645, -7.84, -11.526399999999995, -9.7654214656, -4.134783765053449, -18.577208367191997, -4.000000000000005, 0.0, -11.526400000000011, -17.18928999804276, -15.54102562616381, -13.85995420022696, -4.67168264825116, -4.0, -13.374804606975989, -18.577208367191965, -21.834120032504284, -20.278306357631244, -25.066110147211457, -20.390235420397456, -29.56637532490076, -22.05345535955686, -25.171317145174587, -12.022221450770337, -4.672029119509697, -4.672029119509697, -12.145742036540138, -10.397090886035445, -10.397090886035445, 0.0, 0.0, -11.5264, -7.840000000000004, 0.0, -18.46273024, -16.839812422696976, -4.000000000000005, -15.065343999999994, -4.134783765053466, -15.065343999999993, -9.765421465599989, -3.999999999999997, -7.840000000000002, -7.840000000000002, -18.46273024, -7.969392414451297, -11.650616717873238, -15.184592049158317, -15.303672674347457, -15.303672674347457, -18.691525767373555, -21.94386473667862, -28.06346574132301, -30.940927111670085, -41.34491400550772, -48.10573383757688, -52.17424430471086, -51.22232046335184, -53.17342764481776, -48.17859326026503, -44.94493747970556, -33.98213941229443, -28.466610597862505, -15.89657265641384, -15.89657265641384, -15.89657265641384, -15.89657265641384, -14.222578038567102, -17.65367491702442, -24.109626803529714, -30.05943206213298, -31.520641597981736, -10.89924118160545, -27.349673924038647, -18.000030296097098, -12.883232186573562, -12.883232186573562, -12.883232186573562, -12.883232186573562, -16.367902899110614, -7.447145875015722, -11.149260040015095, -4.000000000000005, -7.840000000000004, -7.839999999999993, -11.526399999999995, -2.089215999999988, -2.089215999999988, -6.005647359999981, 0.0, -7.840000000000007, -18.46273024, -21.724221030400003, -24.9607552312042, -23.467174103326105, -23.467174103326105, -21.94386473667871, -4.403783852618585, -4.000000000000002, -3.999999999999994, 0.0, 0.0, 0.0, -4.000000000000005, 0.0, 0.0, -3.999999999999996, -7.83999999999999, -11.526399999999986, -15.065343999999984, -24.855252189183982, -24.96075523120414, -27.962325021955976, -25.066110147211475, -28.063465741323014, -30.940927111670096, -4.538000706131092, -18.919679289030015, -13.86026727666549, -12.145742036540252, -4.000000000000005, -15.065343999999987, -15.065343999999987, 0.0, -11.526399999999992, 0.0, -16.839812422696955, -20.166219925789072, -20.166219925789072, -15.18459204915831, 0.0, -15.065343999999984, -21.72422103039999, -9.89211059302583, -8.098603162269402, -4.000000000000005, -7.84, -15.065344000000003, 0.0, -7.840000000000004, -11.526400000000002, -18.462730240000003, -16.839812422696973, -16.839812422696973, -0.2806023896151868, -4.000000000000006, -4.0, 0.0, -15.065343999999987, -23.359571128757462, -29.368180752262877, -27.962325021955888, -15.30367267434714, 0.0, 0.0, -11.52639999999999, 0.0, -7.839999999999997, -7.839999999999997, -11.526399999999995, 0.0, -3.9999999999999942, -4.000000000000002, -9.765421465600026, -11.650616717873252, -18.577208367191982, -4.2693782940305205, -3.999999999999997, -4.000000000000002, -13.374804606976054, -7.840000000000014, 0.0, 0.0, 0.0, -11.526399999999995, -15.065344000000003, -13.37480460697602, -16.83981242269698, -4.000000000000004, -3.999999999999997, -9.765421465599946, 0.0, -18.46273024, -23.35957112875753, -32.193453522172426, -32.193453522172426, -36.26567559062527, -20.390235420397552, -28.16446445936765, -36.4445156279231, -40.2614338972499, -39.07239747754077, -40.345306697856415, -36.62285383580265, -21.294087766163106, -5.621831972921904, 0.0, -4.000000000000007, -4.000000000000007, 0.0, -7.840000000000014, 0.0, -11.526400000000008, -13.37480460697604, -4.13478376505349, -6.137615201068549, -9.89211059302581, -4.269378294030613, -4.000000000000005, -11.526399999999992, -4.000000000000004, 0.0, 0.0, 0.0, -4.000000000000002, -7.840000000000005, -21.724221030400003, -7.839999999999994, -18.46273024, -11.650616717873238, -6.137615201068553, -4.269378294030606, -7.839999999999991, -11.526399999999986, -4.0, -24.85525218918399, -24.9607552312042, -4.000000000000002, 0.0, -4.000000000000005, -11.5264, -3.999999999999997, -11.5264, -16.83981242269694, -21.834120032504305, -24.96075523120413, -27.96232502195596, -8.22763249851378, -4.000000000000003, -3.999999999999999, -9.765421465599983, -3.9999999999999942, -7.839999999999997, -3.999999999999996, -3.999999999999999, 0.0, 0.0, -11.5264, -7.840000000000001, -4.000000000000005, -7.840000000000001, -15.065344000000003, -18.46273024000001, -4.134783765053473, 0.0, -2.089216000000035, -16.839812422696998, -6.137615201068543, -18.69152576737356, -23.574626003581557, -23.574626003581557, -26.63164096343829, -18.805682666205044, -25.17131714517456, -25.17131714517456, -12.022221450770392, -7.839999999999997, -11.526399999999995, -4.134783765053409, -7.840000000000001, -7.83999999999999, -7.83999999999999, -7.839999999999998, 0.0, -4.000000000000005, -7.840000000000001, -2.0892160000000706, -13.37480460697606, -13.37480460697606, -4.134783765053512, -7.969392414451369, -11.65061671787331, -2.226682501113141, -3.999999999999997, -7.839999999999993, 0.0, -16.839812422696948, -23.35957112875752, -18.577208367192004, -18.691525767373545, -13.617876975257625, -17.073161896247317, -17.073161896247317, -15.42258611063022, 0.0, 0.0, -11.5264, -11.65061671787324, -15.303672674347418, -18.691525767373523, -17.073161896247264, -17.073161896247264, 0.0, -4.000000000000002, -4.000000000000002, -11.526400000000002, -24.85525218918401, -20.1662199257891, -7.840000000000004, -11.5264, -13.374804606975996, -7.84, -15.065344000000003, -13.374804606975962, -21.83412003250433, -21.943864736678712, -25.17131714517464, -12.022221450770433, 0.0, 0.0, -4.0, 0.0, -7.840000000000008, -3.999999999999995, -11.526400000000006, -24.85525218918401, -26.425188283607216, -32.19345352217241, -30.843832021077738, -30.843832021077738, -32.288653747480254, -33.70329002720327, -20.502007335033408, -22.16289211746869, -12.145742036540094, -7.84, -18.46273024, -0.1403997552639166, -0.1403997552639166, -7.839999999999998, 0.0, -7.84, -7.84, 0.0, -3.999999999999993, -7.840000000000008, -10.760268539141434, -12.624679287178711, -10.885560903710733, -16.237459862675774, -22.80444300944199, -28.856574677501733, -16.35506226403151];

        var Conservative_02_Vol15_dates = ['2020-10-15 17:00:00', '2020-10-28 05:00:00', '2020-11-03 21:00:00', '2020-11-09 23:00:00', '2020-11-17 05:00:00', '2020-11-22 17:00:00', '2020-11-27 01:00:00', '2020-12-01 17:00:00', '2020-12-06 16:00:00', '2020-12-11 16:00:00', '2020-12-17 12:00:00', '2020-12-22 01:00:00', '2020-12-26 15:00:00', '2020-12-30 23:00:00', '2021-01-04 12:00:00', '2021-01-08 16:00:00', '2021-01-12 20:00:00', '2021-01-17 00:00:00', '2021-01-21 04:00:00', '2021-01-25 08:00:00', '2021-01-29 12:00:00', '2021-02-02 19:00:00', '2021-02-06 23:00:00', '2021-02-11 03:00:00', '2021-02-15 07:00:00', '2021-02-19 11:00:00', '2021-02-23 15:00:00', '2021-02-27 19:00:00', '2021-03-03 23:00:00', '2021-03-08 03:00:00', '2021-03-12 07:00:00', '2021-03-16 11:00:00', '2021-03-20 15:00:00', '2021-03-24 19:00:00', '2021-03-28 23:00:00', '2021-04-02 03:00:00', '2021-04-06 08:00:00', '2021-04-10 12:00:00', '2021-04-14 16:00:00', '2021-04-18 20:00:00', '2021-04-23 00:00:00', '2021-04-27 04:00:00', '2021-05-01 08:00:00', '2021-05-05 12:00:00', '2021-05-09 16:00:00', '2021-05-13 20:00:00', '2021-05-18 00:00:00', '2021-05-22 04:00:00', '2021-05-26 08:00:00', '2021-05-30 12:00:00', '2021-06-03 16:00:00', '2021-06-07 20:00:00', '2021-06-12 00:00:00', '2021-06-16 04:00:00', '2021-06-20 08:00:00', '2021-06-24 12:00:00', '2021-06-28 16:00:00', '2021-07-02 20:00:00', '2021-07-07 00:00:00', '2021-07-11 04:00:00', '2021-07-15 08:00:00', '2021-07-19 13:00:00', '2021-07-23 22:00:00', '2021-07-28 02:00:00', '2021-08-01 06:00:00', '2021-08-05 10:00:00', '2021-08-09 14:00:00', '2021-08-13 18:00:00', '2021-08-17 22:00:00', '2021-08-22 02:00:00', '2021-08-26 06:00:00', '2021-08-30 10:00:00', '2021-09-03 14:00:00', '2021-09-07 18:00:00', '2021-09-11 22:00:00', '2021-09-16 02:00:00', '2021-09-20 06:00:00', '2021-09-24 10:00:00', '2021-09-28 14:00:00', '2021-10-02 18:00:00', '2021-10-06 22:00:00', '2021-10-11 02:00:00', '2021-10-15 06:00:00', '2021-10-19 10:00:00', '2021-10-23 15:00:00', '2021-10-27 19:00:00', '2021-10-31 23:00:00', '2021-11-05 03:00:00', '2021-11-09 07:00:00', '2021-11-13 11:00:00', '2021-11-17 15:00:00', '2021-11-21 19:00:00', '2021-11-25 23:00:00', '2021-11-30 03:00:00', '2021-12-04 07:00:00', '2021-12-08 11:00:00', '2021-12-12 15:00:00', '2021-12-16 20:00:00', '2021-12-21 00:00:00', '2021-12-25 04:00:00', '2021-12-29 08:00:00', '2022-01-02 12:00:00', '2022-01-06 16:00:00', '2022-01-10 20:00:00', '2022-01-15 00:00:00', '2022-01-19 04:00:00', '2022-01-23 08:00:00', '2022-01-27 12:00:00', '2022-01-31 16:00:00', '2022-02-04 20:00:00', '2022-02-09 00:00:00', '2022-02-13 04:00:00', '2022-02-17 08:00:00', '2022-02-21 12:00:00', '2022-02-25 16:00:00', '2022-03-01 20:00:00', '2022-03-06 00:00:00', '2022-03-10 04:00:00', '2022-03-14 08:00:00', '2022-03-18 12:00:00', '2022-03-22 16:00:00', '2022-03-26 20:00:00', '2022-03-31 00:00:00', '2022-04-04 04:00:00', '2022-04-08 08:00:00', '2022-04-12 12:00:00', '2022-04-16 16:00:00', '2022-04-20 20:00:00', '2022-04-25 01:00:00', '2022-04-29 06:00:00', '2022-05-03 10:00:00', '2022-05-07 14:00:00', '2022-05-11 18:00:00', '2022-05-15 22:00:00', '2022-05-20 02:00:00', '2022-05-24 06:00:00', '2022-05-28 11:00:00', '2022-06-01 15:00:00', '2022-06-05 19:00:00', '2022-06-09 23:00:00', '2022-06-14 03:00:00', '2022-06-18 07:00:00', '2022-06-22 11:00:00', '2022-06-26 15:00:00', '2022-06-30 19:00:00', '2022-07-04 23:00:00', '2022-07-09 03:00:00', '2022-07-13 07:00:00', '2022-07-17 13:00:00', '2022-07-21 17:00:00', '2022-07-25 21:00:00', '2022-07-30 01:00:00', '2022-08-03 05:00:00', '2022-08-07 10:00:00', '2022-08-11 15:00:00', '2022-08-15 19:00:00', '2022-08-19 23:00:00', '2022-08-24 04:00:00', '2022-08-28 12:00:00', '2022-09-01 17:00:00', '2022-09-05 22:00:00', '2022-09-10 02:00:00', '2022-09-14 07:00:00', '2022-09-18 14:00:00', '2022-09-22 20:00:00', '2022-09-27 01:00:00', '2022-10-01 05:00:00', '2022-10-05 11:00:00', '2022-10-09 16:00:00', '2022-10-13 22:00:00', '2022-10-18 08:00:00', '2022-10-22 16:00:00', '2022-10-26 20:00:00', '2022-10-31 02:00:00', '2022-11-04 07:00:00', '2022-11-08 11:00:00', '2022-11-12 15:00:00', '2022-11-16 21:00:00', '2022-11-21 04:00:00', '2022-11-25 09:00:00', '2022-11-29 16:00:00', '2022-12-03 20:00:00', '2022-12-08 04:00:00', '2022-12-12 18:00:00', '2022-12-17 00:00:00', '2022-12-21 06:00:00', '2022-12-25 17:00:00', '2022-12-30 04:00:00', '2023-01-03 12:00:00', '2023-01-07 16:00:00', '2023-01-11 20:00:00', '2023-01-16 00:00:00', '2023-01-20 05:00:00', '2023-01-24 09:00:00', '2023-01-28 13:00:00', '2023-02-01 17:00:00', '2023-02-05 22:00:00', '2023-02-10 02:00:00', '2023-02-14 07:00:00', '2023-02-18 14:00:00', '2023-02-22 20:00:00', '2023-02-27 01:00:00', '2023-03-03 06:00:00', '2023-03-07 15:00:00', '2023-03-11 20:00:00', '2023-03-16 00:00:00', '2023-03-20 04:00:00', '2023-03-24 08:00:00', '2023-03-28 14:00:00', '2023-04-01 18:00:00', '2023-04-05 22:00:00', '2023-04-10 02:00:00', '2023-04-14 06:00:00', '2023-04-18 10:00:00', '2023-04-22 15:00:00', '2023-04-26 20:00:00', '2023-05-01 02:00:00', '2023-05-05 07:00:00', '2023-05-09 15:00:00', '2023-05-13 20:00:00', '2023-05-18 00:00:00', '2023-05-22 05:00:00', '2023-05-26 09:00:00', '2023-05-30 14:00:00', '2023-06-03 19:00:00', '2023-06-08 00:00:00', '2023-06-12 04:00:00', '2023-06-16 09:00:00', '2023-06-20 16:00:00', '2023-06-24 22:00:00', '2023-06-29 03:00:00', '2023-07-03 08:00:00', '2023-07-07 13:00:00', '2023-07-11 18:00:00', '2023-07-16 00:00:00', '2023-07-20 04:00:00', '2023-07-24 10:00:00', '2023-07-28 16:00:00', '2023-08-01 22:00:00', '2023-08-06 04:00:00', '2023-08-10 10:00:00', '2023-08-14 17:00:00', '2023-08-18 21:00:00', '2023-08-23 01:00:00', '2023-08-27 07:00:00', '2023-08-31 11:00:00', '2023-09-04 17:00:00', '2023-09-08 23:00:00', '2023-09-13 06:00:00', '2023-09-17 14:00:00', '2023-09-21 21:00:00', '2023-09-26 06:00:00', '2023-09-30 11:00:00', '2023-10-04 15:00:00', '2023-10-08 21:00:00', '2023-10-13 02:00:00', '2023-10-17 12:00:00', '2023-10-21 17:00:00', '2023-10-25 22:00:00', '2023-10-30 02:00:00', '2023-11-03 06:00:00', '2023-11-07 10:00:00', '2023-11-11 14:00:00', '2023-11-15 18:00:00', '2023-11-19 22:00:00', '2023-11-24 02:00:00', '2023-11-28 06:00:00', '2023-12-02 10:00:00', '2023-12-06 14:00:00', '2023-12-10 18:00:00', '2023-12-14 22:00:00', '2023-12-19 02:00:00', '2023-12-23 06:00:00', '2023-12-27 10:00:00', '2023-12-31 14:00:00', '2024-01-04 18:00:00', '2024-01-09 00:00:00', '2024-01-13 04:00:00', '2024-01-17 09:00:00', '2024-01-21 20:00:00', '2024-01-26 00:00:00', '2024-01-30 05:00:00', '2024-02-03 09:00:00', '2024-02-07 14:00:00', '2024-02-11 18:00:00', '2024-02-15 22:00:00', '2024-02-20 02:00:00', '2024-02-24 06:00:00', '2024-02-28 10:00:00', '2024-03-03 14:00:00', '2024-03-07 18:00:00', '2024-03-11 22:00:00', '2024-03-16 02:00:00', '2024-03-20 06:00:00', '2024-03-24 10:00:00', '2024-03-28 14:00:00', '2024-04-01 18:00:00', '2024-04-05 22:00:00', '2024-04-10 02:00:00', '2024-04-14 12:00:00', '2024-04-18 16:00:00', '2024-04-22 20:00:00', '2024-04-27 00:00:00', '2024-05-01 04:00:00', '2024-05-05 08:00:00', '2024-05-09 12:00:00', '2024-05-13 20:00:00', '2024-05-18 00:00:00', '2024-05-22 04:00:00', '2024-05-26 08:00:00', '2024-05-30 12:00:00', '2024-06-03 16:00:00', '2024-06-07 21:00:00', '2024-06-12 01:00:00', '2024-06-16 05:00:00', '2024-06-20 09:00:00', '2024-06-24 15:00:00', '2024-06-28 19:00:00', '2024-07-03 00:00:00', '2024-07-07 04:00:00', '2024-07-11 09:00:00', '2024-07-15 13:00:00', '2024-07-19 17:00:00', '2024-07-23 21:00:00', '2024-07-28 02:00:00', '2024-08-01 06:00:00', '2024-08-05 12:00:00', '2024-08-09 16:00:00', '2024-08-13 20:00:00', '2024-08-18 00:00:00', '2024-08-22 04:00:00', '2024-08-26 08:00:00', '2024-08-30 12:00:00', '2024-09-03 18:00:00', '2024-09-07 22:00:00', '2024-09-12 02:00:00', '2024-09-16 07:00:00', '2024-09-20 13:00:00', '2024-09-24 18:00:00', '2024-09-28 22:00:00', '2024-10-03 02:00:00', '2024-10-07 06:00:00', '2024-10-11 10:00:00', '2024-10-15 14:00:00', '2024-10-19 18:00:00', '2024-10-23 22:00:00', '2024-10-28 02:00:00', '2024-11-01 06:00:00', '2024-11-05 10:00:00', '2024-11-09 14:00:00', '2024-11-13 18:00:00', '2024-11-17 22:00:00', '2024-11-22 02:00:00', '2024-11-26 06:00:00', '2024-11-30 10:00:00', '2024-12-04 14:00:00', '2024-12-08 18:00:00', '2024-12-12 22:00:00', '2024-12-17 02:00:00', '2024-12-21 06:00:00', '2024-12-25 10:00:00', '2024-12-29 14:00:00', '2025-01-02 18:00:00', '2025-01-06 22:00:00', '2025-01-11 02:00:00', '2025-01-15 06:00:00', '2025-01-19 10:00:00', '2025-01-23 14:00:00', '2025-01-27 21:00:00', '2025-02-01 01:00:00', '2025-02-05 05:00:00', '2025-02-09 09:00:00', '2025-02-13 13:00:00', '2025-02-17 17:00:00', '2025-02-21 21:00:00', '2025-02-26 01:00:00', '2025-03-02 05:00:00', '2025-03-06 09:00:00', '2025-03-10 13:00:00', '2025-03-14 17:00:00', '2025-03-18 21:00:00', '2025-03-23 01:00:00', '2025-03-27 05:00:00', '2025-03-31 10:00:00'];
        var Conservative_02_Vol15_equity = [10000.0, 10000.0, 10000.0, 10645.146819947457, 11632.18375779844, 14211.1728338731, 13539.22310653451, 15733.910327717598, 15302.25154680402, 15378.51647229314, 18118.21482849297, 17755.850531923115, 17329.505850549358, 18127.554835613315, 18569.822309501447, 20898.77539252777, 20150.226885288463, 20339.38204082336, 21618.626118322143, 21186.2535959557, 19541.52574659504, 22623.617920266573, 24425.067767063345, 29910.373091815512, 29312.1656299792, 29312.1656299792, 28977.47626397046, 31681.139465133117, 31440.78522039097, 29591.81552315022, 27425.562267636204, 29655.341740920278, 34765.20466370677, 33388.50255902398, 33916.30800747705, 38036.06918244392, 35977.663957817625, 39354.52856290324, 48268.52709283712, 45429.95155156156, 49304.08620549228, 53931.769197601614, 55727.909516186286, 55499.794466614985, 55922.48090127273, 54155.70825658772, 59418.66107561834, 64574.2152525441, 67853.8479211355, 71499.87026633152, 70603.52501271431, 69463.93968585737, 82036.52171274452, 79199.04311228349, 91845.5322491384, 93494.29148401498, 94678.55250947917, 88434.64304504702, 95127.92912823154, 99543.97899376212, 122495.82194097378, 129697.52101583316, 120253.1669799028, 123318.87449596245, 127429.50364582783, 121454.8140344327, 148647.56380753953, 164575.25826487978, 152891.3744255278, 179925.37869238603, 203972.15558664344, 228456.93688154756, 262465.5346136096, 265790.0980520487, 255264.8101691876, 226030.20793776272, 234783.44372457356, 223775.33208625505, 229241.41753134856, 232843.49731416025, 215590.68915872625, 223887.34447596545, 225003.17710962676, 225795.18829305255, 222289.4982208196, 216951.9929787419, 218033.26049866423, 226188.30136792967, 212059.2919391219, 207818.10610033947, 221903.55551380684, 222684.65602921543, 270512.92375192407, 291073.9362823217, 303402.5410192623, 286445.863300202, 297131.9439384284, 315951.6294260326, 318511.2810014113, 309772.94524122303, 279893.5851623781, 326575.36571611965, 312229.0284914825, 351159.1671507201, 293992.3029082148, 329626.0898993145, 338029.5041849654, 343516.1467015326, 325641.3296268627, 311465.74761188973, 325924.67976213945, 352056.59630840167, 339164.00210631086, 419029.3597618822, 434480.5349469862, 414021.8227644498, 410270.5996320211, 434843.4671646125, 455136.16229896096, 446033.4390529817, 484701.0017102683, 503353.92758013227, 561782.9300572748, 557520.870227907, 562271.7539710276, 610523.0289354075, 633358.2616939731, 540973.3054766966, 684545.5116969902, 818875.936164534, 991468.422303036, 991027.2902408374, 1302756.6426920511, 1282829.6770854336, 1413242.3473106865, 1449271.2336591706, 1612414.3259978695, 1632838.2407938424, 1567524.711162089, 1661069.7324032846, 1760197.230869052, 1737158.6123436966, 1778799.8165801528, 1743223.8202485498, 1854961.971230783, 1878458.1561997065, 1926347.150998716, 2439615.4120871862, 2383516.822566233, 2609949.650678098, 2577230.486673309, 2822066.0096570537, 2937924.223504056, 2820407.254563894, 3110648.8587378347, 3079617.046405833, 3643403.746443172, 3395242.933951875, 3302094.6704084347, 3199688.700426214, 3087010.21060866, 3573059.225386465, 3405531.9320670967, 3179617.3349244464, 3121413.7245324315, 3277600.9435381037, 3474009.3591902107, 3352358.127485545, 3166318.999254924, 5177570.309924245, 5843250.76029346, 8989244.049939072, 8812879.072292255, 8679665.567887131, 8789607.998413704, 9175496.96433259, 10039053.958629064, 10008596.297052065, 11656532.546278143, 10803222.2261788, 10777212.47169991, 11448637.157124398, 11876847.53062524, 14204158.94565361, 17017377.14449133, 18004970.71724052, 16600294.921764284, 16971475.865942296, 16817706.693982463, 16563494.009752346, 17204931.142618723, 19753247.89931691, 19358182.94133057, 22492899.53865281, 24491571.6618321, 24126997.310268432, 23943953.82400787, 24865397.75363613, 24676752.26934521, 23331594.25346042, 23276919.11739723, 24253342.25032204, 29342715.155358348, 33267438.153465174, 37799155.343085006, 37383130.4598141, 36357526.7678578, 37865933.94935803, 39323142.53043857, 46068886.6740899, 43890602.29170156, 43358434.1462369, 56096814.95469768, 52297710.36929519, 49632546.86862368, 51151537.98641445, 52455583.92883165, 51217378.13585621, 57905654.47098499, 63563640.11217683, 65941094.27549362, 74761018.6708031, 77049060.09689662, 76845606.22500303, 77818983.90385307, 89347506.00858685, 98903054.58629292, 103808646.09377304, 103287963.0382832, 105810943.6820983, 112125413.17901286, 103377836.944439, 110960617.21215816, 113789422.52944992, 109528185.75370046, 107872582.2516328, 108410208.6540336, 104117164.39133388, 108123943.29692575, 128719895.02725913, 124615998.5151777, 134578972.06920132, 173248850.2566903, 173248850.2566903, 173248850.2566903, 169783873.2515565, 179728831.57314295, 191124891.55866435, 229381097.60991564, 203110868.1035888, 236001093.9719208, 270938888.9199742, 294189473.24239945, 304458184.7393458, 323741898.24713624, 340104840.8415699, 271879193.32856363, 268575051.30834633, 278910723.20684195, 301273165.1919594, 296587168.89098144, 328025006.7986138, 355572548.0358724, 311032526.99708635, 296325913.8165777, 287262937.4479637, 310295041.41045827, 326054485.8068275, 405047079.9313749, 545527350.0961535, 572569218.4813386, 541357814.9107867, 559387162.2251596, 577656073.1345968, 624767135.0991294, 617449073.8534286, 692494689.753832, 733820628.6695976, 733820628.6695976, 887807109.6321614, 1083445038.3987195, 1102636856.9520051, 1016613539.9200376, 1029490644.759025, 1073793218.5788317, 1263659589.79733, 1225011208.1799371, 1162235837.5133314, 1378196405.5587776, 1400270136.4110773, 1412110617.5520484, 1412110617.5520484, 1429997352.041041, 1616735092.1804018, 1623102010.5380874, 1516060851.4083793, 1606909955.6729977, 1838566447.2678664, 1765759215.956059, 1909712363.1924083, 2005868163.077352, 1850148926.7802367, 2205589488.4015565, 2145079287.826559, 2172250292.1390285, 2225311125.941677, 2366257450.820725, 2601254550.737752, 2351328086.182494, 2351328086.182494, 2316057279.9250145, 2260445260.5921354, 2198430095.5641418, 2425682628.775976, 2548284324.206442, 2879585617.6710086, 3615510269.5439887, 3405227058.8127656, 3391288198.6246386, 3230937240.47143, 3281705089.661877, 4619169870.393147, 4642191361.375023, 4606972602.91339, 4743027141.410198, 5403240476.74695, 6762156208.279844, 6626724570.173279, 6760341838.369604, 7013197131.438282, 7671456021.993504, 8497791433.599469, 8815632098.28063, 11068609381.94084, 10896689490.24019, 10678755700.435389, 11919453088.659004, 13697205972.514616, 16014993716.74765, 17650431823.949707, 17166193403.555103, 16537553668.162195, 18987021485.15217, 19471072391.556538, 20936041401.51841, 20467923230.77783, 19908122185.48188, 20682279643.17797, 22910083597.590004, 25404292036.83496, 28462316641.666306, 32347030821.387177, 39594927944.68259, 40087986033.90564, 40086845662.19157, 38499406573.96878, 34785920133.28015, 35635624209.06908, 32184955445.656494, 34105653932.494926, 34239966545.10161, 35665617219.12552, 35843371168.04084, 37828829328.2936, 35875353055.87182, 39843767896.43042, 39540361685.061, 37974563362.33258, 39428735975.35418, 39625244940.201164, 42209088454.96841, 45023027685.29963, 46330342730.67957, 44495661158.54466, 43274925661.0374, 47397089924.30469, 52039047459.58601, 51913758650.43248, 51262951159.5879, 53922634594.9267, 51116777464.70559, 47725816399.43669, 48080387470.372246, 52027051414.90626];
        var Conservative_02_Vol15_drawdown = [0.0, 0.0, 0.0, -2.000000000000008, 0.0, 0.0, -4.72832000000002, -2.000000000000005, -4.6886234666666775, -4.213600885359147, -1.9999999999999944, -3.9599999999999977, -6.266064872843959, -5.919999999999998, -6.3051044126720095, -3.959999999999998, -7.399943120349863, -6.53068352037987, -2.784, -4.728320000000006, -12.124435818291204, -3.999999999999997, -5.919999999999995, -2.0000000000000018, -3.960000000000005, -3.960000000000005, -11.452656639999995, -3.960000000000008, -4.688623466666671, -10.293694897838934, -16.86059767254922, -10.101117924357585, -3.960000000000012, -7.763184000000008, -7.83999999999999, -2.000000000000004, -7.303484727766911, 0.0, 0.0, -5.880799999999991, -2.7839999999999945, -1.999999999999997, -3.959999999999994, -6.633753599999993, -5.9226762674175895, -8.894884212722094, -5.920000000000003, 0.0, -5.88080000000001, -1.9999999999999996, -3.9999999999999942, -5.549500415999972, 0.0, -7.763184000000001, -0.7586666666666676, -2.743493333333336, -1.9999999999999931, -9.607920319999996, -5.880799999999995, -2.0000000000000027, 0.0, 0.0, -11.415761913600008, -9.15741503321157, -6.129328867651953, -10.530571182680724, 0.0, -3.999999999999993, -15.029954560000016, -0.0056892630784516, -9.645567999999995, -1.999999999999996, -1.9999999999999916, -0.7586666666666405, -4.688623466666641, -15.604308159886845, -12.33600434860625, -16.446239020535156, -14.405299152343446, -13.06034611218439, -19.502240289725155, -16.404415570428537, -15.987783351421983, -15.692060348819007, -17.001023171631545, -18.993953460530857, -18.59022724516935, -15.545278862271944, -20.820801707820305, -22.404385673663896, -17.14512736932337, -16.85347821766338, -3.960000000000015, -7.801599999999981, -3.896483501091456, -11.489535999999998, -11.45265663999999, -5.844262164771776, -5.490493439999995, -8.083355451839816, -16.9492379050509, -3.0976969749927, -7.477061631999995, 0.0, -16.279473694607795, -6.131999180349866, -3.738949227009455, -4.728320000000009, -9.685769216720878, -13.617262762256264, -9.607184138041896, -2.359689035154226, -9.68319999999999, -1.9999999999999971, -3.959999999999998, -9.645568, -10.464219618827086, -8.539187200000008, -4.271015936000032, -6.185595617280033, 0.0, -5.880800000000009, -9.645568, -10.331056957439989, -9.56694794105544, -3.959999999999991, -2.0000000000000018, -16.29479373187318, 0.0, -1.9999999999999991, -1.9999999999999976, -4.767999999999993, 0.0, -1.529599999999992, -4.000000000000005, -1.5525973333333114, 0.0, 0.0, -4.0, -1.9999999999999936, 0.0, -5.588838399999994, -3.325720665988009, -5.259206252668248, -2.000000000000005, -0.7586666666666734, -2.000000000000001, 0.0, -2.299484961564437, -1.999999999999998, -5.920000000000003, 0.0, -0.4093371733332975, -4.39296368639996, -1.999999999999995, -2.9776473483960144, 0.0, -12.635533361152, -15.032371679091014, -17.667424051534127, -20.56680308157115, -8.060065342196665, -12.370782693800438, -18.183889051502263, -19.68155136235056, -15.662630375052252, -10.608760354371505, -13.739020892596567, -18.52607428700761, 0.0, -4.000000000000005, -5.919999999999997, -7.7658078348785, -9.159999206790248, -8.009359196742896, -3.9707065901430303, -2.000000000000009, -5.468416000000013, 0.0, -9.645568, -9.863104633348591, -4.247539683171084, -3.9600000000000057, 0.0, 0.0, 0.0, -7.801599999999994, -5.740052941650214, -9.36528486400009, -10.735298899706796, -7.278438052216904, 0.0, -2.000000000000009, -2.0000000000000053, -5.919999999999998, -7.3204472831999645, -8.023576156478061, -4.484013808402034, -5.208661756975626, -10.375846120715812, -10.585870894619603, -6.835115752648694, 0.0, 0.0, 0.0, -1.100619523094746, -3.8139174331866137, -3.9600000000000017, -0.2640575649303961, -3.999999999999996, -8.539187200000018, -9.648138287227855, 0.0, -18.428756377600003, -22.58573953559393, -20.216496338241257, -18.182513425555868, -20.113802303792617, -9.681777373939562, -4.728319999999999, -2.0, -5.919999999999995, -3.0407062023227205, -3.296734552498528, -2.0718265234968407, 0.0, -3.9999999999999982, 0.0, -4.000000000000005, -1.6550400000000345, 0.0, -7.801599999999989, -1.0388331546168277, -1.999999999999997, -6.8498493839439245, -8.257886178462481, -7.8006524535159185, -11.451746616356685, -8.044111805528903, -4.728319999999971, -11.452656640000008, -7.839999999999998, 0.0, 0.0, 0.0, -2.000000000000001, -0.7586666666666159, -2.7839999999999856, 0.0, -11.45265664, 0.0, -3.959999999999989, -3.959999999999998, -5.880799999999989, -3.960000000000016, -2.000000000000004, -21.65897762504706, -22.611054394022347, -19.632867304576, -13.189209198685118, -14.539462362855044, -7.801599999999989, -4.000000000000001, -19.99355085598229, -23.77651179214872, -26.10776815600721, -20.18325321171552, -16.129474017593157, 0.0, 0.0, -4.0, -9.23306989977603, -6.210173651929258, -7.801599999999992, -0.2823082666666677, -7.763184000000025, 0.0, -0.7586666666666649, -0.7586666666666649, -5.880799999999995, 0.0, 0.0, -7.801599999999989, -6.633753599999971, -5.91999999999999, 0.0, -7.839999999999995, -12.562714471510846, 0.0, -11.526400000000006, -10.778280072970231, -10.778280072970231, -9.648138287227864, 0.0, -2.000000000000002, -9.607920319999998, -4.191225162998053, -0.7999999999999788, -4.728319999999973, 0.0, -3.999999999999997, -11.452656639999995, 0.0, -2.743493333333331, -2.000000000000002, -2.000000000000008, -3.960000000000017, -4.688623466666658, -13.846064579884509, -13.846064579884509, -15.138406036774036, -17.176060565013792, -19.44832982181665, -11.1216738415459, -6.6294812748922265, -5.92, 0.0, -9.645567999999995, -10.015422277930211, -14.27018106327839, -12.923104907051725, 0.0, -3.9599999999999858, -4.688623466666671, -5.8807999999999945, 0.0, 0.0, -4.728319999999982, -3.5392000000000348, -2.7434933333333533, -3.9599999999999977, -3.959999999999996, -0.3678410638222896, 0.0, -6.205430959229113, -8.081322340044531, 0.0, -1.9999999999999971, 0.0, -3.960000000000001, -6.594850997333326, -10.015422277930169, -5.919999999999997, -3.5215453866666446, -5.91999999999999, -8.023576156478159, -10.539146379682968, -7.060325707558852, -2.0000000000000058, 0.0, 0.0, -2.0000000000000058, 0.0, -7.840000000000004, -7.842621649715211, -11.492053832386487, -20.029147965387043, -18.075726619688226, -26.008617859650577, -21.593041272757024, -21.28426421502799, -18.006774395336926, -17.59812817603869, -13.03367278299068, -17.524603634795582, -8.401443608832839, -9.098957233392484, -12.698638526950145, -9.3555783916088, -8.903815457322683, -2.963706165737272, 0.0, -5.880800000000002, -9.60792032, -12.087820999882158, -3.713723597179526, -6.56678499149147, -6.791734079056787, -7.960222727286811, -3.1849090657220414, -8.222669465443067, -14.310951433763142, -13.674338799140132, -6.588323222426341];

        var equityTraces = [
            {
                x: Conservative_02_Vol15_dates,
                y: Conservative_02_Vol15_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Conservative 0.2 Vol15 (NEW CHAMPION)',
                line: { color: '#6f42c1', width: 5 }
            },
            {
                x: Aggressive_Only_dates,
                y: Aggressive_Only_equity,
                type: 'scatter',
                mode: 'lines',
                name: '⚡ Aggressive Only (HIGH RISK)',
                line: { color: '#dc3545', width: 3 }
            },
            {
                x: PT_Adjusted_dates,
                y: PT_Adjusted_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🏆 PT Adjusted (PREVIOUS WINNER)',
                line: { color: '#28a745', width: 3 }
            },
            {
                x: Confidence_Mode_dates,
                y: Confidence_Mode_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🥈 Confidence Mode',
                line: { color: '#ff7f0e', width: 2 }
            }
        ];
        
        var equityLayout = {
            title: 'CORRECTED Equity Curves - PT Adjusted WINS! (Log Scale)',
            xaxis: { title: 'Date' },
            yaxis: { title: 'Equity ($)', type: 'log' },
            showlegend: true,
            hovermode: 'x unified'
        };
        
        Plotly.newPlot('equityChart', equityTraces, equityLayout);

        var drawdownTraces = [
            {
                x: Conservative_02_Vol15_dates,
                y: Conservative_02_Vol15_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Conservative 0.2 Vol15 (NEW CHAMPION)',
                line: { color: '#6f42c1', width: 5 },
                fill: 'tonexty'
            },
            {
                x: Aggressive_Only_dates,
                y: Aggressive_Only_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '⚡ Aggressive Only (HIGH RISK)',
                line: { color: '#dc3545', width: 3 },
                fill: 'tonexty'
            },
            {
                x: PT_Adjusted_dates,
                y: PT_Adjusted_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🏆 PT Adjusted (PREVIOUS WINNER)',
                line: { color: '#28a745', width: 3 },
                fill: 'tonexty'
            },
            {
                x: Confidence_Mode_dates,
                y: Confidence_Mode_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🥈 Confidence Mode',
                line: { color: '#ff7f0e', width: 2 },
                fill: 'tonexty'
            }
        ];
        
        var drawdownLayout = {
            title: 'CORRECTED Drawdown Comparison',
            xaxis: { title: 'Date' },
            yaxis: { title: 'Drawdown (%)' },
            showlegend: true,
            hovermode: 'x unified'
        };
        
        Plotly.newPlot('drawdownChart', drawdownTraces, drawdownLayout);
    </script>
    
    <div class="summary winner">
        <h3>🎯 FOUR-ALGORITHM COMPARISON INSIGHTS:</h3>
        <ul>
            <li><strong>🚀 Conservative 0.2 Vol15 DOMINATES</strong> - 1,346x better ADA returns than PT Adjusted!</li>
            <li><strong>✅ Revolutionary Parameters</strong> - 0.2 threshold + 15% volatility = game changer</li>
            <li><strong>✅ Excellent Risk Management</strong> - Only -30.80% ADA drawdown despite massive returns</li>
            <li><strong>⚡ Aggressive Only High Risk</strong> - Highest returns but -55.92% drawdown unacceptable</li>
            <li><strong>🏆 PT Adjusted Previous Winner</strong> - Good balance but surpassed by Conservative 0.2</li>
            <li><strong>🥈 Confidence Mode Moderate</strong> - Decent performance, middle ground</li>
            <li><strong>✅ Ready for Implementation</strong> - Conservative 0.2 Vol15 is the new champion!</li>
        </ul>
    </div>
    
</body>
</html>
