#!/usr/bin/env python3
"""
Check Phemex Symbols

Find the correct symbol formats for BTC and ADA on Phemex.
"""

import ccxt
import asyncio

async def check_phemex_symbols():
    """Check available Phemex symbols."""
    print("🔍 CHECKING PHEMEX SYMBOLS")
    print("=" * 50)
    
    try:
        # Initialize Phemex exchange
        exchange = ccxt.phemex({
            'sandbox': False,
            'enableRateLimit': True,
            'options': {'defaultType': 'swap'}  # Perpetual contracts
        })
        
        print("📊 Loading markets...")
        markets = exchange.load_markets()
        
        print(f"✅ Found {len(markets)} markets")
        
        # Find BTC symbols
        btc_symbols = [symbol for symbol in markets.keys() if 'BTC' in symbol]
        print(f"\n🟡 BTC Symbols ({len(btc_symbols)}):")
        for symbol in btc_symbols:
            market = markets[symbol]
            print(f"   {symbol} - Type: {market['type']} - Active: {market['active']}")
        
        # Find ADA symbols
        ada_symbols = [symbol for symbol in markets.keys() if 'ADA' in symbol]
        print(f"\n🔵 ADA Symbols ({len(ada_symbols)}):")
        for symbol in ada_symbols:
            market = markets[symbol]
            print(f"   {symbol} - Type: {market['type']} - Active: {market['active']}")
        
        # Test data fetching for each symbol
        print(f"\n🧪 TESTING DATA FETCHING:")
        print("-" * 50)
        
        test_symbols = []
        if btc_symbols:
            test_symbols.append(btc_symbols[0])  # First BTC symbol
        if ada_symbols:
            test_symbols.append(ada_symbols[0])  # First ADA symbol
        
        for symbol in test_symbols:
            print(f"\n📈 Testing {symbol}:")
            try:
                # Test ticker
                ticker = exchange.fetch_ticker(symbol)
                print(f"   ✅ Ticker: ${ticker['last']:.4f}")
                
                # Test OHLCV
                ohlcv = exchange.fetch_ohlcv(symbol, '1h', limit=5)
                print(f"   ✅ OHLCV: {len(ohlcv)} candles")
                
                # Show latest candle
                if ohlcv:
                    latest = ohlcv[-1]
                    print(f"   📊 Latest: O:{latest[1]:.4f} H:{latest[2]:.4f} L:{latest[3]:.4f} C:{latest[4]:.4f}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking Phemex symbols: {e}")
        return False

async def test_specific_symbols():
    """Test specific symbol formats we've been trying."""
    print(f"\n🎯 TESTING SPECIFIC SYMBOL FORMATS")
    print("=" * 50)
    
    exchange = ccxt.phemex({
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Load markets first
    markets = exchange.load_markets()
    
    test_formats = [
        # BTC formats
        'BTC/USDT:USDT',
        'BTC/USD:BTC', 
        'BTCUSD',
        'BTC/USDT',
        'BTC/USD',
        # ADA formats
        'ADA/USDT:USDT',
        'ADA/USD:ADA',
        'ADAUSD',
        'ADA/USDT',
        'ADA/USD'
    ]
    
    for symbol_format in test_formats:
        print(f"\n🧪 Testing: {symbol_format}")
        
        # Check if symbol exists in markets
        if symbol_format in markets:
            print(f"   ✅ Symbol exists in markets")
            
            try:
                # Test OHLCV fetch
                ohlcv = exchange.fetch_ohlcv(symbol_format, '1h', limit=2)
                print(f"   ✅ OHLCV fetch successful: {len(ohlcv)} candles")
                
                if ohlcv:
                    latest = ohlcv[-1]
                    print(f"   📊 Price: ${latest[4]:.4f}")
                
            except Exception as e:
                print(f"   ❌ OHLCV fetch failed: {e}")
        else:
            print(f"   ❌ Symbol not found in markets")

async def main():
    """Main function."""
    print("🚀 PHEMEX SYMBOL CHECKER")
    print("=" * 60)
    
    # Check all available symbols
    await check_phemex_symbols()
    
    # Test specific formats
    await test_specific_symbols()
    
    print(f"\n🎉 SYMBOL CHECK COMPLETE!")

if __name__ == "__main__":
    asyncio.run(main())
