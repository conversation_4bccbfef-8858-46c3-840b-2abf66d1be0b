#!/usr/bin/env python3
"""
Nike Rocket System Launcher

Runs both bots simultaneously:
1. Pure Execution Bot (trades on CALEB's account)
2. Signal Monitor Bot (sends signals to AXON)

This gives you full visibility while keeping the execution bot pure.
"""

import asyncio
import subprocess
import sys
import time
import signal
import os
from datetime import datetime

class NikeRocketSystemLauncher:
    """Launcher for both Nike Rocket bots."""
    
    def __init__(self):
        self.execution_bot_process = None
        self.monitor_bot_process = None
        self.running = True
    
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully."""
        print("\n🛑 Shutting down Nike Rocket System...")
        self.running = False
        
        if self.execution_bot_process:
            print("⏹️ Stopping execution bot...")
            self.execution_bot_process.terminate()
        
        if self.monitor_bot_process:
            print("⏹️ Stopping signal monitor...")
            self.monitor_bot_process.terminate()
        
        print("✅ Nike Rocket System stopped")
        sys.exit(0)
    
    def start_execution_bot(self):
        """Start the pure execution bot."""
        try:
            print("🚀 Starting Nike Rocket Pure Execution Bot...")
            self.execution_bot_process = subprocess.Popen([
                sys.executable, 'nike_rocket_pure_execution_bot.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print("✅ Execution bot started (PID: {})".format(self.execution_bot_process.pid))
            return True
            
        except Exception as e:
            print(f"❌ Error starting execution bot: {e}")
            return False
    
    def start_monitor_bot(self):
        """Start the signal monitor bot."""
        try:
            print("👁️  Starting Nike Rocket Signal Monitor...")
            self.monitor_bot_process = subprocess.Popen([
                sys.executable, 'nike_rocket_signal_monitor.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            print("✅ Signal monitor started (PID: {})".format(self.monitor_bot_process.pid))
            return True
            
        except Exception as e:
            print(f"❌ Error starting signal monitor: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor both processes and restart if needed."""
        while self.running:
            try:
                # Check execution bot
                if self.execution_bot_process and self.execution_bot_process.poll() is not None:
                    print("⚠️ Execution bot stopped, restarting...")
                    self.start_execution_bot()
                
                # Check monitor bot
                if self.monitor_bot_process and self.monitor_bot_process.poll() is not None:
                    print("⚠️ Signal monitor stopped, restarting...")
                    self.start_monitor_bot()
                
                time.sleep(10)  # Check every 10 seconds
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error monitoring processes: {e}")
                time.sleep(30)
    
    def run_system(self):
        """Run the complete Nike Rocket system."""
        print("🚀 NIKE ROCKET SYSTEM LAUNCHER")
        print("=" * 50)
        print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎯 Running both execution bot and signal monitor")
        print("📡 Signals will be sent to AXON when trades execute")
        print("👁️  Full visibility into CALEB's trading activity")
        print("=" * 50)
        
        # Set up signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        # Start both bots
        execution_started = self.start_execution_bot()
        time.sleep(2)  # Small delay
        monitor_started = self.start_monitor_bot()
        
        if execution_started and monitor_started:
            print("\n✅ NIKE ROCKET SYSTEM RUNNING")
            print("=" * 50)
            print("🚀 Execution Bot: Trading on CALEB's accounts")
            print("👁️  Signal Monitor: Watching trades and sending to AXON")
            print("📊 Both bots running in parallel")
            print("⏹️  Press Ctrl+C to stop both bots")
            print("=" * 50)
            
            # Monitor processes
            self.monitor_processes()
        else:
            print("❌ Failed to start system")
            return False
        
        return True

def run_execution_bot_only():
    """Run only the execution bot."""
    print("🚀 RUNNING EXECUTION BOT ONLY")
    print("=" * 40)
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, 'nike_rocket_pure_execution_bot.py'
        ])
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running execution bot: {e}")
        return False

def run_monitor_bot_only():
    """Run only the signal monitor."""
    print("👁️  RUNNING SIGNAL MONITOR ONLY")
    print("=" * 40)
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, 'nike_rocket_signal_monitor.py'
        ])
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running signal monitor: {e}")
        return False

def main():
    """Main launcher function."""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'execution':
            return run_execution_bot_only()
        elif mode == 'monitor':
            return run_monitor_bot_only()
        elif mode == 'both':
            launcher = NikeRocketSystemLauncher()
            return launcher.run_system()
        else:
            print("❌ Invalid mode. Use: execution, monitor, or both")
            return False
    else:
        # Default: run both
        launcher = NikeRocketSystemLauncher()
        return launcher.run_system()

if __name__ == "__main__":
    print("🚀 NIKE ROCKET SYSTEM")
    print("=" * 30)
    print("Usage:")
    print("  python run_nike_rocket_system.py           # Run both bots")
    print("  python run_nike_rocket_system.py both      # Run both bots")
    print("  python run_nike_rocket_system.py execution # Run execution bot only")
    print("  python run_nike_rocket_system.py monitor   # Run signal monitor only")
    print("=" * 30)
    
    success = main()
    sys.exit(0 if success else 1)
