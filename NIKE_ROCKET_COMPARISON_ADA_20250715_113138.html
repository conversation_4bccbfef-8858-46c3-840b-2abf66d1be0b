
<!DOCTYPE html>
<html>
<head>
    <title>🚀 NIKE'S ROCKET ALGORITHMS - ADA COMPARISON</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .header { text-align: center; margin-bottom: 30px; }
        .chart-container { width: 100%; height: 600px; margin: 20px 0; background: white; border-radius: 10px; padding: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .winner { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .risk-control { background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); }
        .metric { display: inline-block; margin: 15px 30px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; display: block; }
        .metric-label { font-size: 14px; opacity: 0.9; }
        .algorithm-header { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .comparison { display: flex; justify-content: space-around; flex-wrap: wrap; }
        .algo-card { flex: 1; min-width: 300px; margin: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 NIKE'S ROCKET ALGORITHMS COMPARISON</h1>
        <h2>ADA Performance Analysis</h2>
        <p><strong>CALEB's Latest Algorithm Refinements</strong></p>
    </div>
    
    <div class="summary winner">
        <h3>🏆 PERFORMANCE CHAMPION: MASSIVE ROCKET</h3>
        <div class="comparison">

            <div class="algo-card">
                <div class="algorithm-header">🛡️ BABY ROCKET (Lower Risk)</div>
                <div class="metric">
                    <span class="metric-value">364,378%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">42.4%</span>
                    <span class="metric-label">Win Rate</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-16.64%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
                <div class="metric">
                    <span class="metric-value">2,139</span>
                    <span class="metric-label">Total Trades</span>
                </div>
            </div>
            
            <div class="algo-card">
                <div class="algorithm-header">🚀 MASSIVE ROCKET (Higher Risk)</div>
                <div class="metric">
                    <span class="metric-value">520,270,414%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">42.4%</span>
                    <span class="metric-label">Win Rate</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-30.80%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
                <div class="metric">
                    <span class="metric-value">2,139</span>
                    <span class="metric-label">Total Trades</span>
                </div>
            </div>

        </div>
        <div style="text-align: center; margin-top: 20px; font-size: 18px;">
            <strong>🏆 MASSIVE ROCKET DELIVERS 1,428x BETTER RETURNS!</strong>
        </div>
    </div>
    
    <div class="summary risk-control">
        <h3>🛡️ RISK CONTROL CHAMPION: BABY ROCKET</h3>
        <div style="text-align: center;">
            <p><strong>Baby Rocket Drawdown:</strong> -16.64% vs <strong>Massive Rocket:</strong> -30.80%</p>
            <p><strong>🛡️ BABY ROCKET HAS 14.16% BETTER RISK CONTROL</strong></p>
        </div>
    </div>

    <div class="chart-container" id="equityChart"></div>
    <div class="chart-container" id="drawdownChart"></div>
    
    <script>

        var Nikes_Baby_Rocket_dates = ['2020-10-15 17:00:00', '2020-10-28 05:00:00', '2020-11-03 21:00:00', '2020-11-09 23:00:00', '2020-11-17 05:00:00', '2020-11-22 17:00:00', '2020-11-27 01:00:00', '2020-12-01 17:00:00', '2020-12-06 16:00:00', '2020-12-11 16:00:00', '2020-12-17 12:00:00', '2020-12-22 01:00:00', '2020-12-26 15:00:00', '2020-12-30 23:00:00', '2021-01-04 12:00:00', '2021-01-08 16:00:00', '2021-01-12 20:00:00', '2021-01-17 00:00:00', '2021-01-21 04:00:00', '2021-01-25 08:00:00', '2021-01-29 12:00:00', '2021-02-02 19:00:00', '2021-02-06 23:00:00', '2021-02-11 03:00:00', '2021-02-15 07:00:00', '2021-02-19 11:00:00', '2021-02-23 15:00:00', '2021-02-27 19:00:00', '2021-03-03 23:00:00', '2021-03-08 03:00:00', '2021-03-12 07:00:00', '2021-03-16 11:00:00', '2021-03-20 15:00:00', '2021-03-24 19:00:00', '2021-03-28 23:00:00', '2021-04-02 03:00:00', '2021-04-06 08:00:00', '2021-04-10 12:00:00', '2021-04-14 16:00:00', '2021-04-18 20:00:00', '2021-04-23 00:00:00', '2021-04-27 04:00:00', '2021-05-01 08:00:00', '2021-05-05 12:00:00', '2021-05-09 16:00:00', '2021-05-13 20:00:00', '2021-05-18 00:00:00', '2021-05-22 04:00:00', '2021-05-26 08:00:00', '2021-05-30 12:00:00', '2021-06-03 16:00:00', '2021-06-07 20:00:00', '2021-06-12 00:00:00', '2021-06-16 04:00:00', '2021-06-20 08:00:00', '2021-06-24 12:00:00', '2021-06-28 16:00:00', '2021-07-02 20:00:00', '2021-07-07 00:00:00', '2021-07-11 04:00:00', '2021-07-15 08:00:00', '2021-07-19 13:00:00', '2021-07-23 22:00:00', '2021-07-28 02:00:00', '2021-08-01 06:00:00', '2021-08-05 10:00:00', '2021-08-09 14:00:00', '2021-08-13 18:00:00', '2021-08-17 22:00:00', '2021-08-22 02:00:00', '2021-08-26 06:00:00', '2021-08-30 10:00:00', '2021-09-03 14:00:00', '2021-09-07 18:00:00', '2021-09-11 22:00:00', '2021-09-16 02:00:00', '2021-09-20 06:00:00', '2021-09-24 10:00:00', '2021-09-28 14:00:00', '2021-10-02 18:00:00', '2021-10-06 22:00:00', '2021-10-11 02:00:00', '2021-10-15 06:00:00', '2021-10-19 10:00:00', '2021-10-23 15:00:00', '2021-10-27 19:00:00', '2021-10-31 23:00:00', '2021-11-05 03:00:00', '2021-11-09 07:00:00', '2021-11-13 11:00:00', '2021-11-17 15:00:00', '2021-11-21 19:00:00', '2021-11-25 23:00:00', '2021-11-30 03:00:00', '2021-12-04 07:00:00', '2021-12-08 11:00:00', '2021-12-12 15:00:00', '2021-12-16 20:00:00', '2021-12-21 00:00:00', '2021-12-25 04:00:00', '2021-12-29 08:00:00', '2022-01-02 12:00:00', '2022-01-06 16:00:00', '2022-01-10 20:00:00', '2022-01-15 00:00:00', '2022-01-19 04:00:00', '2022-01-23 08:00:00', '2022-01-27 12:00:00', '2022-01-31 16:00:00', '2022-02-04 20:00:00', '2022-02-09 00:00:00', '2022-02-13 04:00:00', '2022-02-17 08:00:00', '2022-02-21 12:00:00', '2022-02-25 16:00:00', '2022-03-01 20:00:00', '2022-03-06 00:00:00', '2022-03-10 04:00:00', '2022-03-14 08:00:00', '2022-03-18 12:00:00', '2022-03-22 16:00:00', '2022-03-26 20:00:00', '2022-03-31 00:00:00', '2022-04-04 04:00:00', '2022-04-08 08:00:00', '2022-04-12 12:00:00', '2022-04-16 16:00:00', '2022-04-20 20:00:00', '2022-04-25 01:00:00', '2022-04-29 06:00:00', '2022-05-03 10:00:00', '2022-05-07 14:00:00', '2022-05-11 18:00:00', '2022-05-15 22:00:00', '2022-05-20 02:00:00', '2022-05-24 06:00:00', '2022-05-28 11:00:00', '2022-06-01 15:00:00', '2022-06-05 19:00:00', '2022-06-09 23:00:00', '2022-06-14 03:00:00', '2022-06-18 07:00:00', '2022-06-22 11:00:00', '2022-06-26 15:00:00', '2022-06-30 19:00:00', '2022-07-04 23:00:00', '2022-07-09 03:00:00', '2022-07-13 07:00:00', '2022-07-17 13:00:00', '2022-07-21 17:00:00', '2022-07-25 21:00:00', '2022-07-30 01:00:00', '2022-08-03 05:00:00', '2022-08-07 10:00:00', '2022-08-11 15:00:00', '2022-08-15 19:00:00', '2022-08-19 23:00:00', '2022-08-24 04:00:00', '2022-08-28 12:00:00', '2022-09-01 17:00:00', '2022-09-05 22:00:00', '2022-09-10 02:00:00', '2022-09-14 07:00:00', '2022-09-18 14:00:00', '2022-09-22 20:00:00', '2022-09-27 01:00:00', '2022-10-01 05:00:00', '2022-10-05 11:00:00', '2022-10-09 16:00:00', '2022-10-13 22:00:00', '2022-10-18 08:00:00', '2022-10-22 16:00:00', '2022-10-26 20:00:00', '2022-10-31 02:00:00', '2022-11-04 07:00:00', '2022-11-08 11:00:00', '2022-11-12 15:00:00', '2022-11-16 21:00:00', '2022-11-21 04:00:00', '2022-11-25 09:00:00', '2022-11-29 16:00:00', '2022-12-03 20:00:00', '2022-12-08 04:00:00', '2022-12-12 18:00:00', '2022-12-17 00:00:00', '2022-12-21 06:00:00', '2022-12-25 17:00:00', '2022-12-30 04:00:00', '2023-01-03 12:00:00', '2023-01-07 16:00:00', '2023-01-11 20:00:00', '2023-01-16 00:00:00', '2023-01-20 05:00:00', '2023-01-24 09:00:00', '2023-01-28 13:00:00', '2023-02-01 17:00:00', '2023-02-05 22:00:00', '2023-02-10 02:00:00', '2023-02-14 07:00:00', '2023-02-18 14:00:00', '2023-02-22 20:00:00', '2023-02-27 01:00:00', '2023-03-03 06:00:00', '2023-03-07 15:00:00', '2023-03-11 20:00:00', '2023-03-16 00:00:00', '2023-03-20 04:00:00', '2023-03-24 08:00:00', '2023-03-28 14:00:00', '2023-04-01 18:00:00', '2023-04-05 22:00:00', '2023-04-10 02:00:00', '2023-04-14 06:00:00', '2023-04-18 10:00:00', '2023-04-22 15:00:00', '2023-04-26 20:00:00', '2023-05-01 02:00:00', '2023-05-05 07:00:00', '2023-05-09 15:00:00', '2023-05-13 20:00:00', '2023-05-18 00:00:00', '2023-05-22 05:00:00', '2023-05-26 09:00:00', '2023-05-30 14:00:00', '2023-06-03 19:00:00', '2023-06-08 00:00:00', '2023-06-12 04:00:00', '2023-06-16 09:00:00', '2023-06-20 16:00:00', '2023-06-24 22:00:00', '2023-06-29 03:00:00', '2023-07-03 08:00:00', '2023-07-07 13:00:00', '2023-07-11 18:00:00', '2023-07-16 00:00:00', '2023-07-20 04:00:00', '2023-07-24 10:00:00', '2023-07-28 16:00:00', '2023-08-01 22:00:00', '2023-08-06 04:00:00', '2023-08-10 10:00:00', '2023-08-14 17:00:00', '2023-08-18 21:00:00', '2023-08-23 01:00:00', '2023-08-27 07:00:00', '2023-08-31 11:00:00', '2023-09-04 17:00:00', '2023-09-08 23:00:00', '2023-09-13 06:00:00', '2023-09-17 14:00:00', '2023-09-21 21:00:00', '2023-09-26 06:00:00', '2023-09-30 11:00:00', '2023-10-04 15:00:00', '2023-10-08 21:00:00', '2023-10-13 02:00:00', '2023-10-17 12:00:00', '2023-10-21 17:00:00', '2023-10-25 22:00:00', '2023-10-30 02:00:00', '2023-11-03 06:00:00', '2023-11-07 10:00:00', '2023-11-11 14:00:00', '2023-11-15 18:00:00', '2023-11-19 22:00:00', '2023-11-24 02:00:00', '2023-11-28 06:00:00', '2023-12-02 10:00:00', '2023-12-06 14:00:00', '2023-12-10 18:00:00', '2023-12-14 22:00:00', '2023-12-19 02:00:00', '2023-12-23 06:00:00', '2023-12-27 10:00:00', '2023-12-31 14:00:00', '2024-01-04 18:00:00', '2024-01-09 00:00:00', '2024-01-13 04:00:00', '2024-01-17 09:00:00', '2024-01-21 20:00:00', '2024-01-26 00:00:00', '2024-01-30 05:00:00', '2024-02-03 09:00:00', '2024-02-07 14:00:00', '2024-02-11 18:00:00', '2024-02-15 22:00:00', '2024-02-20 02:00:00', '2024-02-24 06:00:00', '2024-02-28 10:00:00', '2024-03-03 14:00:00', '2024-03-07 18:00:00', '2024-03-11 22:00:00', '2024-03-16 02:00:00', '2024-03-20 06:00:00', '2024-03-24 10:00:00', '2024-03-28 14:00:00', '2024-04-01 18:00:00', '2024-04-05 22:00:00', '2024-04-10 02:00:00', '2024-04-14 12:00:00', '2024-04-18 16:00:00', '2024-04-22 20:00:00', '2024-04-27 00:00:00', '2024-05-01 04:00:00', '2024-05-05 08:00:00', '2024-05-09 12:00:00', '2024-05-13 20:00:00', '2024-05-18 00:00:00', '2024-05-22 04:00:00', '2024-05-26 08:00:00', '2024-05-30 12:00:00', '2024-06-03 16:00:00', '2024-06-07 21:00:00', '2024-06-12 01:00:00', '2024-06-16 05:00:00', '2024-06-20 09:00:00', '2024-06-24 15:00:00', '2024-06-28 19:00:00', '2024-07-03 00:00:00', '2024-07-07 04:00:00', '2024-07-11 09:00:00', '2024-07-15 13:00:00', '2024-07-19 17:00:00', '2024-07-23 21:00:00', '2024-07-28 02:00:00', '2024-08-01 06:00:00', '2024-08-05 12:00:00', '2024-08-09 16:00:00', '2024-08-13 20:00:00', '2024-08-18 00:00:00', '2024-08-22 04:00:00', '2024-08-26 08:00:00', '2024-08-30 12:00:00', '2024-09-03 18:00:00', '2024-09-07 22:00:00', '2024-09-12 02:00:00', '2024-09-16 07:00:00', '2024-09-20 13:00:00', '2024-09-24 18:00:00', '2024-09-28 22:00:00', '2024-10-03 02:00:00', '2024-10-07 06:00:00', '2024-10-11 10:00:00', '2024-10-15 14:00:00', '2024-10-19 18:00:00', '2024-10-23 22:00:00', '2024-10-28 02:00:00', '2024-11-01 06:00:00', '2024-11-05 10:00:00', '2024-11-09 14:00:00', '2024-11-13 18:00:00', '2024-11-17 22:00:00', '2024-11-22 02:00:00', '2024-11-26 06:00:00', '2024-11-30 10:00:00', '2024-12-04 14:00:00', '2024-12-08 18:00:00', '2024-12-12 22:00:00', '2024-12-17 02:00:00', '2024-12-21 06:00:00', '2024-12-25 10:00:00', '2024-12-29 14:00:00', '2025-01-02 18:00:00', '2025-01-06 22:00:00', '2025-01-11 02:00:00', '2025-01-15 06:00:00', '2025-01-19 10:00:00', '2025-01-23 14:00:00', '2025-01-27 21:00:00', '2025-02-01 01:00:00', '2025-02-05 05:00:00', '2025-02-09 09:00:00', '2025-02-13 13:00:00', '2025-02-17 17:00:00', '2025-02-21 21:00:00', '2025-02-26 01:00:00', '2025-03-02 05:00:00', '2025-03-06 09:00:00', '2025-03-10 13:00:00', '2025-03-14 17:00:00', '2025-03-18 21:00:00', '2025-03-23 01:00:00', '2025-03-27 05:00:00', '2025-03-31 10:00:00'];
        var Nikes_Baby_Rocket_equity = [10000.0, 10000.0, 10000.0, 10328.198654047312, 10808.75940922148, 11966.001893050628, 11684.876234375864, 12626.435319609163, 12455.60785691781, 12491.8764384048, 13590.229015062096, 13454.326724911476, 13306.01006764707, 13636.95354628593, 13833.679445336797, 14706.188423418274, 14451.620158046191, 14533.766354570213, 15004.669104718098, 14854.622413670915, 14269.29102062885, 15379.430340374012, 16019.331255526184, 17773.460651239333, 17595.72604472694, 17595.72604472694, 17515.86565751781, 18363.24876129792, 18297.783779463887, 17754.321303430028, 17103.620430918632, 17793.402316389074, 19301.69020576132, 18917.58657066667, 19086.96623300417, 20239.39908022049, 19695.451938957376, 20617.402445241845, 22884.587995437985, 22204.89284738548, 23160.874242334725, 24245.04229290254, 24689.42918387269, 24663.898953709016, 24792.61162104877, 24430.793464279453, 25635.84723806373, 26737.10883308596, 27433.270609323252, 28213.572367623485, 28077.201501313128, 27869.87599193165, 30338.747536594245, 29823.28969479759, 32157.272184667287, 32462.38998210116, 32673.39551698482, 31589.94193365149, 32819.98955297025, 33583.87480981563, 37308.46542249655, 38479.64656047904, 37073.13927181949, 37556.656422487365, 38182.60069619549, 37289.35477819176, 41367.12683181824, 43650.28151995898, 42144.80596029208, 45836.81992897576, 48977.58812546485, 51917.84008236563, 55755.55630831077, 56117.96742431479, 55001.21987257093, 51777.27330762986, 52796.51669928155, 51561.392004615365, 52219.8309805143, 52771.67325830181, 50788.43586460945, 51785.2617459894, 51936.05149523608, 52067.96906603398, 51692.11148487375, 51122.272403429415, 51271.131642076216, 52263.38210910098, 50661.94753923161, 50155.32806383929, 51841.10436820721, 51972.78077330246, 57452.40713804771, 59694.99056959479, 61025.17565127732, 59357.01024542811, 60602.54367470063, 62572.48674745563, 62907.66479071099, 62056.564976074784, 59009.15448092598, 63830.43858615844, 62489.14570769112, 66419.85683341089, 60839.94497148997, 64509.6616985048, 65351.01283379212, 65942.8649350315, 64311.64620294052, 62966.6660432928, 64432.21519545045, 67012.72541402823, 65839.27898184824, 73453.87523770807, 74987.56214751555, 73349.73680831506, 73180.60719490403, 75498.17039072594, 77259.79436650954, 76487.19642284444, 79803.5928117155, 81369.8998929945, 86176.88911570323, 85869.66850600576, 86338.4502218446, 90062.9103832294, 91805.89833819056, 84938.91534790097, 95810.45673553411, 104981.81050872264, 115952.17507360296, 116129.9140915636, 133578.60079088574, 132621.9108520214, 139535.34134933565, 141355.19615504905, 149345.60183179978, 150316.34824370648, 147310.02127883234, 151718.46332725385, 156258.83367984346, 155483.10030768416, 157709.99761654448, 156132.89764037903, 161239.4628419747, 162287.51935044752, 164404.11374969594, 185791.38493636675, 183792.66709632165, 192447.7809471086, 191517.5807489451, 200536.47411393825, 204936.67784219803, 200837.94428535405, 211370.01592918488, 210488.72513223856, 229241.13006150324, 221874.27240110227, 218872.45771379245, 215710.00047171657, 212011.34856745283, 228492.63473166, 223147.26512491365, 215725.663120026, 214016.67724545175, 219546.4401521198, 226129.5401600811, 222628.31730875248, 216564.7143818524, 278385.6113243024, 296637.8405489412, 369953.3026681939, 366890.0512471013, 364278.7880699211, 366646.6001923756, 374952.72126290976, 392526.7553085024, 392872.2884501707, 425469.23444333125, 409875.6672577728, 409627.0430372352, 422843.5816410941, 431026.875590812, 472259.5416279308, 517676.22925261647, 533823.3307615878, 512736.2415498436, 519025.8488835226, 517829.0805651652, 514143.54042703216, 524264.53314186935, 562928.12186829, 557298.8406496071, 602187.8223196259, 629638.1661875689, 625192.48628395, 622963.6750703478, 635190.5984614341, 632926.1439779191, 615852.9960806862, 615726.4332359122, 629377.6098631384, 693880.5735708168, 740241.477832284, 790146.2955487927, 786640.379724308, 775997.646702885, 793105.6333824645, 808671.9371790884, 877080.8813559442, 856475.0062536321, 852422.1901627502, 971743.4006331208, 940611.5753054952, 917615.6430337664, 932290.2022073688, 944449.364097108, 934133.333903756, 994214.7659848888, 1043397.7981774838, 1063590.6809826016, 1134653.379107542, 1152798.8175377245, 1152033.874233696, 1159522.094416215, 1243878.4559340435, 1312472.1008901566, 1346246.3829530631, 1346730.9268280894, 1363928.680763684, 1404746.0025937136, 1349255.7259992566, 1398746.2944764767, 1416989.093335612, 1390694.8846043872, 1380796.9047313528, 1384817.5471304646, 1357259.6779425682, 1383898.6470640665, 1514508.6380342897, 1492268.6047099535, 1554501.6323523682, 1769843.239044686, 1769843.239044686, 1769843.239044686, 1752144.8066542393, 1804095.0240991355, 1862804.426089432, 2046187.190058992, 1926248.065474104, 2081402.3701236304, 2234823.4546424863, 2331229.0642307666, 2374903.8977454635, 2452102.397975672, 2515805.074605845, 2251577.371706147, 2244587.0179884066, 2288641.58266342, 2380301.6780490894, 2366983.091018761, 2496951.555439908, 2603550.729576973, 2439343.447538529, 2382034.244385094, 2347902.3248042245, 2441935.812912634, 2505517.196509018, 2799238.310875618, 3255679.0167558603, 3344918.712198886, 3254689.71404332, 3314344.8561388897, 3374530.6815139367, 3510824.2262617494, 3494567.655075186, 3705693.129277719, 3816590.767251259, 3816590.767251259, 4207741.3450428685, 4656828.365875295, 4700745.77037786, 4515056.910956394, 4544404.780877612, 4646406.782996271, 5053446.235572506, 4981035.932939203, 4858812.238794343, 5303365.557489048, 5359904.039014184, 5388975.214106706, 5388975.214106706, 5424003.552998399, 5772863.709519558, 5788117.347299222, 5596182.705017395, 5763984.243427342, 6173604.9130229335, 6050750.175253777, 6305426.18569686, 6468942.280800063, 6214040.14385684, 6791860.913661955, 6699971.450209702, 6743521.264636065, 6829636.031185467, 7051639.818044472, 7403223.692286356, 7040392.068548265, 7040392.068548265, 6991712.377682517, 6914637.736222233, 6821087.182165675, 7167857.359699755, 7354069.689124949, 7838789.360516676, 8810176.547791569, 8564146.243931547, 8555290.444830863, 8354295.017707141, 8442580.504376005, 10044487.773518473, 10073735.589240815, 10037822.721865172, 10195607.373038288, 10907996.414729154, 12249784.82627997, 12147115.845573291, 12309416.289564852, 12547640.484563882, 13134999.613629466, 13839227.26031205, 14107057.894677, 15855212.44492647, 15792465.510833291, 15634540.85572496, 16531715.953946687, 17755148.90907864, 19216142.73513106, 20219921.60981556, 19946359.2133838, 19597082.50647008, 21042332.929433275, 21316772.296082184, 22144544.08941149, 21909800.43701405, 21649735.749782298, 22113005.27861329, 23298585.037007704, 24554328.022492837, 26030788.44234789, 27787288.59597984, 30826872.994337905, 31076579.713621784, 31127391.851930685, 30507956.754077263, 29009803.131913926, 29380258.31790847, 27934632.0876341, 28770611.918695364, 28846632.506568134, 29483467.90068284, 29569318.67331142, 30423091.553634256, 29673547.7175982, 31336395.611146983, 31275735.654495347, 30653348.51497089, 31253575.13817015, 31344580.157730788, 32378454.1355038, 33457735.94002059, 33978994.75202645, 33302812.75646113, 32852247.346694503, 34401277.34977489, 36168102.97498895, 36146163.96487244, 35971450.758424945, 36917971.26437725, 36004736.96167324, 34807263.9481876, 34950173.05282287, 36447799.7322];
        var Nikes_Baby_Rocket_drawdown = [0.0, 0.0, 0.0, -0.9999999999999962, 0.0, 0.0, -2.349370000000004, -1.0000000000000009, -2.3394056500000096, -2.055034845705336, -0.9999999999999936, -1.9899999999999969, -3.070434262947485, -2.980000000000007, -3.080324025802002, -1.989999999999997, -3.686580716277703, -3.1391139975391744, -1.3630000000000062, -2.349370000000003, -6.197194448013714, -2.0000000000000027, -2.9800000000000004, -0.9999999999999966, -1.9899999999999944, -1.9899999999999944, -5.861591020000001, -1.9899999999999904, -2.339405650000003, -5.240022962789365, -8.713002790261585, -5.031436229041638, -1.9899999999999904, -3.9403990000000015, -3.9600000000000017, -0.9999999999999952, -3.660690012169152, 0.0, 0.0, -2.9701000000000097, -1.3629999999999918, -1.0000000000000044, -1.9899999999999969, -3.325876299999996, -2.8213662731176026, -4.23957080396137, -2.980000000000007, 0.0, -2.970100000000002, -0.9999999999999984, -2.000000000000004, -2.72364405399999, 0.0, -3.9403990000000046, -0.3565000000000093, -1.3529350000000042, -0.999999999999996, -4.900995010000004, -2.970099999999999, -1.0000000000000022, 0.0, 0.0, -5.851985059900018, -4.624083112047515, -3.0344844972482976, -5.302901245471288, 0.0, -2.000000000000007, -7.753772079999999, 0.0, -4.91069799999999, -0.9999999999999992, -1.0000000000000049, -0.3565000000000062, -2.339405650000008, -8.063870278498216, -6.254093774510998, -8.447190801393788, -7.278061427927593, -6.298205981792979, -9.81965774329711, -8.049685945133831, -7.781942492035204, -7.547708625964984, -8.215084274216137, -9.2268949850812, -8.962579361638365, -7.200731704837038, -10.044251398917035, -10.94380888492787, -7.950531350226851, -7.716725699856425, -1.9899999999999995, -3.950199999999995, -1.809919721211132, -5.871195999999998, -5.8615910200000005, -2.801532887031805, -2.707422310000005, -4.023727645970202, -8.736832532934095, -1.2802664700470308, -3.706233508, 0.0, -8.400969420810435, -2.8759398559034635, -1.6092235824891303, -2.3493699999999897, -4.764938947655226, -6.756635244995324, -4.586395930322578, -0.7650810873320022, -4.920400000000016, -0.9999999999999984, -1.990000000000009, -4.910698000000012, -5.12995464067226, -4.3023825999999845, -2.06943819399999, -3.0487438120599912, 0.0, -2.9701000000000177, -4.910698000000007, -5.249691361629994, -4.732428246113858, -1.9900000000000024, -1.0000000000000075, -8.405094099012443, 0.0, -0.9999999999999936, -1.0000000000000018, -2.3593333333333266, 0.0, -0.7162000000000202, -2.0000000000000027, -0.7218594999999813, 0.0, 0.0, -2.000000000000007, -0.9999999999999944, 0.0, -2.7335692, -1.3404766223350553, -2.32707185611171, -1.0000000000000018, -0.3565000000000054, -0.9999999999999916, 0.0, -1.0757860708824778, -0.9999999999999938, -2.980000000000009, 0.0, -0.1034055099999973, -2.101337399800004, -1.0000000000000002, -1.4127727791198017, 0.0, -6.335727035583992, -7.602943767015098, -8.937975788324017, -10.499362504378414, -3.541783919970705, -5.798332876836127, -8.93136379789033, -9.652812561265083, -7.318421932223042, -4.539364813860754, -6.017406811732629, -8.577158122899894, 0.0, -2.000000000000009, -2.9799999999999947, -3.940398999999987, -4.624083112047464, -4.004139652275764, -1.829420896122224, -0.9999999999999928, -2.7018759999999937, 0.0, -4.910697999999987, -4.968377695282657, -1.9022004832877295, -1.9899999999999969, 0.0, 0.0, 0.0, -3.9502000000000015, -2.7719811078609364, -4.678897816000053, -5.357325817237044, -3.494270579548227, 0.0, -0.9999999999999968, -1.0000000000000042, -2.9799999999999924, -3.665028143799988, -4.008462318467328, -2.12443404460026, -2.4733604372312583, -5.104136171521935, -5.123638050314197, -3.0201487329608803, 0.0, 0.0, 0.0, -0.443704646118684, -1.7906366106647085, -1.9899999999999969, -0.0663553165037488, -2.000000000000003, -4.302382599999994, -4.755221084282546, 0.0, -9.598696638400009, -11.808814293718228, -10.398456064690755, -9.229850328300463, -10.2213143921929, -4.446943854384231, -2.3493699999999924, -1.0000000000000109, -2.9800000000000044, -1.4284508935398343, -1.4938583569320512, -1.0, 0.0, -1.999999999999995, 0.0, -2.000000000000003, -0.7485400000000121, 0.0, -3.950200000000008, -0.4271027008554591, -0.9999999999999944, -3.4645634012929194, -4.151634173657488, -3.872540266214275, -5.785476714916619, -3.936325946367673, -2.3493699999999773, -5.861591020000002, -3.960000000000007, 0.0, 0.0, 0.0, -0.9999999999999952, -0.3564999999999627, -1.362999999999998, 0.0, -5.861591020000006, 0.0, -1.9899999999999984, -1.9900000000000024, -2.9701000000000097, -1.990000000000005, -1.0000000000000004, -11.397682575295851, -11.672761525188164, -9.93916064058481, -6.33222402424024, -6.856326678023558, -3.9502000000000015, -2.000000000000005, -10.484443987012687, -12.587495605314992, -13.840020239414857, -10.389313050003407, -8.056093875619213, 0.0, 0.0, -1.9999999999999944, -4.643544606030988, -2.980000000000004, -3.9501999999999935, -0.0708552999999992, -3.940399000000002, 0.0, -0.3564999999999904, -0.3564999999999904, -2.970099999999998, 0.0, 0.0, -3.950200000000005, -3.325876299999982, -2.9799999999999995, 0.0, -3.96, -6.316610902573007, 0.0, -5.880800000000009, -5.3703140430030345, -5.3703140430030345, -4.7552210842825575, 0.0, -0.9999999999999926, -4.900995010000008, -2.0494513453748606, -0.3666666666666586, -2.3493699999999973, 0.0, -1.999999999999995, -5.861591019999996, 0.0, -1.3529350000000029, -1.0000000000000056, -1.0000000000000004, -1.9899999999999929, -2.339405649999994, -7.125746505829827, -7.125746505829827, -7.767911019604632, -8.78465124078251, -10.018735619518026, -5.444271433582502, -2.9878271165060637, -2.9799999999999978, 0.0, -4.910698000000001, -5.00902557768855, -7.240715033473995, -6.260464922726388, 0.0, -1.989999999999998, -2.33940565000002, -2.970099999999998, 0.0, 0.0, -2.3493700000000044, -1.7190666666666738, -1.352935000000007, -1.9899999999999975, -1.9900000000000069, -0.0932119799500484, 0.0, -2.8945953111443155, -3.865649358032871, 0.0, -1.0, 0.0, -1.9900000000000095, -3.3160115935000136, -5.009025577688552, -2.979999999999995, -1.71464090499996, -2.9799999999999924, -4.008462318467385, -5.1478615245810575, -3.1181782985139037, -0.9999999999999952, 0.0, 0.0, -1.000000000000001, 0.0, -3.96, -3.8029686339952073, -5.717289558178706, -10.347228734191017, -9.202362845126627, -13.669969783695016, -11.086432479558187, -10.851496160098922, -8.883400835117985, -8.618085015963194, -5.9795595353015045, -8.295972431323955, -3.1570571076912004, -3.3445224687608364, -5.267966471632492, -3.4130079973320235, -3.1317633378763787, -1.3529349999999962, 0.0, -2.9701000000000017, -4.900995009999997, -6.187622733161449, -1.764236244162218, -3.296590877728093, -3.355249665348269, -3.822383998985813, -1.3529349999999976, -3.7931526103864, -6.992873346694754, -6.611011525710814, -2.609261935293786];

        var Nikes_Massive_Rocket_dates = ['2020-10-15 17:00:00', '2020-10-28 05:00:00', '2020-11-03 21:00:00', '2020-11-09 23:00:00', '2020-11-17 05:00:00', '2020-11-22 17:00:00', '2020-11-27 01:00:00', '2020-12-01 17:00:00', '2020-12-06 16:00:00', '2020-12-11 16:00:00', '2020-12-17 12:00:00', '2020-12-22 01:00:00', '2020-12-26 15:00:00', '2020-12-30 23:00:00', '2021-01-04 12:00:00', '2021-01-08 16:00:00', '2021-01-12 20:00:00', '2021-01-17 00:00:00', '2021-01-21 04:00:00', '2021-01-25 08:00:00', '2021-01-29 12:00:00', '2021-02-02 19:00:00', '2021-02-06 23:00:00', '2021-02-11 03:00:00', '2021-02-15 07:00:00', '2021-02-19 11:00:00', '2021-02-23 15:00:00', '2021-02-27 19:00:00', '2021-03-03 23:00:00', '2021-03-08 03:00:00', '2021-03-12 07:00:00', '2021-03-16 11:00:00', '2021-03-20 15:00:00', '2021-03-24 19:00:00', '2021-03-28 23:00:00', '2021-04-02 03:00:00', '2021-04-06 08:00:00', '2021-04-10 12:00:00', '2021-04-14 16:00:00', '2021-04-18 20:00:00', '2021-04-23 00:00:00', '2021-04-27 04:00:00', '2021-05-01 08:00:00', '2021-05-05 12:00:00', '2021-05-09 16:00:00', '2021-05-13 20:00:00', '2021-05-18 00:00:00', '2021-05-22 04:00:00', '2021-05-26 08:00:00', '2021-05-30 12:00:00', '2021-06-03 16:00:00', '2021-06-07 20:00:00', '2021-06-12 00:00:00', '2021-06-16 04:00:00', '2021-06-20 08:00:00', '2021-06-24 12:00:00', '2021-06-28 16:00:00', '2021-07-02 20:00:00', '2021-07-07 00:00:00', '2021-07-11 04:00:00', '2021-07-15 08:00:00', '2021-07-19 13:00:00', '2021-07-23 22:00:00', '2021-07-28 02:00:00', '2021-08-01 06:00:00', '2021-08-05 10:00:00', '2021-08-09 14:00:00', '2021-08-13 18:00:00', '2021-08-17 22:00:00', '2021-08-22 02:00:00', '2021-08-26 06:00:00', '2021-08-30 10:00:00', '2021-09-03 14:00:00', '2021-09-07 18:00:00', '2021-09-11 22:00:00', '2021-09-16 02:00:00', '2021-09-20 06:00:00', '2021-09-24 10:00:00', '2021-09-28 14:00:00', '2021-10-02 18:00:00', '2021-10-06 22:00:00', '2021-10-11 02:00:00', '2021-10-15 06:00:00', '2021-10-19 10:00:00', '2021-10-23 15:00:00', '2021-10-27 19:00:00', '2021-10-31 23:00:00', '2021-11-05 03:00:00', '2021-11-09 07:00:00', '2021-11-13 11:00:00', '2021-11-17 15:00:00', '2021-11-21 19:00:00', '2021-11-25 23:00:00', '2021-11-30 03:00:00', '2021-12-04 07:00:00', '2021-12-08 11:00:00', '2021-12-12 15:00:00', '2021-12-16 20:00:00', '2021-12-21 00:00:00', '2021-12-25 04:00:00', '2021-12-29 08:00:00', '2022-01-02 12:00:00', '2022-01-06 16:00:00', '2022-01-10 20:00:00', '2022-01-15 00:00:00', '2022-01-19 04:00:00', '2022-01-23 08:00:00', '2022-01-27 12:00:00', '2022-01-31 16:00:00', '2022-02-04 20:00:00', '2022-02-09 00:00:00', '2022-02-13 04:00:00', '2022-02-17 08:00:00', '2022-02-21 12:00:00', '2022-02-25 16:00:00', '2022-03-01 20:00:00', '2022-03-06 00:00:00', '2022-03-10 04:00:00', '2022-03-14 08:00:00', '2022-03-18 12:00:00', '2022-03-22 16:00:00', '2022-03-26 20:00:00', '2022-03-31 00:00:00', '2022-04-04 04:00:00', '2022-04-08 08:00:00', '2022-04-12 12:00:00', '2022-04-16 16:00:00', '2022-04-20 20:00:00', '2022-04-25 01:00:00', '2022-04-29 06:00:00', '2022-05-03 10:00:00', '2022-05-07 14:00:00', '2022-05-11 18:00:00', '2022-05-15 22:00:00', '2022-05-20 02:00:00', '2022-05-24 06:00:00', '2022-05-28 11:00:00', '2022-06-01 15:00:00', '2022-06-05 19:00:00', '2022-06-09 23:00:00', '2022-06-14 03:00:00', '2022-06-18 07:00:00', '2022-06-22 11:00:00', '2022-06-26 15:00:00', '2022-06-30 19:00:00', '2022-07-04 23:00:00', '2022-07-09 03:00:00', '2022-07-13 07:00:00', '2022-07-17 13:00:00', '2022-07-21 17:00:00', '2022-07-25 21:00:00', '2022-07-30 01:00:00', '2022-08-03 05:00:00', '2022-08-07 10:00:00', '2022-08-11 15:00:00', '2022-08-15 19:00:00', '2022-08-19 23:00:00', '2022-08-24 04:00:00', '2022-08-28 12:00:00', '2022-09-01 17:00:00', '2022-09-05 22:00:00', '2022-09-10 02:00:00', '2022-09-14 07:00:00', '2022-09-18 14:00:00', '2022-09-22 20:00:00', '2022-09-27 01:00:00', '2022-10-01 05:00:00', '2022-10-05 11:00:00', '2022-10-09 16:00:00', '2022-10-13 22:00:00', '2022-10-18 08:00:00', '2022-10-22 16:00:00', '2022-10-26 20:00:00', '2022-10-31 02:00:00', '2022-11-04 07:00:00', '2022-11-08 11:00:00', '2022-11-12 15:00:00', '2022-11-16 21:00:00', '2022-11-21 04:00:00', '2022-11-25 09:00:00', '2022-11-29 16:00:00', '2022-12-03 20:00:00', '2022-12-08 04:00:00', '2022-12-12 18:00:00', '2022-12-17 00:00:00', '2022-12-21 06:00:00', '2022-12-25 17:00:00', '2022-12-30 04:00:00', '2023-01-03 12:00:00', '2023-01-07 16:00:00', '2023-01-11 20:00:00', '2023-01-16 00:00:00', '2023-01-20 05:00:00', '2023-01-24 09:00:00', '2023-01-28 13:00:00', '2023-02-01 17:00:00', '2023-02-05 22:00:00', '2023-02-10 02:00:00', '2023-02-14 07:00:00', '2023-02-18 14:00:00', '2023-02-22 20:00:00', '2023-02-27 01:00:00', '2023-03-03 06:00:00', '2023-03-07 15:00:00', '2023-03-11 20:00:00', '2023-03-16 00:00:00', '2023-03-20 04:00:00', '2023-03-24 08:00:00', '2023-03-28 14:00:00', '2023-04-01 18:00:00', '2023-04-05 22:00:00', '2023-04-10 02:00:00', '2023-04-14 06:00:00', '2023-04-18 10:00:00', '2023-04-22 15:00:00', '2023-04-26 20:00:00', '2023-05-01 02:00:00', '2023-05-05 07:00:00', '2023-05-09 15:00:00', '2023-05-13 20:00:00', '2023-05-18 00:00:00', '2023-05-22 05:00:00', '2023-05-26 09:00:00', '2023-05-30 14:00:00', '2023-06-03 19:00:00', '2023-06-08 00:00:00', '2023-06-12 04:00:00', '2023-06-16 09:00:00', '2023-06-20 16:00:00', '2023-06-24 22:00:00', '2023-06-29 03:00:00', '2023-07-03 08:00:00', '2023-07-07 13:00:00', '2023-07-11 18:00:00', '2023-07-16 00:00:00', '2023-07-20 04:00:00', '2023-07-24 10:00:00', '2023-07-28 16:00:00', '2023-08-01 22:00:00', '2023-08-06 04:00:00', '2023-08-10 10:00:00', '2023-08-14 17:00:00', '2023-08-18 21:00:00', '2023-08-23 01:00:00', '2023-08-27 07:00:00', '2023-08-31 11:00:00', '2023-09-04 17:00:00', '2023-09-08 23:00:00', '2023-09-13 06:00:00', '2023-09-17 14:00:00', '2023-09-21 21:00:00', '2023-09-26 06:00:00', '2023-09-30 11:00:00', '2023-10-04 15:00:00', '2023-10-08 21:00:00', '2023-10-13 02:00:00', '2023-10-17 12:00:00', '2023-10-21 17:00:00', '2023-10-25 22:00:00', '2023-10-30 02:00:00', '2023-11-03 06:00:00', '2023-11-07 10:00:00', '2023-11-11 14:00:00', '2023-11-15 18:00:00', '2023-11-19 22:00:00', '2023-11-24 02:00:00', '2023-11-28 06:00:00', '2023-12-02 10:00:00', '2023-12-06 14:00:00', '2023-12-10 18:00:00', '2023-12-14 22:00:00', '2023-12-19 02:00:00', '2023-12-23 06:00:00', '2023-12-27 10:00:00', '2023-12-31 14:00:00', '2024-01-04 18:00:00', '2024-01-09 00:00:00', '2024-01-13 04:00:00', '2024-01-17 09:00:00', '2024-01-21 20:00:00', '2024-01-26 00:00:00', '2024-01-30 05:00:00', '2024-02-03 09:00:00', '2024-02-07 14:00:00', '2024-02-11 18:00:00', '2024-02-15 22:00:00', '2024-02-20 02:00:00', '2024-02-24 06:00:00', '2024-02-28 10:00:00', '2024-03-03 14:00:00', '2024-03-07 18:00:00', '2024-03-11 22:00:00', '2024-03-16 02:00:00', '2024-03-20 06:00:00', '2024-03-24 10:00:00', '2024-03-28 14:00:00', '2024-04-01 18:00:00', '2024-04-05 22:00:00', '2024-04-10 02:00:00', '2024-04-14 12:00:00', '2024-04-18 16:00:00', '2024-04-22 20:00:00', '2024-04-27 00:00:00', '2024-05-01 04:00:00', '2024-05-05 08:00:00', '2024-05-09 12:00:00', '2024-05-13 20:00:00', '2024-05-18 00:00:00', '2024-05-22 04:00:00', '2024-05-26 08:00:00', '2024-05-30 12:00:00', '2024-06-03 16:00:00', '2024-06-07 21:00:00', '2024-06-12 01:00:00', '2024-06-16 05:00:00', '2024-06-20 09:00:00', '2024-06-24 15:00:00', '2024-06-28 19:00:00', '2024-07-03 00:00:00', '2024-07-07 04:00:00', '2024-07-11 09:00:00', '2024-07-15 13:00:00', '2024-07-19 17:00:00', '2024-07-23 21:00:00', '2024-07-28 02:00:00', '2024-08-01 06:00:00', '2024-08-05 12:00:00', '2024-08-09 16:00:00', '2024-08-13 20:00:00', '2024-08-18 00:00:00', '2024-08-22 04:00:00', '2024-08-26 08:00:00', '2024-08-30 12:00:00', '2024-09-03 18:00:00', '2024-09-07 22:00:00', '2024-09-12 02:00:00', '2024-09-16 07:00:00', '2024-09-20 13:00:00', '2024-09-24 18:00:00', '2024-09-28 22:00:00', '2024-10-03 02:00:00', '2024-10-07 06:00:00', '2024-10-11 10:00:00', '2024-10-15 14:00:00', '2024-10-19 18:00:00', '2024-10-23 22:00:00', '2024-10-28 02:00:00', '2024-11-01 06:00:00', '2024-11-05 10:00:00', '2024-11-09 14:00:00', '2024-11-13 18:00:00', '2024-11-17 22:00:00', '2024-11-22 02:00:00', '2024-11-26 06:00:00', '2024-11-30 10:00:00', '2024-12-04 14:00:00', '2024-12-08 18:00:00', '2024-12-12 22:00:00', '2024-12-17 02:00:00', '2024-12-21 06:00:00', '2024-12-25 10:00:00', '2024-12-29 14:00:00', '2025-01-02 18:00:00', '2025-01-06 22:00:00', '2025-01-11 02:00:00', '2025-01-15 06:00:00', '2025-01-19 10:00:00', '2025-01-23 14:00:00', '2025-01-27 21:00:00', '2025-02-01 01:00:00', '2025-02-05 05:00:00', '2025-02-09 09:00:00', '2025-02-13 13:00:00', '2025-02-17 17:00:00', '2025-02-21 21:00:00', '2025-02-26 01:00:00', '2025-03-02 05:00:00', '2025-03-06 09:00:00', '2025-03-10 13:00:00', '2025-03-14 17:00:00', '2025-03-18 21:00:00', '2025-03-23 01:00:00', '2025-03-27 05:00:00', '2025-03-31 10:00:00'];
        var Nikes_Massive_Rocket_equity = [10000.0, 10000.0, 10000.0, 10645.146819947457, 11632.18375779844, 14211.1728338731, 13539.22310653451, 15733.910327717598, 15302.25154680402, 15378.51647229314, 18118.21482849297, 17755.850531923115, 17329.505850549358, 18127.554835613315, 18569.822309501447, 20898.77539252777, 20150.226885288463, 20339.38204082336, 21618.626118322143, 21186.2535959557, 19541.52574659504, 22623.617920266573, 24425.067767063345, 29910.373091815512, 29312.1656299792, 29312.1656299792, 28977.47626397046, 31681.139465133117, 31440.78522039097, 29591.81552315022, 27425.562267636204, 29655.341740920278, 34765.20466370677, 33388.50255902398, 33916.30800747705, 38036.06918244392, 35977.663957817625, 39354.52856290324, 48268.52709283712, 45429.95155156156, 49304.08620549228, 53931.769197601614, 55727.909516186286, 55499.794466614985, 55922.48090127273, 54155.70825658772, 59418.66107561834, 64574.2152525441, 67853.8479211355, 71499.87026633152, 70603.52501271431, 69463.93968585737, 82036.52171274452, 79199.04311228349, 91845.5322491384, 93494.29148401498, 94678.55250947917, 88434.64304504702, 95127.92912823154, 99543.97899376212, 122495.82194097378, 129697.52101583316, 120253.1669799028, 123318.87449596245, 127429.50364582783, 121454.8140344327, 148647.56380753953, 164575.25826487978, 152891.3744255278, 179925.37869238603, 203972.15558664344, 228456.93688154756, 262465.5346136096, 265790.0980520487, 255264.8101691876, 226030.20793776272, 234783.44372457356, 223775.33208625505, 229241.41753134856, 232843.49731416025, 215590.68915872625, 223887.34447596545, 225003.17710962676, 225795.18829305255, 222289.4982208196, 216951.9929787419, 218033.26049866423, 226188.30136792967, 212059.2919391219, 207818.10610033947, 221903.55551380684, 222684.65602921543, 270512.92375192407, 291073.9362823217, 303402.5410192623, 286445.863300202, 297131.9439384284, 315951.6294260326, 318511.2810014113, 309772.94524122303, 279893.5851623781, 326575.36571611965, 312229.0284914825, 351159.1671507201, 293992.3029082148, 329626.0898993145, 338029.5041849654, 343516.1467015326, 325641.3296268627, 311465.74761188973, 325924.67976213945, 352056.59630840167, 339164.00210631086, 419029.3597618822, 434480.5349469862, 414021.8227644498, 410270.5996320211, 434843.4671646125, 455136.16229896096, 446033.4390529817, 484701.0017102683, 503353.92758013227, 561782.9300572748, 557520.870227907, 562271.7539710276, 610523.0289354075, 633358.2616939731, 540973.3054766966, 684545.5116969902, 818875.936164534, 991468.422303036, 991027.2902408374, 1302756.6426920511, 1282829.6770854336, 1413242.3473106865, 1449271.2336591706, 1612414.3259978695, 1632838.2407938424, 1567524.711162089, 1661069.7324032846, 1760197.230869052, 1737158.6123436966, 1778799.8165801528, 1743223.8202485498, 1854961.971230783, 1878458.1561997065, 1926347.150998716, 2439615.4120871862, 2383516.822566233, 2609949.650678098, 2577230.486673309, 2822066.0096570537, 2937924.223504056, 2820407.254563894, 3110648.8587378347, 3079617.046405833, 3643403.746443172, 3395242.933951875, 3302094.6704084347, 3199688.700426214, 3087010.21060866, 3573059.225386465, 3405531.9320670967, 3179617.3349244464, 3121413.7245324315, 3277600.9435381037, 3474009.3591902107, 3352358.127485545, 3166318.999254924, 5177570.309924245, 5843250.76029346, 8989244.049939072, 8812879.072292255, 8679665.567887131, 8789607.998413704, 9175496.96433259, 10039053.958629064, 10008596.297052065, 11656532.546278143, 10803222.2261788, 10777212.47169991, 11448637.157124398, 11876847.53062524, 14204158.94565361, 17017377.14449133, 18004970.71724052, 16600294.921764284, 16971475.865942296, 16817706.693982463, 16563494.009752346, 17204931.142618723, 19753247.89931691, 19358182.94133057, 22492899.53865281, 24491571.6618321, 24126997.310268432, 23943953.82400787, 24865397.75363613, 24676752.26934521, 23331594.25346042, 23276919.11739723, 24253342.25032204, 29342715.155358348, 33267438.153465174, 37799155.343085006, 37383130.4598141, 36357526.7678578, 37865933.94935803, 39323142.53043857, 46068886.6740899, 43890602.29170156, 43358434.1462369, 56096814.95469768, 52297710.36929519, 49632546.86862368, 51151537.98641445, 52455583.92883165, 51217378.13585621, 57905654.47098499, 63563640.11217683, 65941094.27549362, 74761018.6708031, 77049060.09689662, 76845606.22500303, 77818983.90385307, 89347506.00858685, 98903054.58629292, 103808646.09377304, 103287963.0382832, 105810943.6820983, 112125413.17901286, 103377836.944439, 110960617.21215816, 113789422.52944992, 109528185.75370046, 107872582.2516328, 108410208.6540336, 104117164.39133388, 108123943.29692575, 128719895.02725913, 124615998.5151777, 134578972.06920132, 173248850.2566903, 173248850.2566903, 173248850.2566903, 169783873.2515565, 179728831.57314295, 191124891.55866435, 229381097.60991564, 203110868.1035888, 236001093.9719208, 270938888.9199742, 294189473.24239945, 304458184.7393458, 323741898.24713624, 340104840.8415699, 271879193.32856363, 268575051.30834633, 278910723.20684195, 301273165.1919594, 296587168.89098144, 328025006.7986138, 355572548.0358724, 311032526.99708635, 296325913.8165777, 287262937.4479637, 310295041.41045827, 326054485.8068275, 405047079.9313749, 545527350.0961535, 572569218.4813386, 541357814.9107867, 559387162.2251596, 577656073.1345968, 624767135.0991294, 617449073.8534286, 692494689.753832, 733820628.6695976, 733820628.6695976, 887807109.6321614, 1083445038.3987195, 1102636856.9520051, 1016613539.9200376, 1029490644.759025, 1073793218.5788317, 1263659589.79733, 1225011208.1799371, 1162235837.5133314, 1378196405.5587776, 1400270136.4110773, 1412110617.5520484, 1412110617.5520484, 1429997352.041041, 1616735092.1804018, 1623102010.5380874, 1516060851.4083793, 1606909955.6729977, 1838566447.2678664, 1765759215.956059, 1909712363.1924083, 2005868163.077352, 1850148926.7802367, 2205589488.4015565, 2145079287.826559, 2172250292.1390285, 2225311125.941677, 2366257450.820725, 2601254550.737752, 2351328086.182494, 2351328086.182494, 2316057279.9250145, 2260445260.5921354, 2198430095.5641418, 2425682628.775976, 2548284324.206442, 2879585617.6710086, 3615510269.5439887, 3405227058.8127656, 3391288198.6246386, 3230937240.47143, 3281705089.661877, 4619169870.393147, 4642191361.375023, 4606972602.91339, 4743027141.410198, 5403240476.74695, 6762156208.279844, 6626724570.173279, 6760341838.369604, 7013197131.438282, 7671456021.993504, 8497791433.599469, 8815632098.28063, 11068609381.94084, 10896689490.24019, 10678755700.435389, 11919453088.659004, 13697205972.514616, 16014993716.74765, 17650431823.949707, 17166193403.555103, 16537553668.162195, 18987021485.15217, 19471072391.556538, 20936041401.51841, 20467923230.77783, 19908122185.48188, 20682279643.17797, 22910083597.590004, 25404292036.83496, 28462316641.666306, 32347030821.387177, 39594927944.68259, 40087986033.90564, 40086845662.19157, 38499406573.96878, 34785920133.28015, 35635624209.06908, 32184955445.656494, 34105653932.494926, 34239966545.10161, 35665617219.12552, 35843371168.04084, 37828829328.2936, 35875353055.87182, 39843767896.43042, 39540361685.061, 37974563362.33258, 39428735975.35418, 39625244940.201164, 42209088454.96841, 45023027685.29963, 46330342730.67957, 44495661158.54466, 43274925661.0374, 47397089924.30469, 52039047459.58601, 51913758650.43248, 51262951159.5879, 53922634594.9267, 51116777464.70559, 47725816399.43669, 48080387470.372246, 52027051414.90626];
        var Nikes_Massive_Rocket_drawdown = [0.0, 0.0, 0.0, -2.000000000000008, 0.0, 0.0, -4.72832000000002, -2.000000000000005, -4.6886234666666775, -4.213600885359147, -1.9999999999999944, -3.9599999999999977, -6.266064872843959, -5.919999999999998, -6.3051044126720095, -3.959999999999998, -7.399943120349863, -6.53068352037987, -2.784, -4.728320000000006, -12.124435818291204, -3.999999999999997, -5.919999999999995, -2.0000000000000018, -3.960000000000005, -3.960000000000005, -11.452656639999995, -3.960000000000008, -4.688623466666671, -10.293694897838934, -16.86059767254922, -10.101117924357585, -3.960000000000012, -7.763184000000008, -7.83999999999999, -2.000000000000004, -7.303484727766911, 0.0, 0.0, -5.880799999999991, -2.7839999999999945, -1.999999999999997, -3.959999999999994, -6.633753599999993, -5.9226762674175895, -8.894884212722094, -5.920000000000003, 0.0, -5.88080000000001, -1.9999999999999996, -3.9999999999999942, -5.549500415999972, 0.0, -7.763184000000001, -0.7586666666666676, -2.743493333333336, -1.9999999999999931, -9.607920319999996, -5.880799999999995, -2.0000000000000027, 0.0, 0.0, -11.415761913600008, -9.15741503321157, -6.129328867651953, -10.530571182680724, 0.0, -3.999999999999993, -15.029954560000016, -0.0056892630784516, -9.645567999999995, -1.999999999999996, -1.9999999999999916, -0.7586666666666405, -4.688623466666641, -15.604308159886845, -12.33600434860625, -16.446239020535156, -14.405299152343446, -13.06034611218439, -19.502240289725155, -16.404415570428537, -15.987783351421983, -15.692060348819007, -17.001023171631545, -18.993953460530857, -18.59022724516935, -15.545278862271944, -20.820801707820305, -22.404385673663896, -17.14512736932337, -16.85347821766338, -3.960000000000015, -7.801599999999981, -3.896483501091456, -11.489535999999998, -11.45265663999999, -5.844262164771776, -5.490493439999995, -8.083355451839816, -16.9492379050509, -3.0976969749927, -7.477061631999995, 0.0, -16.279473694607795, -6.131999180349866, -3.738949227009455, -4.728320000000009, -9.685769216720878, -13.617262762256264, -9.607184138041896, -2.359689035154226, -9.68319999999999, -1.9999999999999971, -3.959999999999998, -9.645568, -10.464219618827086, -8.539187200000008, -4.271015936000032, -6.185595617280033, 0.0, -5.880800000000009, -9.645568, -10.331056957439989, -9.56694794105544, -3.959999999999991, -2.0000000000000018, -16.29479373187318, 0.0, -1.9999999999999991, -1.9999999999999976, -4.767999999999993, 0.0, -1.529599999999992, -4.000000000000005, -1.5525973333333114, 0.0, 0.0, -4.0, -1.9999999999999936, 0.0, -5.588838399999994, -3.325720665988009, -5.259206252668248, -2.000000000000005, -0.7586666666666734, -2.000000000000001, 0.0, -2.299484961564437, -1.999999999999998, -5.920000000000003, 0.0, -0.4093371733332975, -4.39296368639996, -1.999999999999995, -2.9776473483960144, 0.0, -12.635533361152, -15.032371679091014, -17.667424051534127, -20.56680308157115, -8.060065342196665, -12.370782693800438, -18.183889051502263, -19.68155136235056, -15.662630375052252, -10.608760354371505, -13.739020892596567, -18.52607428700761, 0.0, -4.000000000000005, -5.919999999999997, -7.7658078348785, -9.159999206790248, -8.009359196742896, -3.9707065901430303, -2.000000000000009, -5.468416000000013, 0.0, -9.645568, -9.863104633348591, -4.247539683171084, -3.9600000000000057, 0.0, 0.0, 0.0, -7.801599999999994, -5.740052941650214, -9.36528486400009, -10.735298899706796, -7.278438052216904, 0.0, -2.000000000000009, -2.0000000000000053, -5.919999999999998, -7.3204472831999645, -8.023576156478061, -4.484013808402034, -5.208661756975626, -10.375846120715812, -10.585870894619603, -6.835115752648694, 0.0, 0.0, 0.0, -1.100619523094746, -3.8139174331866137, -3.9600000000000017, -0.2640575649303961, -3.999999999999996, -8.539187200000018, -9.648138287227855, 0.0, -18.428756377600003, -22.58573953559393, -20.216496338241257, -18.182513425555868, -20.113802303792617, -9.681777373939562, -4.728319999999999, -2.0, -5.919999999999995, -3.0407062023227205, -3.296734552498528, -2.0718265234968407, 0.0, -3.9999999999999982, 0.0, -4.000000000000005, -1.6550400000000345, 0.0, -7.801599999999989, -1.0388331546168277, -1.999999999999997, -6.8498493839439245, -8.257886178462481, -7.8006524535159185, -11.451746616356685, -8.044111805528903, -4.728319999999971, -11.452656640000008, -7.839999999999998, 0.0, 0.0, 0.0, -2.000000000000001, -0.7586666666666159, -2.7839999999999856, 0.0, -11.45265664, 0.0, -3.959999999999989, -3.959999999999998, -5.880799999999989, -3.960000000000016, -2.000000000000004, -21.65897762504706, -22.611054394022347, -19.632867304576, -13.189209198685118, -14.539462362855044, -7.801599999999989, -4.000000000000001, -19.99355085598229, -23.77651179214872, -26.10776815600721, -20.18325321171552, -16.129474017593157, 0.0, 0.0, -4.0, -9.23306989977603, -6.210173651929258, -7.801599999999992, -0.2823082666666677, -7.763184000000025, 0.0, -0.7586666666666649, -0.7586666666666649, -5.880799999999995, 0.0, 0.0, -7.801599999999989, -6.633753599999971, -5.91999999999999, 0.0, -7.839999999999995, -12.562714471510846, 0.0, -11.526400000000006, -10.778280072970231, -10.778280072970231, -9.648138287227864, 0.0, -2.000000000000002, -9.607920319999998, -4.191225162998053, -0.7999999999999788, -4.728319999999973, 0.0, -3.999999999999997, -11.452656639999995, 0.0, -2.743493333333331, -2.000000000000002, -2.000000000000008, -3.960000000000017, -4.688623466666658, -13.846064579884509, -13.846064579884509, -15.138406036774036, -17.176060565013792, -19.44832982181665, -11.1216738415459, -6.6294812748922265, -5.92, 0.0, -9.645567999999995, -10.015422277930211, -14.27018106327839, -12.923104907051725, 0.0, -3.9599999999999858, -4.688623466666671, -5.8807999999999945, 0.0, 0.0, -4.728319999999982, -3.5392000000000348, -2.7434933333333533, -3.9599999999999977, -3.959999999999996, -0.3678410638222896, 0.0, -6.205430959229113, -8.081322340044531, 0.0, -1.9999999999999971, 0.0, -3.960000000000001, -6.594850997333326, -10.015422277930169, -5.919999999999997, -3.5215453866666446, -5.91999999999999, -8.023576156478159, -10.539146379682968, -7.060325707558852, -2.0000000000000058, 0.0, 0.0, -2.0000000000000058, 0.0, -7.840000000000004, -7.842621649715211, -11.492053832386487, -20.029147965387043, -18.075726619688226, -26.008617859650577, -21.593041272757024, -21.28426421502799, -18.006774395336926, -17.59812817603869, -13.03367278299068, -17.524603634795582, -8.401443608832839, -9.098957233392484, -12.698638526950145, -9.3555783916088, -8.903815457322683, -2.963706165737272, 0.0, -5.880800000000002, -9.60792032, -12.087820999882158, -3.713723597179526, -6.56678499149147, -6.791734079056787, -7.960222727286811, -3.1849090657220414, -8.222669465443067, -14.310951433763142, -13.674338799140132, -6.588323222426341];

        var equityTraces = [
            {
                x: Nikes_Baby_Rocket_dates,
                y: Nikes_Baby_Rocket_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket (Lower Risk)',
                line: { color: '#3f5efb', width: 3 }
            },
            {
                x: Nikes_Massive_Rocket_dates,
                y: Nikes_Massive_Rocket_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket (Higher Risk)',
                line: { color: '#11998e', width: 4 }
            }
        ];
        
        var equityLayout = {
            title: {
                text: 'Nike Rocket Algorithms - Equity Curves Comparison (Log Scale)',
                font: { size: 18 }
            },
            xaxis: { 
                title: 'Date',
                gridcolor: '#e1e5e9'
            },
            yaxis: { 
                title: 'Equity ($)', 
                type: 'log',
                gridcolor: '#e1e5e9'
            },
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff'
        };
        
        Plotly.newPlot('equityChart', equityTraces, equityLayout);

        var drawdownTraces = [
            {
                x: Nikes_Baby_Rocket_dates,
                y: Nikes_Baby_Rocket_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket (Better Risk Control)',
                line: { color: '#3f5efb', width: 3 },
                fill: 'tonexty'
            },
            {
                x: Nikes_Massive_Rocket_dates,
                y: Nikes_Massive_Rocket_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket (Higher Risk)',
                line: { color: '#11998e', width: 4 },
                fill: 'tonexty'
            }
        ];
        
        var drawdownLayout = {
            title: {
                text: 'Nike Rocket Algorithms - Drawdown Comparison',
                font: { size: 18 }
            },
            xaxis: { 
                title: 'Date',
                gridcolor: '#e1e5e9'
            },
            yaxis: { 
                title: 'Drawdown (%)',
                gridcolor: '#e1e5e9'
            },
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff'
        };
        
        Plotly.newPlot('drawdownChart', drawdownTraces, drawdownLayout);
    </script>
    
    <div class="summary">
        <h3>🎯 KEY INSIGHTS FROM VISUAL ANALYSIS:</h3>
        <ul style="font-size: 16px; line-height: 1.6;">
            <li><strong>🚀 Massive Rocket Equity Curve:</strong> Exponentially higher growth due to 2x position sizing</li>
            <li><strong>🛡️ Baby Rocket Risk Control:</strong> Much shallower drawdowns, better risk management</li>
            <li><strong>📊 Same Trading Logic:</strong> Identical trade frequency and win rates, only position sizing differs</li>
            <li><strong>⚖️ Risk vs Reward:</strong> Choose based on your risk tolerance and return objectives</li>
            <li><strong>🎯 CALEB's Genius:</strong> Same algorithm, different risk levels = perfect customization</li>
        </ul>
    </div>
    
    <div class="summary">
        <h3>🏆 IMPLEMENTATION RECOMMENDATIONS:</h3>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 300px; margin: 10px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4>🚀 Choose MASSIVE ROCKET if:</h4>
                <ul>
                    <li>You can handle -30% drawdowns</li>
                    <li>You want maximum returns</li>
                    <li>You have larger account size</li>
                    <li>You prefer aggressive growth</li>
                </ul>
            </div>
            <div style="flex: 1; min-width: 300px; margin: 10px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4>🛡️ Choose BABY ROCKET if:</h4>
                <ul>
                    <li>You prefer -16% max drawdowns</li>
                    <li>You want steady growth</li>
                    <li>You have smaller account size</li>
                    <li>You prefer conservative approach</li>
                </ul>
            </div>
        </div>
    </div>
    
</body>
</html>
