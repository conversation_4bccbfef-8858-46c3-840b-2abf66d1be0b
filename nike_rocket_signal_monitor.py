#!/usr/bin/env python3
"""
Nike Rocket Signal Monitor (Parallel Bot)

This bot runs in parallel to the execution bot and:
1. Monitors CALEB's account for live trading activity
2. Generates the same signals as the execution bot
3. Sends signals to AXON AI when trades are executed
4. Provides real-time visibility into trading activity

DOES NOT INTERFERE WITH EXECUTION BOT - PURE MONITORING ONLY
"""

import asyncio
import logging
import ccxt.pro as ccxt
import pandas as pd
import numpy as np
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional
import importlib.util

logger = logging.getLogger(__name__)

class NikeRocketSignalMonitor:
    """Parallel signal monitoring bot - does NOT execute trades."""
    
    def __init__(self):
        """Initialize signal monitoring system."""
        self.running = False
        self.exchanges = {}
        self.algorithms = {}
        self.last_positions = {}
        self.sent_signals = {}
        
        # CALEB's Account Configuration (MONITORING ONLY)
        self.accounts = {
            'CALEB_MAIN': {
                'algorithm': 'massive_rocket',
                'symbol': 'BTC',
                'funding_usdt': 500,
                'api_key': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
                'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM'
            },
            'CALEB_SUB1': {
                'algorithm': 'massive_rocket',
                'symbol': 'ADA',
                'funding_usdt': 500,
                'api_key': 'a2cfaa90-469c-41d6-b954-7853887d1d7d',
                'secret': 'wm79OAQzbMIb7EjniYf1pU-buGaQ4PJf5L5awFjXXcw0MzE2MDUwZC03MGQ4LTQ4MzEtOWE0NC00N2I1OTRhYmQzNzI'
            }
        }
        
        # AXON AI Configuration
        self.axon_webhook_url = "https://axonai-production.up.railway.app/api/v1/signals/tradingview-webhook"
        
        self.initialize_monitoring_system()
    
    def initialize_monitoring_system(self):
        """Initialize monitoring system - READ ONLY."""
        logger.info("👁️  Initializing Nike Rocket Signal Monitor")
        logger.info("📡 MONITORING ONLY - Does not execute trades")
        logger.info("🎯 Sends signals to AXON when execution bot trades")
        
        # Load Nike algorithms for signal generation
        self.load_nike_algorithms()
        
        # Initialize read-only Phemex connections
        self.initialize_monitoring_connections()
    
    def load_nike_algorithms(self):
        """Load Nike algorithms for signal monitoring."""
        try:
            # Load Massive Rocket (same as execution bot)
            massive_spec = importlib.util.spec_from_file_location(
                "NikesMassiveRocket",
                "/Users/<USER>/TomorrowTech/python-backend/data_seed/Nike's Massive Rocket Algo.py"
            )
            massive_module = importlib.util.module_from_spec(massive_spec)
            massive_spec.loader.exec_module(massive_module)
            
            self.algorithms = {
                'massive_rocket': massive_module.NikesMassiveRocket()
            }
            
            logger.info("✅ Nike algorithms loaded for monitoring")
            
        except Exception as e:
            logger.error(f"❌ Error loading Nike algorithms: {e}")
            raise
    
    def initialize_monitoring_connections(self):
        """Initialize read-only Phemex connections."""
        for account_name, config in self.accounts.items():
            try:
                exchange = ccxt.phemex({
                    'apiKey': config['api_key'],
                    'secret': config['secret'],
                    'sandbox': False,
                    'enableRateLimit': True,
                    'options': {'defaultType': 'swap'}
                })
                
                self.exchanges[account_name] = exchange
                logger.info(f"👁️  {account_name}: Monitoring {config['algorithm']} {config['symbol']}")
                
            except Exception as e:
                logger.error(f"❌ Error initializing monitoring for {account_name}: {e}")
    
    async def start_signal_monitoring(self):
        """Start parallel signal monitoring."""
        logger.info("🚀 Starting Parallel Signal Monitoring")
        logger.info("👁️  Watching for execution bot trades...")
        logger.info("📡 Will send signals to AXON when trades detected")
        self.running = True
        
        tasks = []
        
        for account_name, config in self.accounts.items():
            # Position monitoring task (detect when execution bot trades)
            tasks.append(asyncio.create_task(
                self.monitor_execution_bot_trades(account_name, config)
            ))
            
            # Signal generation task (parallel to execution bot)
            tasks.append(asyncio.create_task(
                self.generate_parallel_signals(account_name, config)
            ))
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"❌ Error in signal monitoring: {e}")
        finally:
            self.running = False
    
    async def monitor_execution_bot_trades(self, account_name: str, config: Dict[str, Any]):
        """Monitor for trades executed by the execution bot."""
        logger.info(f"👁️  Monitoring execution bot trades: {account_name}")
        
        while self.running:
            try:
                # Get current positions
                positions = await self.exchanges[account_name].fetch_positions()
                active_positions = [pos for pos in positions if pos['contracts'] != 0]
                
                # Check for new positions (execution bot trades)
                for position in active_positions:
                    position_key = f"{account_name}_{position['symbol']}_{position['side']}"
                    
                    # If this is a new position, execution bot just traded
                    if position_key not in self.last_positions:
                        logger.info(f"🚀 EXECUTION BOT TRADE DETECTED: {account_name}")
                        logger.info(f"   Symbol: {position['symbol']}")
                        logger.info(f"   Side: {position['side']}")
                        logger.info(f"   Size: {position['contracts']}")
                        logger.info(f"   Entry: ${position['entryPrice']:.4f}")
                        
                        # Send signal to AXON for this trade
                        await self.send_trade_signal_to_axon(account_name, position, config)
                        
                        self.last_positions[position_key] = position
                
                # Clean up closed positions
                current_keys = [f"{account_name}_{pos['symbol']}_{pos['side']}" for pos in active_positions]
                closed_keys = [key for key in self.last_positions.keys() if key.startswith(account_name) and key not in current_keys]
                
                for key in closed_keys:
                    logger.info(f"📤 Position closed: {key}")
                    del self.last_positions[key]
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"❌ Error monitoring trades for {account_name}: {e}")
                await asyncio.sleep(30)
    
    async def generate_parallel_signals(self, account_name: str, config: Dict[str, Any]):
        """Generate signals in parallel to execution bot (for comparison)."""
        algorithm = self.algorithms[config['algorithm']]
        symbol = config['symbol']
        
        logger.info(f"📊 Generating parallel signals: {account_name} {symbol}")
        
        while self.running:
            try:
                # Get same data as execution bot
                data = await self.get_multi_timeframe_data(account_name, symbol)
                
                if data:
                    # Process through same algorithm as execution bot
                    signal_result = await self.process_parallel_algorithm(
                        algorithm, data, account_name, config['funding_usdt']
                    )
                    
                    if signal_result and signal_result['signal'] != 'NEUTRAL':
                        logger.info(f"📡 Parallel signal generated: {account_name} {signal_result['signal']}")
                        logger.info(f"   Mode: {signal_result['mode_used']}")
                        logger.info(f"   Entry: ${signal_result['entry_price']:.4f}")
                        logger.info(f"   Stop: ${signal_result['stop_loss']:.4f}")
                        logger.info(f"   Target: ${signal_result['take_profit']:.4f}")
                
                await asyncio.sleep(30)  # Same timing as execution bot
                
            except Exception as e:
                logger.error(f"❌ Error generating parallel signals: {e}")
                await asyncio.sleep(60)
    
    async def get_multi_timeframe_data(self, account_name: str, symbol: str) -> Dict[str, pd.DataFrame]:
        """Get same multi-timeframe data as execution bot."""
        exchange = self.exchanges[account_name]
        phemex_symbol = f"{symbol}/USDT:USDT"
        
        try:
            data = {}
            timeframes = {'daily': '1d', 'medium': '4h', 'lower': '1h'}
            
            for tf_name, tf_interval in timeframes.items():
                ohlcv = await exchange.fetch_ohlcv(phemex_symbol, tf_interval, limit=200)
                
                if ohlcv:
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df.set_index('timestamp', inplace=True)
                    data[tf_name] = df
            
            return data
            
        except Exception as e:
            logger.error(f"❌ Error fetching monitoring data: {e}")
            return {}
    
    async def process_parallel_algorithm(self, algorithm, data: Dict[str, pd.DataFrame], 
                                       account_name: str, funding_usdt: float) -> Optional[Dict[str, Any]]:
        """Process algorithm in parallel to execution bot."""
        try:
            # Same processing as execution bot
            processed_data = {}
            for tf_name, df in data.items():
                processed_data[tf_name] = algorithm.calculate_indicators(df)
            
            combined_df = algorithm.combine_timeframes(
                processed_data['daily'], 
                processed_data['medium'], 
                processed_data['lower']
            )
            
            result_df = algorithm.generate_signals_with_compounding_and_reversal(
                combined_df, initial_equity=funding_usdt
            )
            
            if not result_df.empty:
                latest = result_df.iloc[-1]
                
                return {
                    'signal': latest['signal'],
                    'mode_used': latest.get('mode_used', 'aggressive'),
                    'confidence': latest.get('confidence', 0.5),
                    'entry_price': latest['close'],
                    'stop_loss': latest.get('stop_loss', 0),
                    'take_profit': latest.get('take_profit', 0),
                    'position_size': latest.get('position_size', 0),
                    'leverage': latest.get('leverage', 1),
                    'account': account_name,
                    'timestamp': datetime.now().isoformat()
                }
            
        except Exception as e:
            logger.error(f"❌ Error processing parallel algorithm: {e}")
        
        return None
    
    async def send_trade_signal_to_axon(self, account_name: str, position: Dict[str, Any], config: Dict[str, Any]):
        """Send signal to AXON when execution bot trades."""
        try:
            # Get current price for signal
            symbol = config['symbol']
            phemex_symbol = f"{symbol}/USDT:USDT"
            ticker = await self.exchanges[account_name].fetch_ticker(phemex_symbol)
            current_price = ticker['last']
            
            # Create AXON signal payload
            signal_data = {
                'symbol': symbol,
                'action': position['side'].upper(),  # BUY or SELL
                'price': float(position['entryPrice']),
                'timestamp': int(datetime.now().timestamp() * 1000),  # Milliseconds
                'target': float(position['entryPrice']) * 1.035 if position['side'] == 'long' else float(position['entryPrice']) * 0.965,  # Estimated TP
                'stop_loss': float(position['entryPrice']) * 0.985 if position['side'] == 'long' else float(position['entryPrice']) * 1.015,  # Estimated SL
                'source': f'Nike_Rocket_{config["algorithm"]}',
                'account': account_name,
                'mode': 'live_trade',
                'confidence': 0.8,
                'position_size': float(position['contracts'])
            }
            
            logger.info(f"📡 Sending signal to AXON: {account_name} {signal_data['action']} {symbol}")
            
            # Send to AXON webhook
            response = requests.post(
                self.axon_webhook_url,
                json=signal_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Signal sent to AXON successfully: {account_name}")
            else:
                logger.error(f"❌ AXON signal failed: {response.status_code} - {response.text}")
            
        except Exception as e:
            logger.error(f"❌ Error sending signal to AXON: {e}")
    
    async def display_monitoring_dashboard(self):
        """Display real-time monitoring dashboard."""
        while self.running:
            try:
                print("\n" + "="*60)
                print("👁️  NIKE ROCKET SIGNAL MONITOR DASHBOARD")
                print("="*60)
                print(f"⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                for account_name, config in self.accounts.items():
                    print(f"\n📊 {account_name} ({config['algorithm']} {config['symbol']}):")
                    
                    # Get current positions
                    positions = await self.exchanges[account_name].fetch_positions()
                    active_positions = [pos for pos in positions if pos['contracts'] != 0]
                    
                    if active_positions:
                        for pos in active_positions:
                            pnl = pos['unrealizedPnl']
                            pct = pos['percentage']
                            print(f"   🔥 ACTIVE: {pos['side']} {pos['contracts']} @ ${pos['entryPrice']:.4f}")
                            print(f"   💰 P&L: ${pnl:.2f} ({pct:.2f}%)")
                    else:
                        print(f"   💤 No active positions")
                
                print(f"\n📡 Signals sent to AXON: {len(self.sent_signals)}")
                print("="*60)
                
                await asyncio.sleep(10)  # Update every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Dashboard error: {e}")
                await asyncio.sleep(30)

async def main():
    """Main function for signal monitoring."""
    logger.info("👁️  NIKE ROCKET SIGNAL MONITOR")
    logger.info("=" * 50)
    logger.info("📡 Parallel monitoring bot - does NOT execute trades")
    logger.info("🎯 Watches execution bot and sends signals to AXON")
    logger.info("👁️  Provides real-time visibility into trading")
    
    monitor = NikeRocketSignalMonitor()
    
    # Start monitoring tasks
    tasks = [
        asyncio.create_task(monitor.start_signal_monitoring()),
        asyncio.create_task(monitor.display_monitoring_dashboard())
    ]
    
    try:
        await asyncio.gather(*tasks)
    except KeyboardInterrupt:
        logger.info("⏹️ Signal monitor stopped by user")
    except Exception as e:
        logger.error(f"❌ Signal monitor error: {e}")

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Run signal monitor
    asyncio.run(main())
