#!/usr/bin/env python3
"""
Test Phemex Limits

Find the maximum number of candles we can fetch for accurate Nike Rocket signals.
"""

import ccxt
import asyncio

async def test_phemex_limits():
    """Test different limits to find the maximum allowed."""
    print("📊 TESTING PHEMEX CANDLE LIMITS")
    print("=" * 50)
    
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    # Test symbols and timeframes
    test_cases = [
        {'symbol': 'BTC/USDT:USDT', 'timeframe': '1h'},
        {'symbol': 'BTC/USDT:USDT', 'timeframe': '4h'},
        {'symbol': 'BTC/USDT:USDT', 'timeframe': '1d'},
        {'symbol': 'ADA/USDT:USDT', 'timeframe': '15m'},
        {'symbol': 'ADA/USDT:USDT', 'timeframe': '1h'},
        {'symbol': 'ADA/USDT:USDT', 'timeframe': '1d'}
    ]
    
    # Test different limits
    test_limits = [5, 10, 20, 50, 100, 150, 200, 250, 300, 500, 1000]
    
    for case in test_cases:
        symbol = case['symbol']
        timeframe = case['timeframe']
        
        print(f"\n📈 Testing {symbol} {timeframe}:")
        print("-" * 40)
        
        max_working_limit = 0
        
        for limit in test_limits:
            try:
                print(f"   limit={limit:4d}...", end=" ")
                
                ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
                
                if ohlcv and len(ohlcv) > 0:
                    actual_count = len(ohlcv)
                    print(f"✅ SUCCESS ({actual_count:3d} candles)")
                    max_working_limit = limit
                    
                    # Show price range for verification
                    if actual_count > 0:
                        first_price = ohlcv[0][4]  # First close
                        last_price = ohlcv[-1][4]  # Last close
                        print(f"      Price range: ${first_price:.4f} → ${last_price:.4f}")
                else:
                    print(f"❌ NO DATA")
                    break
                    
            except Exception as e:
                error_msg = str(e)
                if "30000" in error_msg:
                    print(f"❌ LIMIT EXCEEDED")
                    break
                else:
                    print(f"❌ ERROR: {error_msg[:30]}...")
                    break
        
        print(f"   🎯 MAX WORKING LIMIT: {max_working_limit}")

async def test_nike_algorithm_requirements():
    """Test what the Nike algorithm actually needs."""
    print(f"\n🧠 NIKE ALGORITHM REQUIREMENTS")
    print("=" * 50)
    
    print("📋 Nike Rocket Algorithm Indicators:")
    indicators = [
        "EMA 9, 21, 50, 100, 200",
        "MACD (12, 26, 9)",
        "RSI (14)",
        "ADX (14)",
        "ATR (14)",
        "Bollinger Bands (20, 2)",
        "Stochastic (14, 3, 3)"
    ]
    
    for indicator in indicators:
        print(f"   • {indicator}")
    
    print(f"\n📊 Minimum Data Requirements:")
    print(f"   • EMA 200: Needs at least 200 candles for accuracy")
    print(f"   • MACD: Needs at least 50 candles for stability")
    print(f"   • RSI: Needs at least 30 candles")
    print(f"   • ADX: Needs at least 30 candles")
    print(f"   • ATR: Needs at least 20 candles")
    
    print(f"\n🎯 RECOMMENDED MINIMUM: 200 candles")
    print(f"   • Ensures all indicators have sufficient data")
    print(f"   • Provides stable signal generation")
    print(f"   • Matches original backtest conditions")

async def test_alternative_approaches():
    """Test alternative approaches if limit is too low."""
    print(f"\n🔄 ALTERNATIVE APPROACHES")
    print("=" * 50)
    
    exchange = ccxt.phemex({
        'apiKey': '71b3cd21-f622-4328-816b-e7d7a6fa78c4',
        'secret': 'LoyHVr-4CLfN6uRsrKgg5jmSsqYF3Df2oGN0_JkKJxM4Njg1MWUzNi1jMWUyLTRhYTctODJiYS1jMjFiNTY0NmYyNmM',
        'sandbox': False,
        'enableRateLimit': True,
        'options': {'defaultType': 'swap'}
    })
    
    symbol = 'BTC/USDT:USDT'
    timeframe = '1h'
    
    print(f"🧪 Testing alternative approaches for {symbol} {timeframe}:")
    
    # Approach 1: Multiple smaller requests
    print(f"\n1. Multiple smaller requests:")
    try:
        all_data = []
        batch_size = 100
        num_batches = 3
        
        for i in range(num_batches):
            print(f"   Batch {i+1}: limit={batch_size}...", end=" ")
            
            # For subsequent batches, we'd need to use 'since' parameter
            # but for now just test if multiple calls work
            ohlcv = exchange.fetch_ohlcv(symbol, timeframe, limit=batch_size)
            
            if ohlcv:
                all_data.extend(ohlcv)
                print(f"✅ {len(ohlcv)} candles")
            else:
                print(f"❌ Failed")
                break
        
        print(f"   Total collected: {len(all_data)} candles")
        
    except Exception as e:
        print(f"   ❌ Multiple requests failed: {e}")
    
    # Approach 2: Different timeframe strategy
    print(f"\n2. Different timeframe strategy:")
    print(f"   • Use higher timeframes (4h, 1d) with more data")
    print(f"   • Convert/resample to needed timeframes")
    print(f"   • May provide sufficient indicator data")

async def main():
    """Main test function."""
    print("🚀 PHEMEX LIMIT TESTING FOR NIKE ROCKET")
    print("=" * 60)
    
    # Test limits
    await test_phemex_limits()
    
    # Check Nike requirements
    await test_nike_algorithm_requirements()
    
    # Test alternatives
    await test_alternative_approaches()
    
    print(f"\n🎯 CONCLUSION:")
    print(f"   • Find maximum working limit for each timeframe")
    print(f"   • Ensure sufficient data for Nike indicators")
    print(f"   • Implement fallback strategies if needed")

if __name__ == "__main__":
    asyncio.run(main())
