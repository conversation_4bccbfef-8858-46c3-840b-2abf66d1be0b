#!/usr/bin/env python3
"""
Check actual account balances on Phemex to fix position sizing
"""

import asyncio
from services.caleb_phemex_service import CalebPhemexService
from services.guthrix_phemex_service import GuthrixPhemexService

async def check_balances():
    """Check actual account balances"""
    
    print("💰 CHECKING PHEMEX ACCOUNT BALANCES")
    print("="*50)
    
    # Check CALEB account
    try:
        print("🔍 CALEB Account:")
        caleb_service = CalebPhemexService()
        
        # Get account balance
        balance = caleb_service.exchange.fetch_balance()
        print(f"  Total Balance: ${balance['total']['USDT']:.2f}")
        print(f"  Free Balance: ${balance['free']['USDT']:.2f}")
        print(f"  Used Balance: ${balance['used']['USDT']:.2f}")
        
        # Calculate safe position size for ADA
        free_balance = balance['free']['USDT']
        risk_percent = 0.02  # 2%
        leverage = 5
        
        # Safe position size = (Free Balance * Risk%) * Leverage
        safe_position_value = free_balance * risk_percent * leverage
        
        # Get current ADA price
        ticker = caleb_service.exchange.fetch_ticker('ADA/USDT:USDT')
        ada_price = ticker['last']
        safe_ada_quantity = safe_position_value / ada_price
        
        print(f"  ADA Price: ${ada_price:.4f}")
        print(f"  Safe Position Value: ${safe_position_value:.2f}")
        print(f"  Safe ADA Quantity: {safe_ada_quantity:.0f} ADA")
        print(f"  Current Attempted: 500 ADA (${500 * ada_price:.2f})")
        
        if safe_ada_quantity < 500:
            print(f"  ⚠️  ISSUE: Trying to trade 500 ADA but can only afford {safe_ada_quantity:.0f} ADA")
        else:
            print(f"  ✅ OK: Can afford 500 ADA trade")
            
    except Exception as e:
        print(f"  ❌ Error checking CALEB: {str(e)}")
    
    print("\n" + "="*50)
    
    # Check GUTHRIX account
    try:
        print("🔍 GUTHRIX Account:")
        guthrix_service = GuthrixPhemexService()
        
        # Get account balance
        balance = guthrix_service.exchange.fetch_balance()
        print(f"  Total Balance: ${balance['total']['USDT']:.2f}")
        print(f"  Free Balance: ${balance['free']['USDT']:.2f}")
        print(f"  Used Balance: ${balance['used']['USDT']:.2f}")
        
        # Calculate safe position size for ADA
        free_balance = balance['free']['USDT']
        risk_percent = 0.02  # 2%
        leverage = 5
        
        # Safe position size = (Free Balance * Risk%) * Leverage
        safe_position_value = free_balance * risk_percent * leverage
        
        # Get current ADA price
        ticker = guthrix_service.exchange.fetch_ticker('ADA/USDT:USDT')
        ada_price = ticker['last']
        safe_ada_quantity = safe_position_value / ada_price
        
        print(f"  ADA Price: ${ada_price:.4f}")
        print(f"  Safe Position Value: ${safe_position_value:.2f}")
        print(f"  Safe ADA Quantity: {safe_ada_quantity:.0f} ADA")
        print(f"  Current Attempted: 500 ADA (${500 * ada_price:.2f})")
        
        if safe_ada_quantity < 500:
            print(f"  ⚠️  ISSUE: Trying to trade 500 ADA but can only afford {safe_ada_quantity:.0f} ADA")
        else:
            print(f"  ✅ OK: Can afford 500 ADA trade")
            
    except Exception as e:
        print(f"  ❌ Error checking GUTHRIX: {str(e)}")
    
    print("\n" + "="*50)
    print("💡 SOLUTION:")
    print("Update the hardcoded position sizes in the signal sender")
    print("to use dynamic position sizing based on account balance.")

if __name__ == "__main__":
    asyncio.run(check_balances())
