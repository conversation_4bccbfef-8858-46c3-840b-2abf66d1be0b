#!/usr/bin/env python3
"""
Create Nike's Rocket Algorithms Interactive Equity Charts

Creates comprehensive interactive HTML charts comparing:
- Nike's Baby Rocket (Lower Risk: 1% conservative, 2% aggressive)
- Nike's Massive Rocket (Higher Risk: 2% conservative, 4% aggressive)

Features:
- Interactive equity curves with log scale
- Drawdown analysis charts
- Performance metrics summary
- Hover functionality with precise data
- Direct visual comparison
"""

import pandas as pd
import numpy as np
from datetime import datetime

def create_nike_rocket_html_chart(symbol: str):
    """Create interactive HTML chart comparing Nike's rocket algorithms."""
    
    timestamp = "20250715_113138"  # Use the generated timestamp
    
    # Load equity curves for both algorithms
    algorithms = ['Nikes_Baby_Rocket', 'Nikes_Massive_Rocket']
    data = {}
    
    print(f"🚀 Creating Nike Rocket comparison chart for {symbol}...")
    
    for algo in algorithms:
        filename = f"NIKE_ROCKET_equity_curve_{symbol}_{algo}_{timestamp}.csv"
        try:
            df = pd.read_csv(filename)
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            # Sample data for chart (every 100th point to reduce size)
            sample_df = df.iloc[::100].copy()
            data[algo] = sample_df
            print(f"   ✅ Loaded {algo}: {len(df)} points, sampled to {len(sample_df)}")
            
        except Exception as e:
            print(f"   ❌ Error loading {algo}: {e}")
    
    if not data:
        print(f"   ❌ No data loaded for {symbol}")
        return
    
    # Load performance summary
    try:
        summary_df = pd.read_csv(f"NIKE_ROCKET_ALGORITHMS_SUMMARY_{timestamp}.csv")
        symbol_data = summary_df[summary_df['symbol'] == symbol]
        print(f"   ✅ Loaded performance summary")
    except Exception as e:
        print(f"   ❌ Error loading summary: {e}")
        symbol_data = pd.DataFrame()
    
    # Create HTML chart
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>🚀 NIKE'S ROCKET ALGORITHMS - {symbol} COMPARISON</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .chart-container {{ width: 100%; height: 600px; margin: 20px 0; background: white; border-radius: 10px; padding: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .summary {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); }}
        .winner {{ background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }}
        .risk-control {{ background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); }}
        .metric {{ display: inline-block; margin: 15px 30px; text-align: center; }}
        .metric-value {{ font-size: 24px; font-weight: bold; display: block; }}
        .metric-label {{ font-size: 14px; opacity: 0.9; }}
        .algorithm-header {{ font-size: 18px; font-weight: bold; margin-bottom: 10px; }}
        .comparison {{ display: flex; justify-content: space-around; flex-wrap: wrap; }}
        .algo-card {{ flex: 1; min-width: 300px; margin: 10px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 NIKE'S ROCKET ALGORITHMS COMPARISON</h1>
        <h2>{symbol} Performance Analysis</h2>
        <p><strong>CALEB's Latest Algorithm Refinements</strong></p>
    </div>
    
    <div class="summary winner">
        <h3>🏆 PERFORMANCE CHAMPION: MASSIVE ROCKET</h3>
        <div class="comparison">
"""
    
    # Add performance metrics for both algorithms
    if not symbol_data.empty:
        # Get aggregated data for each algorithm
        baby_data = symbol_data[symbol_data['algorithm'] == 'Nikes_Baby_Rocket']
        massive_data = symbol_data[symbol_data['algorithm'] == 'Nikes_Massive_Rocket']
        
        if not baby_data.empty and not massive_data.empty:
            baby_return = baby_data['total_return_percent'].iloc[0]
            baby_dd = baby_data['max_drawdown_percent'].iloc[0]
            baby_trades = baby_data['total_trades_all_modes'].iloc[0]
            baby_win_rate = baby_data['overall_win_rate_percent'].iloc[0]
            
            massive_return = massive_data['total_return_percent'].iloc[0]
            massive_dd = massive_data['max_drawdown_percent'].iloc[0]
            massive_trades = massive_data['total_trades_all_modes'].iloc[0]
            massive_win_rate = massive_data['overall_win_rate_percent'].iloc[0]
            
            # Calculate multiplier
            multiplier = massive_return / baby_return if baby_return > 0 else float('inf')
            
            html_content += f"""
            <div class="algo-card">
                <div class="algorithm-header">🛡️ BABY ROCKET (Lower Risk)</div>
                <div class="metric">
                    <span class="metric-value">{baby_return:,.0f}%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{baby_win_rate:.1f}%</span>
                    <span class="metric-label">Win Rate</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{baby_dd:.2f}%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{baby_trades:,}</span>
                    <span class="metric-label">Total Trades</span>
                </div>
            </div>
            
            <div class="algo-card">
                <div class="algorithm-header">🚀 MASSIVE ROCKET (Higher Risk)</div>
                <div class="metric">
                    <span class="metric-value">{massive_return:,.0f}%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{massive_win_rate:.1f}%</span>
                    <span class="metric-label">Win Rate</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{massive_dd:.2f}%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
                <div class="metric">
                    <span class="metric-value">{massive_trades:,}</span>
                    <span class="metric-label">Total Trades</span>
                </div>
            </div>
"""
            
            html_content += f"""
        </div>
        <div style="text-align: center; margin-top: 20px; font-size: 18px;">
            <strong>🏆 MASSIVE ROCKET DELIVERS {multiplier:,.0f}x BETTER RETURNS!</strong>
        </div>
    </div>
    
    <div class="summary risk-control">
        <h3>🛡️ RISK CONTROL CHAMPION: BABY ROCKET</h3>
        <div style="text-align: center;">
            <p><strong>Baby Rocket Drawdown:</strong> {baby_dd:.2f}% vs <strong>Massive Rocket:</strong> {massive_dd:.2f}%</p>
            <p><strong>🛡️ BABY ROCKET HAS {abs(baby_dd - massive_dd):.2f}% BETTER RISK CONTROL</strong></p>
        </div>
    </div>
"""
    
    html_content += """
    <div class="chart-container" id="equityChart"></div>
    <div class="chart-container" id="drawdownChart"></div>
    
    <script>
"""
    
    # Add equity data for both algorithms
    for algo, df in data.items():
        dates = df['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()
        equity = df['equity'].tolist()
        drawdown = df['drawdown'].tolist()
        
        html_content += f"""
        var {algo}_dates = {dates};
        var {algo}_equity = {equity};
        var {algo}_drawdown = {drawdown};
"""
    
    # Create equity chart
    html_content += """
        var equityTraces = ["""
    
    # Add traces for available algorithms
    traces = []
    if 'Nikes_Baby_Rocket' in data:
        traces.append("""
            {
                x: Nikes_Baby_Rocket_dates,
                y: Nikes_Baby_Rocket_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket (Lower Risk)',
                line: { color: '#3f5efb', width: 3 }
            }""")
    
    if 'Nikes_Massive_Rocket' in data:
        traces.append("""
            {
                x: Nikes_Massive_Rocket_dates,
                y: Nikes_Massive_Rocket_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket (Higher Risk)',
                line: { color: '#11998e', width: 4 }
            }""")
    
    html_content += ','.join(traces)
    html_content += """
        ];
        
        var equityLayout = {
            title: {
                text: 'Nike Rocket Algorithms - Equity Curves Comparison (Log Scale)',
                font: { size: 18 }
            },
            xaxis: { 
                title: 'Date',
                gridcolor: '#e1e5e9'
            },
            yaxis: { 
                title: 'Equity ($)', 
                type: 'log',
                gridcolor: '#e1e5e9'
            },
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff'
        };
        
        Plotly.newPlot('equityChart', equityTraces, equityLayout);
"""
    
    # Create drawdown chart
    html_content += """
        var drawdownTraces = ["""
    
    # Add drawdown traces
    dd_traces = []
    if 'Nikes_Baby_Rocket' in data:
        dd_traces.append("""
            {
                x: Nikes_Baby_Rocket_dates,
                y: Nikes_Baby_Rocket_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket (Better Risk Control)',
                line: { color: '#3f5efb', width: 3 },
                fill: 'tonexty'
            }""")
    
    if 'Nikes_Massive_Rocket' in data:
        dd_traces.append("""
            {
                x: Nikes_Massive_Rocket_dates,
                y: Nikes_Massive_Rocket_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket (Higher Risk)',
                line: { color: '#11998e', width: 4 },
                fill: 'tonexty'
            }""")
    
    html_content += ','.join(dd_traces)
    html_content += """
        ];
        
        var drawdownLayout = {
            title: {
                text: 'Nike Rocket Algorithms - Drawdown Comparison',
                font: { size: 18 }
            },
            xaxis: { 
                title: 'Date',
                gridcolor: '#e1e5e9'
            },
            yaxis: { 
                title: 'Drawdown (%)',
                gridcolor: '#e1e5e9'
            },
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff'
        };
        
        Plotly.newPlot('drawdownChart', drawdownTraces, drawdownLayout);
    </script>
    
    <div class="summary">
        <h3>🎯 KEY INSIGHTS FROM VISUAL ANALYSIS:</h3>
        <ul style="font-size: 16px; line-height: 1.6;">
            <li><strong>🚀 Massive Rocket Equity Curve:</strong> Exponentially higher growth due to 2x position sizing</li>
            <li><strong>🛡️ Baby Rocket Risk Control:</strong> Much shallower drawdowns, better risk management</li>
            <li><strong>📊 Same Trading Logic:</strong> Identical trade frequency and win rates, only position sizing differs</li>
            <li><strong>⚖️ Risk vs Reward:</strong> Choose based on your risk tolerance and return objectives</li>
            <li><strong>🎯 CALEB's Genius:</strong> Same algorithm, different risk levels = perfect customization</li>
        </ul>
    </div>
    
    <div class="summary">
        <h3>🏆 IMPLEMENTATION RECOMMENDATIONS:</h3>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 300px; margin: 10px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4>🚀 Choose MASSIVE ROCKET if:</h4>
                <ul>
                    <li>You can handle -30% drawdowns</li>
                    <li>You want maximum returns</li>
                    <li>You have larger account size</li>
                    <li>You prefer aggressive growth</li>
                </ul>
            </div>
            <div style="flex: 1; min-width: 300px; margin: 10px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4>🛡️ Choose BABY ROCKET if:</h4>
                <ul>
                    <li>You prefer -16% max drawdowns</li>
                    <li>You want steady growth</li>
                    <li>You have smaller account size</li>
                    <li>You prefer conservative approach</li>
                </ul>
            </div>
        </div>
    </div>
    
</body>
</html>
"""
    
    # Save HTML file
    html_filename = f"NIKE_ROCKET_COMPARISON_{symbol}_{timestamp}.html"
    with open(html_filename, 'w') as f:
        f.write(html_content)
    
    print(f"   ✅ Created interactive chart: {html_filename}")
    return html_filename

def main():
    """Create interactive charts for both BTC and ADA."""
    
    print("🚀 CREATING NIKE'S ROCKET ALGORITHMS INTERACTIVE CHARTS")
    print("=" * 60)
    print("📊 Generating comprehensive visual comparison charts...")
    
    html_files = []
    
    for symbol in ['BTC', 'ADA']:
        html_file = create_nike_rocket_html_chart(symbol)
        if html_file:
            html_files.append(html_file)
    
    print(f"\n✅ INTERACTIVE CHARTS CREATED SUCCESSFULLY!")
    print("=" * 60)
    for html_file in html_files:
        print(f"📊 {html_file}")
    
    print(f"\n🌐 CHART FEATURES:")
    print("✅ Interactive equity curves with log scale")
    print("✅ Drawdown analysis with risk visualization")
    print("✅ Performance metrics comparison")
    print("✅ Hover functionality with precise data")
    print("✅ Zoom, pan, and interactive exploration")
    print("✅ Clear visual distinction between algorithms")
    
    print(f"\n🎯 VISUAL CONFIRMATION:")
    print("🚀 Massive Rocket: Higher equity curves, deeper drawdowns")
    print("🛡️ Baby Rocket: Lower equity curves, shallower drawdowns")
    print("📊 Same trading logic, different position sizing impact")
    
    return html_files

if __name__ == "__main__":
    main()
