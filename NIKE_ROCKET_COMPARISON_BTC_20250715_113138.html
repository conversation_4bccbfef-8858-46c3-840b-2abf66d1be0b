
<!DOCTYPE html>
<html>
<head>
    <title>🚀 NIKE'S ROCKET ALGORITHMS - BTC COMPARISON</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .header { text-align: center; margin-bottom: 30px; }
        .chart-container { width: 100%; height: 600px; margin: 20px 0; background: white; border-radius: 10px; padding: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.2); }
        .winner { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
        .risk-control { background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%); }
        .metric { display: inline-block; margin: 15px 30px; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; display: block; }
        .metric-label { font-size: 14px; opacity: 0.9; }
        .algorithm-header { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
        .comparison { display: flex; justify-content: space-around; flex-wrap: wrap; }
        .algo-card { flex: 1; min-width: 300px; margin: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 NIKE'S ROCKET ALGORITHMS COMPARISON</h1>
        <h2>BTC Performance Analysis</h2>
        <p><strong>CALEB's Latest Algorithm Refinements</strong></p>
    </div>
    
    <div class="summary winner">
        <h3>🏆 PERFORMANCE CHAMPION: MASSIVE ROCKET</h3>
        <div class="comparison">

            <div class="algo-card">
                <div class="algorithm-header">🛡️ BABY ROCKET (Lower Risk)</div>
                <div class="metric">
                    <span class="metric-value">5,563,647,747%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">41.4%</span>
                    <span class="metric-label">Win Rate</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-19.16%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
                <div class="metric">
                    <span class="metric-value">4,510</span>
                    <span class="metric-label">Total Trades</span>
                </div>
            </div>
            
            <div class="algo-card">
                <div class="algorithm-header">🚀 MASSIVE ROCKET (Higher Risk)</div>
                <div class="metric">
                    <span class="metric-value">40,621,427,817,859,904%</span>
                    <span class="metric-label">Total Return</span>
                </div>
                <div class="metric">
                    <span class="metric-value">41.4%</span>
                    <span class="metric-label">Win Rate</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-35.01%</span>
                    <span class="metric-label">Max Drawdown</span>
                </div>
                <div class="metric">
                    <span class="metric-value">4,510</span>
                    <span class="metric-label">Total Trades</span>
                </div>
            </div>

        </div>
        <div style="text-align: center; margin-top: 20px; font-size: 18px;">
            <strong>🏆 MASSIVE ROCKET DELIVERS 7,301,222x BETTER RETURNS!</strong>
        </div>
    </div>
    
    <div class="summary risk-control">
        <h3>🛡️ RISK CONTROL CHAMPION: BABY ROCKET</h3>
        <div style="text-align: center;">
            <p><strong>Baby Rocket Drawdown:</strong> -19.16% vs <strong>Massive Rocket:</strong> -35.01%</p>
            <p><strong>🛡️ BABY ROCKET HAS 15.85% BETTER RISK CONTROL</strong></p>
        </div>
    </div>

    <div class="chart-container" id="equityChart"></div>
    <div class="chart-container" id="drawdownChart"></div>
    
    <script>

        var Nikes_Baby_Rocket_dates = ['2013-10-06 21:00:00', '2013-10-31 00:00:00', '2013-11-07 00:00:00', '2013-11-15 18:00:00', '2013-11-21 07:00:00', '2013-11-26 08:00:00', '2013-11-30 16:00:00', '2013-12-05 03:00:00', '2013-12-09 11:00:00', '2013-12-14 10:00:00', '2013-12-19 00:00:00', '2013-12-23 10:00:00', '2013-12-29 03:00:00', '2014-01-03 22:00:00', '2014-01-08 14:00:00', '2014-01-14 20:00:00', '2014-01-23 02:00:00', '2014-01-30 03:00:00', '2014-02-07 09:00:00', '2014-02-12 09:00:00', '2014-02-17 22:00:00', '2014-02-23 02:00:00', '2014-02-27 19:00:00', '2014-03-07 09:00:00', '2014-03-15 23:00:00', '2014-03-23 21:00:00', '2014-03-30 10:00:00', '2014-04-04 22:00:00', '2014-04-14 02:00:00', '2014-04-19 13:00:00', '2014-04-29 00:00:00', '2014-05-08 08:00:00', '2014-05-22 08:00:00', '2014-05-28 23:00:00', '2014-06-05 14:00:00', '2014-06-14 23:00:00', '2014-06-25 13:00:00', '2014-07-07 03:00:00', '2014-07-28 02:00:00', '2014-08-14 07:00:00', '2014-09-08 17:00:00', '2014-10-06 05:00:00', '2014-10-27 15:00:00', '2014-11-15 21:00:00', '2014-12-02 10:00:00', '2014-12-18 02:00:00', '2015-01-05 21:00:00', '2015-01-15 20:00:00', '2015-01-28 06:00:00', '2015-02-13 11:00:00', '2015-02-27 22:00:00', '2015-03-11 09:00:00', '2015-03-23 18:00:00', '2015-04-03 16:00:00', '2015-04-12 06:00:00', '2015-04-21 02:00:00', '2015-04-27 15:00:00', '2015-05-04 23:00:00', '2015-05-13 00:00:00', '2015-05-24 11:00:00', '2015-06-06 17:00:00', '2015-06-20 06:00:00', '2015-06-30 22:00:00', '2015-07-09 21:00:00', '2015-07-14 20:00:00', '2015-07-24 15:00:00', '2015-08-05 14:00:00', '2015-08-12 13:00:00', '2015-08-17 17:00:00', '2015-08-23 02:00:00', '2015-08-28 06:00:00', '2015-09-03 13:00:00', '2015-09-09 20:00:00', '2015-09-15 21:00:00', '2015-09-24 07:00:00', '2015-10-02 21:00:00', '2015-10-13 00:00:00', '2015-10-20 02:00:00', '2015-10-26 18:00:00', '2015-10-31 15:00:00', '2015-11-05 11:00:00', '2015-11-10 08:00:00', '2015-11-16 12:00:00', '2015-11-23 18:00:00', '2015-11-29 22:00:00', '2015-12-05 04:00:00', '2015-12-10 02:00:00', '2015-12-14 21:00:00', '2015-12-21 02:00:00', '2015-12-27 09:00:00', '2016-01-03 18:00:00', '2016-01-09 21:00:00', '2016-01-15 02:00:00', '2016-01-19 14:00:00', '2016-01-23 23:00:00', '2016-01-28 12:00:00', '2016-02-01 22:00:00', '2016-02-06 14:00:00', '2016-02-10 22:00:00', '2016-02-15 02:00:00', '2016-02-19 06:00:00', '2016-02-23 10:00:00', '2016-02-27 14:00:00', '2016-03-02 19:00:00', '2016-03-06 23:00:00', '2016-03-11 03:00:00', '2016-03-15 07:00:00', '2016-03-19 11:00:00', '2016-03-23 15:00:00', '2016-03-27 19:00:00', '2016-03-31 23:00:00', '2016-04-05 04:00:00', '2016-04-09 08:00:00', '2016-04-13 12:00:00', '2016-04-17 17:00:00', '2016-04-21 22:00:00', '2016-04-26 04:00:00', '2016-04-30 08:00:00', '2016-05-04 12:00:00', '2016-05-08 17:00:00', '2016-05-12 23:00:00', '2016-05-17 04:00:00', '2016-05-21 08:00:00', '2016-05-25 13:00:00', '2016-05-29 17:00:00', '2016-06-02 21:00:00', '2016-06-07 01:00:00', '2016-06-11 05:00:00', '2016-06-15 09:00:00', '2016-06-19 13:00:00', '2016-06-23 17:00:00', '2016-06-27 21:00:00', '2016-07-02 01:00:00', '2016-07-06 06:00:00', '2016-07-10 10:00:00', '2016-07-14 14:00:00', '2016-07-18 18:00:00', '2016-07-22 22:00:00', '2016-07-27 03:00:00', '2016-07-31 08:00:00', '2016-08-04 12:00:00', '2016-08-08 16:00:00', '2016-08-12 20:00:00', '2016-08-17 00:00:00', '2016-08-21 05:00:00', '2016-08-25 10:00:00', '2016-08-29 14:00:00', '2016-09-02 18:00:00', '2016-09-06 23:00:00', '2016-09-11 03:00:00', '2016-09-15 07:00:00', '2016-09-19 11:00:00', '2016-09-23 15:00:00', '2016-09-27 19:00:00', '2016-10-02 01:00:00', '2016-10-06 07:00:00', '2016-10-10 11:00:00', '2016-10-14 15:00:00', '2016-10-18 20:00:00', '2016-10-23 01:00:00', '2016-10-27 05:00:00', '2016-10-31 09:00:00', '2016-11-04 13:00:00', '2016-11-08 17:00:00', '2016-11-12 21:00:00', '2016-11-17 01:00:00', '2016-11-21 05:00:00', '2016-11-25 09:00:00', '2016-11-29 13:00:00', '2016-12-03 17:00:00', '2016-12-07 21:00:00', '2016-12-12 01:00:00', '2016-12-16 05:00:00', '2016-12-20 10:00:00', '2016-12-24 14:00:00', '2016-12-28 18:00:00', '2017-01-01 22:00:00', '2017-01-06 02:00:00', '2017-01-10 06:00:00', '2017-01-14 10:00:00', '2017-01-18 14:00:00', '2017-01-22 18:00:00', '2017-01-26 22:00:00', '2017-01-31 02:00:00', '2017-02-04 06:00:00', '2017-02-08 10:00:00', '2017-02-12 14:00:00', '2017-02-16 18:00:00', '2017-02-20 22:00:00', '2017-02-25 02:00:00', '2017-03-01 06:00:00', '2017-03-05 10:00:00', '2017-03-09 14:00:00', '2017-03-13 18:00:00', '2017-03-17 22:00:00', '2017-03-22 02:00:00', '2017-03-26 06:00:00', '2017-03-30 10:00:00', '2017-04-03 14:00:00', '2017-04-07 18:00:00', '2017-04-11 22:00:00', '2017-04-16 02:00:00', '2017-04-20 06:00:00', '2017-04-24 10:00:00', '2017-04-28 14:00:00', '2017-05-02 18:00:00', '2017-05-06 22:00:00', '2017-05-11 02:00:00', '2017-05-15 06:00:00', '2017-05-19 10:00:00', '2017-05-23 14:00:00', '2017-05-27 18:00:00', '2017-05-31 22:00:00', '2017-06-05 02:00:00', '2017-06-09 06:00:00', '2017-06-13 10:00:00', '2017-06-17 14:00:00', '2017-06-21 18:00:00', '2017-06-25 22:00:00', '2017-06-30 02:00:00', '2017-07-04 06:00:00', '2017-07-08 10:00:00', '2017-07-12 14:00:00', '2017-07-16 18:00:00', '2017-07-20 22:00:00', '2017-07-25 02:00:00', '2017-07-29 06:00:00', '2017-08-02 10:00:00', '2017-08-06 14:00:00', '2017-08-10 18:00:00', '2017-08-14 22:00:00', '2017-08-19 02:00:00', '2017-08-23 06:00:00', '2017-08-27 10:00:00', '2017-08-31 14:00:00', '2017-09-04 18:00:00', '2017-09-08 22:00:00', '2017-09-13 02:00:00', '2017-09-17 06:00:00', '2017-09-21 10:00:00', '2017-09-25 14:00:00', '2017-09-29 18:00:00', '2017-10-03 22:00:00', '2017-10-08 02:00:00', '2017-10-12 06:00:00', '2017-10-16 10:00:00', '2017-10-20 14:00:00', '2017-10-24 18:00:00', '2017-10-28 22:00:00', '2017-11-02 02:00:00', '2017-11-06 06:00:00', '2017-11-10 10:00:00', '2017-11-14 14:00:00', '2017-11-18 18:00:00', '2017-11-22 22:00:00', '2017-11-27 02:00:00', '2017-12-01 06:00:00', '2017-12-05 10:00:00', '2017-12-09 14:00:00', '2017-12-13 20:00:00', '2017-12-18 00:00:00', '2017-12-22 04:00:00', '2017-12-26 08:00:00', '2017-12-30 12:00:00', '2018-01-03 16:00:00', '2018-01-07 20:00:00', '2018-01-14 02:00:00', '2018-01-18 06:00:00', '2018-01-22 10:00:00', '2018-01-26 14:00:00', '2018-01-30 18:00:00', '2018-02-03 22:00:00', '2018-02-08 02:00:00', '2018-02-12 06:00:00', '2018-02-16 10:00:00', '2018-02-20 14:00:00', '2018-02-24 18:00:00', '2018-02-28 22:00:00', '2018-03-05 02:00:00', '2018-03-09 06:00:00', '2018-03-13 10:00:00', '2018-03-17 14:00:00', '2018-03-21 18:00:00', '2018-03-25 22:00:00', '2018-03-30 02:00:00', '2018-04-03 06:00:00', '2018-04-07 10:00:00', '2018-04-11 14:00:00', '2018-04-15 18:00:00', '2018-04-19 22:00:00', '2018-04-24 03:00:00', '2018-04-28 07:00:00', '2018-05-02 11:00:00', '2018-05-06 15:00:00', '2018-05-10 19:00:00', '2018-05-14 23:00:00', '2018-05-19 03:00:00', '2018-05-23 07:00:00', '2018-05-27 11:00:00', '2018-05-31 15:00:00', '2018-06-04 19:00:00', '2018-06-08 23:00:00', '2018-06-13 03:00:00', '2018-06-17 07:00:00', '2018-06-21 11:00:00', '2018-06-25 15:00:00', '2018-06-29 19:00:00', '2018-07-03 23:00:00', '2018-07-08 03:00:00', '2018-07-12 07:00:00', '2018-07-16 11:00:00', '2018-07-20 15:00:00', '2018-07-24 19:00:00', '2018-07-28 23:00:00', '2018-08-02 03:00:00', '2018-08-06 07:00:00', '2018-08-10 11:00:00', '2018-08-14 15:00:00', '2018-08-18 19:00:00', '2018-08-22 23:00:00', '2018-08-27 03:00:00', '2018-08-31 07:00:00', '2018-09-04 11:00:00', '2018-09-08 15:00:00', '2018-09-12 19:00:00', '2018-09-16 23:00:00', '2018-09-21 03:00:00', '2018-09-25 07:00:00', '2018-09-29 11:00:00', '2018-10-03 15:00:00', '2018-10-07 19:00:00', '2018-10-11 23:00:00', '2018-10-16 03:00:00', '2018-10-20 07:00:00', '2018-10-24 11:00:00', '2018-10-28 15:00:00', '2018-11-01 19:00:00', '2018-11-05 23:00:00', '2018-11-10 03:00:00', '2018-11-14 07:00:00', '2018-11-18 11:00:00', '2018-11-22 15:00:00', '2018-11-26 19:00:00', '2018-11-30 23:00:00', '2018-12-05 03:00:00', '2018-12-09 07:00:00', '2018-12-13 11:00:00', '2018-12-17 15:00:00', '2018-12-21 19:00:00', '2018-12-25 23:00:00', '2018-12-30 03:00:00', '2019-01-03 07:00:00', '2019-01-07 11:00:00', '2019-01-11 15:00:00', '2019-01-15 19:00:00', '2019-01-19 23:00:00', '2019-01-24 03:00:00', '2019-01-28 07:00:00', '2019-02-01 11:00:00', '2019-02-05 15:00:00', '2019-02-09 19:00:00', '2019-02-13 23:00:00', '2019-02-18 03:00:00', '2019-02-22 07:00:00', '2019-02-26 11:00:00', '2019-03-02 15:00:00', '2019-03-06 19:00:00', '2019-03-10 23:00:00', '2019-03-15 03:00:00', '2019-03-19 07:00:00', '2019-03-23 11:00:00', '2019-03-27 15:00:00', '2019-03-31 19:00:00', '2019-04-04 23:00:00', '2019-04-09 03:00:00', '2019-04-13 07:00:00', '2019-04-17 11:00:00', '2019-04-21 15:00:00', '2019-04-25 19:00:00', '2019-04-29 23:00:00', '2019-05-04 03:00:00', '2019-05-08 07:00:00', '2019-05-12 11:00:00', '2019-05-16 15:00:00', '2019-05-20 19:00:00', '2019-05-24 23:00:00', '2019-05-29 03:00:00', '2019-06-02 07:00:00', '2019-06-06 11:00:00', '2019-06-10 15:00:00', '2019-06-14 19:00:00', '2019-06-18 23:00:00', '2019-06-23 03:00:00', '2019-06-27 07:00:00', '2019-07-01 11:00:00', '2019-07-05 15:00:00', '2019-07-09 19:00:00', '2019-07-13 23:00:00', '2019-07-18 06:00:00', '2019-07-22 10:00:00', '2019-07-26 14:00:00', '2019-07-30 18:00:00', '2019-08-03 22:00:00', '2019-08-08 02:00:00', '2019-08-12 06:00:00', '2019-08-16 10:00:00', '2019-08-20 14:00:00', '2019-08-24 18:00:00', '2019-08-28 22:00:00', '2019-09-02 02:00:00', '2019-09-06 06:00:00', '2019-09-10 10:00:00', '2019-09-14 14:00:00', '2019-09-18 18:00:00', '2019-09-22 22:00:00', '2019-09-27 02:00:00', '2019-10-01 06:00:00', '2019-10-05 10:00:00', '2019-10-09 14:00:00', '2019-10-13 18:00:00', '2019-10-17 22:00:00', '2019-10-22 02:00:00', '2019-10-26 06:00:00', '2019-10-30 10:00:00', '2019-11-03 14:00:00', '2019-11-07 18:00:00', '2019-11-11 22:00:00', '2019-11-16 02:00:00', '2019-11-20 06:00:00', '2019-11-24 10:00:00', '2019-11-28 14:00:00', '2019-12-02 18:00:00', '2019-12-06 22:00:00', '2019-12-11 02:00:00', '2019-12-15 06:00:00', '2019-12-19 10:00:00', '2019-12-23 14:00:00', '2019-12-27 18:00:00', '2019-12-31 22:00:00', '2020-01-05 02:00:00', '2020-01-09 06:00:00', '2020-01-13 10:00:00', '2020-01-17 14:00:00', '2020-01-21 18:00:00', '2020-01-25 22:00:00', '2020-01-30 02:00:00', '2020-02-03 06:00:00', '2020-02-07 10:00:00', '2020-02-11 14:00:00', '2020-02-15 18:00:00', '2020-02-19 22:00:00', '2020-02-24 02:00:00', '2020-02-28 06:00:00', '2020-03-03 10:00:00', '2020-03-07 14:00:00', '2020-03-11 18:00:00', '2020-03-15 22:00:00', '2020-03-20 02:00:00', '2020-03-24 06:00:00', '2020-03-28 10:00:00', '2020-04-01 14:00:00', '2020-04-05 18:00:00', '2020-04-09 22:00:00', '2020-04-14 02:00:00', '2020-04-18 06:00:00', '2020-04-22 10:00:00', '2020-04-26 14:00:00', '2020-04-30 18:00:00', '2020-05-04 22:00:00', '2020-05-09 02:00:00', '2020-05-13 06:00:00', '2020-05-17 10:00:00', '2020-05-21 14:00:00', '2020-05-25 18:00:00', '2020-05-29 22:00:00', '2020-06-03 02:00:00', '2020-06-07 06:00:00', '2020-06-11 10:00:00', '2020-06-15 14:00:00', '2020-06-19 18:00:00', '2020-06-23 22:00:00', '2020-06-28 02:00:00', '2020-07-02 06:00:00', '2020-07-06 10:00:00', '2020-07-10 14:00:00', '2020-07-14 18:00:00', '2020-07-18 22:00:00', '2020-07-23 02:00:00', '2020-07-27 06:00:00', '2020-07-31 10:00:00', '2020-08-04 14:00:00', '2020-08-08 18:00:00', '2020-08-12 22:00:00', '2020-08-17 02:00:00', '2020-08-21 06:00:00', '2020-08-25 10:00:00', '2020-08-29 14:00:00', '2020-09-02 18:00:00', '2020-09-06 22:00:00', '2020-09-11 02:00:00', '2020-09-15 06:00:00', '2020-09-19 10:00:00', '2020-09-23 14:00:00', '2020-09-27 18:00:00', '2020-10-01 22:00:00', '2020-10-06 02:00:00', '2020-10-10 06:00:00', '2020-10-14 10:00:00', '2020-10-18 14:00:00', '2020-10-22 18:00:00', '2020-10-26 23:00:00', '2020-10-31 03:00:00', '2020-11-04 07:00:00', '2020-11-08 11:00:00', '2020-11-12 15:00:00', '2020-11-16 19:00:00', '2020-11-20 23:00:00', '2020-11-25 03:00:00', '2020-11-29 07:00:00', '2020-12-03 11:00:00', '2020-12-07 15:00:00', '2020-12-11 19:00:00', '2020-12-15 23:00:00', '2020-12-20 03:00:00', '2020-12-24 07:00:00', '2020-12-28 11:00:00', '2021-01-01 15:00:00', '2021-01-05 19:00:00', '2021-01-09 23:00:00', '2021-01-14 03:00:00', '2021-01-18 07:00:00', '2021-01-22 11:00:00', '2021-01-26 15:00:00', '2021-01-30 20:00:00', '2021-02-04 00:00:00', '2021-02-08 04:00:00', '2021-02-12 08:00:00', '2021-02-16 12:00:00', '2021-02-20 16:00:00', '2021-02-24 20:00:00', '2021-03-01 00:00:00', '2021-03-05 04:00:00', '2021-03-09 08:00:00', '2021-03-13 12:00:00', '2021-03-17 16:00:00', '2021-03-21 20:00:00', '2021-03-26 00:00:00', '2021-03-30 04:00:00', '2021-04-03 08:00:00', '2021-04-07 12:00:00', '2021-04-11 16:00:00', '2021-04-15 20:00:00', '2021-04-20 00:00:00', '2021-04-24 04:00:00', '2021-04-28 08:00:00', '2021-05-02 12:00:00', '2021-05-06 16:00:00', '2021-05-10 20:00:00', '2021-05-15 00:00:00', '2021-05-19 04:00:00', '2021-05-23 08:00:00', '2021-05-27 12:00:00', '2021-05-31 16:00:00', '2021-06-04 20:00:00', '2021-06-09 00:00:00', '2021-06-13 04:00:00', '2021-06-17 08:00:00', '2021-06-21 12:00:00', '2021-06-25 16:00:00', '2021-06-29 20:00:00', '2021-07-04 00:00:00', '2021-07-08 04:00:00', '2021-07-12 08:00:00', '2021-07-16 12:00:00', '2021-07-20 16:00:00', '2021-07-25 01:00:00', '2021-07-29 05:00:00', '2021-08-02 09:00:00', '2021-08-06 13:00:00', '2021-08-10 17:00:00', '2021-08-14 21:00:00', '2021-08-19 01:00:00', '2021-08-23 05:00:00', '2021-08-27 09:00:00', '2021-08-31 13:00:00', '2021-09-04 17:00:00', '2021-09-08 21:00:00', '2021-09-13 01:00:00', '2021-09-17 05:00:00', '2021-09-21 09:00:00', '2021-09-25 13:00:00', '2021-09-29 17:00:00', '2021-10-03 21:00:00', '2021-10-08 01:00:00', '2021-10-12 05:00:00', '2021-10-16 09:00:00', '2021-10-20 13:00:00', '2021-10-24 17:00:00', '2021-10-28 21:00:00', '2021-11-02 01:00:00', '2021-11-06 05:00:00', '2021-11-10 09:00:00', '2021-11-14 13:00:00', '2021-11-18 17:00:00', '2021-11-22 21:00:00', '2021-11-27 01:00:00', '2021-12-01 05:00:00', '2021-12-05 09:00:00', '2021-12-09 13:00:00', '2021-12-13 17:00:00', '2021-12-17 21:00:00', '2021-12-22 01:00:00', '2021-12-26 05:00:00', '2021-12-30 09:00:00', '2022-01-03 13:00:00', '2022-01-07 17:00:00', '2022-01-11 21:00:00', '2022-01-16 01:00:00', '2022-01-20 05:00:00', '2022-01-24 09:00:00', '2022-01-28 13:00:00', '2022-02-01 17:00:00', '2022-02-05 21:00:00', '2022-02-10 01:00:00', '2022-02-14 05:00:00', '2022-02-18 09:00:00', '2022-02-22 13:00:00', '2022-02-26 17:00:00', '2022-03-02 21:00:00', '2022-03-07 01:00:00', '2022-03-11 05:00:00', '2022-03-15 09:00:00', '2022-03-19 13:00:00', '2022-03-23 17:00:00', '2022-03-27 21:00:00', '2022-04-01 01:00:00', '2022-04-05 05:00:00', '2022-04-09 09:00:00', '2022-04-13 13:00:00', '2022-04-17 17:00:00', '2022-04-21 21:00:00', '2022-04-26 01:00:00', '2022-04-30 05:00:00', '2022-05-04 09:00:00', '2022-05-08 13:00:00', '2022-05-12 17:00:00', '2022-05-16 21:00:00', '2022-05-21 01:00:00', '2022-05-25 05:00:00', '2022-05-29 09:00:00', '2022-06-02 13:00:00', '2022-06-06 17:00:00', '2022-06-10 21:00:00', '2022-06-15 01:00:00', '2022-06-19 05:00:00', '2022-06-23 09:00:00', '2022-06-27 13:00:00', '2022-07-01 17:00:00', '2022-07-05 21:00:00', '2022-07-10 01:00:00', '2022-07-14 05:00:00', '2022-07-18 09:00:00', '2022-07-22 13:00:00', '2022-07-26 17:00:00', '2022-07-30 21:00:00', '2022-08-04 01:00:00', '2022-08-08 05:00:00', '2022-08-12 09:00:00', '2022-08-16 13:00:00', '2022-08-20 17:00:00', '2022-08-24 21:00:00', '2022-08-29 01:00:00', '2022-09-02 05:00:00', '2022-09-06 09:00:00', '2022-09-10 13:00:00', '2022-09-14 17:00:00', '2022-09-18 21:00:00', '2022-09-23 01:00:00', '2022-09-27 05:00:00', '2022-10-01 09:00:00', '2022-10-05 13:00:00', '2022-10-09 17:00:00', '2022-10-13 21:00:00', '2022-10-18 01:00:00', '2022-10-22 05:00:00', '2022-10-26 09:00:00', '2022-10-30 13:00:00', '2022-11-03 17:00:00', '2022-11-07 21:00:00', '2022-11-12 01:00:00', '2022-11-16 05:00:00', '2022-11-20 09:00:00', '2022-11-24 13:00:00', '2022-11-28 17:00:00', '2022-12-02 21:00:00', '2022-12-07 01:00:00', '2022-12-11 05:00:00', '2022-12-15 09:00:00', '2022-12-19 13:00:00', '2022-12-23 17:00:00', '2022-12-27 21:00:00', '2023-01-01 01:00:00', '2023-01-05 05:00:00', '2023-01-09 09:00:00', '2023-01-13 13:00:00', '2023-01-17 17:00:00', '2023-01-21 21:00:00', '2023-01-26 01:00:00', '2023-01-30 05:00:00', '2023-02-03 09:00:00', '2023-02-07 13:00:00', '2023-02-11 17:00:00', '2023-02-15 21:00:00', '2023-02-20 01:00:00', '2023-02-24 05:00:00', '2023-02-28 09:00:00', '2023-03-04 13:00:00', '2023-03-08 17:00:00', '2023-03-12 21:00:00', '2023-03-17 01:00:00', '2023-03-21 05:00:00', '2023-03-25 09:00:00', '2023-03-29 13:00:00', '2023-04-02 17:00:00', '2023-04-06 21:00:00', '2023-04-11 01:00:00', '2023-04-15 05:00:00', '2023-04-19 09:00:00', '2023-04-23 13:00:00', '2023-04-27 17:00:00', '2023-05-01 21:00:00', '2023-05-06 01:00:00', '2023-05-10 06:00:00', '2023-05-14 10:00:00', '2023-05-18 14:00:00', '2023-05-22 18:00:00', '2023-05-26 22:00:00', '2023-05-31 02:00:00', '2023-06-04 06:00:00', '2023-06-08 10:00:00', '2023-06-12 14:00:00', '2023-06-16 18:00:00', '2023-06-20 22:00:00', '2023-06-25 02:00:00', '2023-06-29 06:00:00', '2023-07-03 10:00:00', '2023-07-07 14:00:00', '2023-07-11 18:00:00', '2023-07-15 22:00:00', '2023-07-20 02:00:00', '2023-07-24 06:00:00', '2023-07-28 10:00:00', '2023-08-01 14:00:00', '2023-08-05 18:00:00', '2023-08-09 22:00:00', '2023-08-14 02:00:00', '2023-08-18 06:00:00', '2023-08-22 10:00:00', '2023-08-26 14:00:00', '2023-08-30 18:00:00', '2023-09-03 22:00:00', '2023-09-08 02:00:00', '2023-09-12 06:00:00', '2023-09-16 10:00:00', '2023-09-20 14:00:00', '2023-09-24 18:00:00', '2023-09-28 22:00:00', '2023-10-03 02:00:00', '2023-10-07 06:00:00', '2023-10-11 10:00:00', '2023-10-15 14:00:00', '2023-10-19 18:00:00', '2023-10-23 22:00:00', '2023-10-28 02:00:00', '2023-11-01 06:00:00', '2023-11-05 10:00:00', '2023-11-09 14:00:00', '2023-11-13 18:00:00', '2023-11-17 22:00:00', '2023-11-22 02:00:00', '2023-11-26 06:00:00', '2023-11-30 10:00:00', '2023-12-04 14:00:00', '2023-12-08 18:00:00', '2023-12-12 22:00:00', '2023-12-17 02:00:00', '2023-12-21 06:00:00', '2023-12-25 10:00:00', '2023-12-29 14:00:00', '2024-01-02 18:00:00', '2024-01-06 22:00:00', '2024-01-11 04:00:00', '2024-01-15 08:00:00', '2024-01-19 12:00:00', '2024-01-23 20:00:00', '2024-01-28 00:00:00', '2024-02-01 04:00:00', '2024-02-05 08:00:00', '2024-02-09 12:00:00', '2024-02-13 16:00:00', '2024-02-17 20:00:00', '2024-02-22 00:00:00', '2024-02-26 04:00:00', '2024-03-01 08:00:00', '2024-03-05 12:00:00', '2024-03-09 16:00:00', '2024-03-13 20:00:00', '2024-03-18 00:00:00', '2024-03-22 04:00:00', '2024-03-26 08:00:00', '2024-03-30 12:00:00', '2024-04-03 17:00:00', '2024-04-07 21:00:00', '2024-04-12 01:00:00', '2024-04-16 10:00:00', '2024-04-20 14:00:00', '2024-04-24 18:00:00', '2024-04-28 22:00:00', '2024-05-03 02:00:00', '2024-05-07 06:00:00', '2024-05-11 10:00:00', '2024-05-15 14:00:00', '2024-05-19 18:00:00', '2024-05-23 22:00:00', '2024-05-28 02:00:00', '2024-06-01 06:00:00', '2024-06-05 10:00:00', '2024-06-09 14:00:00', '2024-06-13 18:00:00', '2024-06-17 22:00:00', '2024-06-22 02:00:00', '2024-06-26 06:00:00', '2024-06-30 10:00:00', '2024-07-04 14:00:00', '2024-07-08 18:00:00', '2024-07-12 22:00:00', '2024-07-17 02:00:00', '2024-07-21 06:00:00', '2024-07-25 10:00:00', '2024-07-29 14:00:00', '2024-08-02 18:00:00', '2024-08-06 22:00:00', '2024-08-11 02:00:00', '2024-08-15 06:00:00', '2024-08-19 10:00:00', '2024-08-23 14:00:00', '2024-08-27 18:00:00', '2024-08-31 22:00:00', '2024-09-05 02:00:00', '2024-09-09 06:00:00', '2024-09-13 10:00:00', '2024-09-17 14:00:00', '2024-09-21 18:00:00', '2024-09-25 22:00:00', '2024-09-30 02:00:00', '2024-10-04 06:00:00', '2024-10-08 10:00:00', '2024-10-12 14:00:00', '2024-10-16 18:00:00', '2024-10-20 22:00:00', '2024-10-25 02:00:00', '2024-10-29 06:00:00', '2024-11-02 10:00:00', '2024-11-06 14:00:00', '2024-11-10 18:00:00', '2024-11-14 22:00:00', '2024-11-19 02:00:00', '2024-11-23 06:00:00', '2024-11-27 10:00:00', '2024-12-01 14:00:00', '2024-12-05 18:00:00', '2024-12-09 22:00:00', '2024-12-14 02:00:00', '2024-12-18 06:00:00', '2024-12-22 10:00:00', '2024-12-26 14:00:00', '2024-12-30 18:00:00', '2025-01-03 22:00:00', '2025-01-08 02:00:00', '2025-01-12 06:00:00', '2025-01-16 10:00:00', '2025-01-20 14:00:00', '2025-01-24 18:00:00', '2025-01-29 01:00:00', '2025-02-02 05:00:00', '2025-02-06 09:00:00', '2025-02-10 13:00:00', '2025-02-14 17:00:00', '2025-02-18 21:00:00', '2025-02-23 01:00:00', '2025-02-27 05:00:00', '2025-03-03 09:00:00', '2025-03-07 13:00:00', '2025-03-11 17:00:00', '2025-03-15 21:00:00', '2025-03-20 01:00:00', '2025-03-24 05:00:00', '2025-03-28 09:00:00'];
        var Nikes_Baby_Rocket_equity = [10000.0, 10000.0, 10000.0, 10183.870311999995, 11951.275874337533, 11722.56040733034, 14430.80379120909, 13722.15059805026, 13314.58900313757, 13935.936489950656, 14985.25978173793, 14394.780755150929, 14436.695873610248, 15457.947739709436, 15529.192131949329, 15362.131246073235, 15208.509933612504, 14505.255794178263, 14688.146620380729, 14783.619573413203, 15163.604025034509, 15283.013706048025, 16519.401884607072, 16131.30001255068, 15433.991503004509, 16363.71246163459, 16616.46705439112, 16585.246860958156, 17405.09332814668, 17456.30566883923, 17797.104978942767, 19695.09996632517, 20212.186943515397, 19058.35503770352, 21499.6633890574, 20861.10188673901, 22281.00892349289, 22914.761664363527, 22893.402361315348, 24031.533653207294, 24913.00796453241, 26311.76907090697, 26889.10616070309, 27947.792573922285, 26485.0125610429, 25734.388094312373, 27276.50700398177, 27053.14925679645, 27405.983192690903, 27826.45773036065, 26997.229289995903, 30260.15997685304, 30718.1378198636, 31591.87312083796, 31591.87312083796, 35230.53374099568, 36110.462399565135, 35621.911315099576, 34218.2658778585, 34307.47244476297, 32464.73454548431, 34318.927481211635, 34994.50071651841, 36857.04685617102, 37997.79738234212, 37241.64121443351, 36976.58162497218, 34972.150441075435, 34972.150441075435, 35555.019615093355, 36243.11608747936, 35395.24258199281, 34090.36839448535, 34658.54120106011, 39489.46810073916, 39094.57341973177, 40264.52886358052, 41310.55298719804, 41786.29760886837, 45384.67650390749, 48248.84850268771, 48248.84850268771, 45879.49326424319, 44516.8264348019, 48762.26351174619, 47270.803468937054, 51020.02942834682, 57756.10943223548, 57690.49964704264, 56704.547085684826, 57428.664151969024, 62695.233244182, 60003.96666707919, 63012.60612485029, 62106.16462587981, 61641.27514118927, 64287.86178175879, 64336.74671102889, 63466.31234691404, 64943.49076678846, 67047.69883372688, 69681.09628851086, 74610.33703996013, 75021.92804266358, 76473.82779118289, 75709.08951327106, 75521.48705570507, 77760.6613861632, 76414.60242694717, 78580.09261508533, 73219.3649832916, 74628.76453985498, 72404.8273565673, 75976.20994689551, 75608.97741659301, 83931.85614652639, 87112.39100118814, 86801.8353272689, 83372.98922817112, 88378.0865413102, 86310.56459140088, 83439.98197925928, 83892.06938283828, 83296.63061759759, 88654.08942288849, 86012.19755808642, 94719.05908010894, 95608.02586529349, 101008.91821106795, 108429.0009795956, 103104.38019707064, 104449.09329969589, 103404.60236669892, 102005.60530966902, 99975.6937640066, 98225.03938564385, 94948.50745684117, 95224.98113193797, 94521.82815858712, 98074.890680916, 100655.54542770228, 99648.98997342524, 99648.98997342524, 106058.19710237104, 101868.6862004332, 105292.60593105888, 104532.54125234798, 103479.94386582966, 107264.63274684535, 107548.05808595232, 111132.99335548408, 111132.99335548408, 112412.86106220364, 110455.69177368846, 112996.17268448324, 112593.34132886304, 114019.52365236192, 107621.89772746268, 109287.45421669287, 118093.76019338964, 130061.45136798803, 142906.7993994527, 149604.841087305, 150959.7388266606, 146880.99387364037, 150299.64900604935, 153013.3556174571, 153417.66280700508, 150364.65131714568, 162565.4465436993, 164641.40729606236, 164641.40729606236, 171080.73094720163, 187420.84164966995, 222682.78891506584, 234336.5064459685, 253862.35355923447, 267801.65899475873, 272265.01997800474, 269378.86955866707, 267434.33259776526, 270849.4690250387, 266205.37323816004, 266205.37323816004, 309239.0373699928, 315674.72570597054, 312517.9784489108, 338005.5558024109, 344624.08014675067, 404378.373160636, 414864.9557604615, 440945.3914205481, 437644.7387876087, 444938.81776740216, 440489.4295897281, 458131.0312447968, 477440.9771622159, 461561.88085628935, 514341.010683224, 534940.3681610872, 574312.4547602928, 557197.9436084362, 564255.7842274763, 574539.3906425956, 644225.7473924715, 677808.5083447526, 713214.6702959765, 733426.2440202342, 739201.7966989254, 748278.360346472, 759858.7162511939, 864846.928062251, 896477.9517189867, 899047.8551805811, 965406.087090696, 960739.7774606212, 951132.3796860152, 956291.1541239067, 974798.2616111224, 1003700.5426687612, 1002932.19397713, 992902.8720373588, 993166.6886489646, 1096419.9325705562, 1099675.2033503582, 1091799.329543963, 1096362.1938730888, 1165284.8716274572, 1235113.9201792483, 1244713.129326805, 1445928.326812736, 1424362.6401359818, 1382057.645361303, 1349450.298648412, 1439612.4079279583, 1484516.087816735, 1503877.5177630628, 1468695.722143434, 1543361.220869624, 1482395.365922832, 1484819.174432516, 1533084.627821203, 1531998.3561089062, 1628307.2489258903, 1830087.9992188397, 1920983.1832191527, 1882755.617873092, 1948894.0213870192, 1999433.9104860404, 2261811.717212709, 2350407.8773730868, 2464115.984138726, 2422112.3726345226, 2575307.6794266608, 2810933.4906914253, 3088060.4708716893, 3073134.268824521, 3258984.476711184, 3386640.3326171306, 3452181.9999074712, 3564034.7680136734, 3445499.3803405464, 3426084.6027010386, 3345934.5859316704, 3234983.0071512507, 3294464.74063521, 3217393.8463555328, 3247736.2974602133, 3183106.3451407547, 3364861.717448292, 3496664.831459896, 3506846.516274424, 3828228.127081258, 3789945.8458104455, 3938801.569686621, 4344735.980150443, 4090482.679699938, 4238558.152705076, 4294083.264505513, 4451589.819974457, 4532427.095265852, 4886638.002357441, 4766730.626334084, 4888751.398933847, 5494568.222462259, 5398904.7896749405, 5650853.679859772, 5538401.691630564, 5797656.074796341, 5739679.514048378, 6582827.508292198, 6865463.182208974, 6882901.458691784, 7098313.768631588, 7073008.280046416, 7379685.740006173, 7252410.587750864, 7769042.577699518, 8513025.614127178, 8260180.240361987, 8367911.576101848, 8306121.887770789, 8807126.851573108, 9057633.496067766, 8844836.1720012, 9967454.120861365, 10731743.997914445, 10693485.330561876, 10617376.68606607, 11003235.743994366, 10255734.751828734, 10322397.02771562, 11927343.591926618, 11530654.89818316, 11374652.632336428, 12135876.370878112, 13079292.14872854, 13053385.8715103, 12746713.539860798, 12896520.4124488, 12639879.65624107, 12468870.300413905, 13132513.037654124, 13607910.009617204, 13645991.881322348, 13496939.460284889, 14025620.517597606, 14446178.74881777, 13738172.24920253, 14004047.165432204, 13725366.626840103, 14085720.64139372, 14121498.371822862, 14356856.67801991, 14765217.776084391, 14712579.774712648, 15094115.220978351, 15840809.376236357, 16362372.067316191, 17922802.731507387, 18341001.461909223, 19644842.87694584, 20147719.815743912, 20304306.32424154, 21314937.334171727, 21734525.072835498, 21875799.48580893, 22220932.095821638, 21778735.547114786, 21560948.19164364, 21701094.35488932, 21189093.404176213, 21080063.24730926, 22068085.81171065, 20669912.132702217, 20567298.153938048, 21098716.003639504, 24141458.81485877, 24269921.311162755, 23697312.96769472, 23612832.046964888, 23293365.77771029, 23444772.655265413, 23984002.42633652, 24695116.10627618, 24117397.16481189, 26260910.358252443, 26330299.56169206, 25548263.334410243, 27333375.50750121, 29272624.10634117, 29654404.988092117, 32575573.59904839, 37593603.15255698, 40776127.77798211, 40177344.50409024, 40697194.70047926, 42010281.944034986, 42132608.63828237, 41294169.72638056, 47428481.6367816, 48603627.891818106, 54143760.966690846, 55389067.46892474, 56045750.72924005, 57499162.76460114, 61113840.73189827, 64157993.3303952, 60397314.15472856, 61185031.02526307, 61759270.89694446, 69236653.38963066, 76810283.710226, 87647652.48560633, 87879243.87776904, 87221432.58564639, 93049041.31209023, 91790148.2650145, 92057425.34296164, 92300668.67794532, 93136590.60683385, 93407788.29261903, 99792390.80719636, 102412400.13933007, 100374393.3765574, 98829744.3078642, 95894402.07217634, 92714532.58194546, 91134109.73154664, 91399476.53845096, 97298696.20336612, 100364018.2490134, 96045974.19120702, 98619114.88626038, 104156165.34778516, 120341600.82409038, 119772147.69041894, 114619091.64854003, 111396633.0978118, 109878296.9886886, 108655942.66646668, 109651174.05148885, 112932626.61086623, 107648624.410819, 106181373.66009954, 103323026.6989732, 105350950.28832348, 105118757.6777934, 111020735.8732581, 118897436.15220138, 126059642.9148891, 129341894.01658158, 123789741.19133784, 130082959.46921784, 135668110.35891658, 146270624.9550868, 144807918.70553592, 148138500.83576328, 140863866.43799147, 145991494.2993607, 151337820.83556503, 162498719.37751478, 175644784.6453773, 174813638.37223142, 167908150.02925155, 183306381.6672828, 176552118.30692434, 179739821.36462256, 190358089.11876184, 194105316.656778, 190745298.93239415, 186949467.48363957, 192544170.9955268, 197493147.8590949, 191627403.87453192, 205211288.46216765, 213487374.2211433, 211352500.47893187, 211946295.3290274, 219070045.81091973, 216106170.48662776, 213182394.46895447, 226584080.115358, 219070525.74970737, 219708421.3310249, 229969577.6553328, 232968898.37950808, 262787162.60374883, 262787162.60374883, 273016874.5917249, 269295654.59103966, 272488377.5691762, 279463649.50331056, 281280163.2250821, 283932847.35607255, 282720162.49639875, 269579785.9931725, 269301025.64060944, 267384331.93511957, 261129127.7666146, 285317598.66109204, 295766159.76589185, 278429557.10485554, 304093365.6021913, 308059427.29971653, 310881089.90717953, 302464225.9214614, 312769788.9652204, 347065956.6900844, 347065956.6900844, 338436118.4125484, 340268393.9589557, 342113949.7724981, 342982919.2049203, 346237243.522738, 335953651.1528692, 352068224.99648947, 359947323.1886365, 386945977.0466089, 388094819.6524603, 380371732.7413763, 387837298.106545, 366068575.8027366, 371943966.355612, 375312480.6063512, 366495001.78072953, 397463099.6391671, 401234348.1712788, 414276850.6275139, 498617407.0209717, 502656354.3675909, 555760675.2616514, 560287729.4497396, 611669632.2371306, 579440605.173809, 623934927.8928237, 661323881.8162856, 593309754.7701776, 582453683.9994239, 625711908.5821095, 625711908.5821095, 613260241.6013254, 612763187.3615049, 656323841.5030485, 644896885.0696629, 769327336.8533815, 801337187.4140813, 849885397.1723645, 933727931.2746222, 936195120.5874292, 892773373.5538154, 913552673.8232805, 919490766.2031316, 927912818.8429254, 954714386.4118198, 931192770.1997272, 935929237.1556182, 947881053.5140954, 1073028033.6742772, 1020438983.2879996, 1000410624.5351003, 967138204.6710054, 975897101.4929904, 1045135058.8086864, 964196045.733591, 932032972.5576372, 910228940.537761, 921852564.1084282, 924288375.138572, 888265549.3809186, 940520811.8928784, 1019809262.0117792, 962729424.3934884, 981130507.926108, 961606010.8183784, 947421844.632378, 984577110.3784946, 976514753.8138412, 1019764025.8386524, 1107029388.123845, 1205330435.559795, 1211867935.4630485, 1246743962.3598576, 1263076308.2667718, 1330795629.341457, 1252662009.5833864, 1256309545.2710967, 1324172853.3490584, 1381026612.059456, 1326473298.829883, 1347001799.602574, 1315489963.39707, 1284715316.8681755, 1436390446.3682096, 1448090837.697444, 1441238511.8207667, 1621644447.8639023, 1663544497.1078098, 1635187521.5326948, 1692456526.5318582, 1684603204.177175, 1638887825.5618012, 1898556717.031114, 1798888349.0614858, 1821860153.279001, 1874548237.3371544, 1860706342.583224, 1866124398.4809816, 1914604070.1318257, 1920179066.914848, 1937766898.9777684, 1988221091.865553, 2034496937.7787232, 2151723798.8382525, 2322148186.2363377, 2477552872.507758, 2522285855.283826, 2386831183.79284, 2345275149.6731806, 2199709428.1612587, 2134290044.9061296, 2263025202.0757074, 2254957517.230307, 2366884914.624663, 2326301337.704234, 2324890239.4053483, 2354579087.762555, 2449538281.297399, 2456010696.298071, 2574827659.583752, 2546183679.790428, 2486364404.272535, 2502525772.900306, 2707719830.9661245, 2733255671.6184363, 2829081809.912204, 2779825950.922964, 2823134582.904483, 3063265605.410122, 3083176831.845288, 2898860684.170314, 2819330634.5574803, 2884950555.0768065, 3110550609.073526, 3048650651.9529624, 3055649477.0463943, 3187138586.219429, 3221147890.6063533, 3630980363.508108, 3678546206.270064, 3812232748.215952, 3935751376.597798, 4037443320.666333, 3914128886.446772, 4303903639.668602, 4283537686.433432, 4553911295.7679, 4737568640.863179, 5037440235.68729, 5101768347.497017, 5282944519.479995, 5517294998.607415, 5675752217.7916565, 5965447786.6271925, 6578299803.209389, 6774472621.66493, 6741728072.758659, 6355635423.387152, 6374141937.392943, 6522500090.985764, 6607568167.797423, 6411316785.645672, 6493189300.998367, 6876077796.709762, 7088897054.72573, 6828390208.073995, 6493068586.693909, 6885742007.735483, 6861194337.477906, 7137903446.277418, 7351514396.957959, 7372920738.745666, 7442452943.286769, 7442452943.286769, 7388728889.682624, 7026607655.49685, 7001557799.205004, 7201442274.379176, 6795849207.59192, 7089007928.549024, 6756631039.257215, 6621498418.472071, 6701303357.9206505, 6943890539.477375, 6825216811.479301, 7029470212.029465, 7575658711.904812, 7494588046.63554, 7457381816.936726, 7525687518.881543, 7719693794.314966, 7692173085.938234, 8321128746.425362, 8375216083.277127, 8816787296.577272, 9821217730.252346, 9812063178.750778, 9519663696.024004, 9547383314.564837, 10290162027.456364, 10290162027.456364, 10290162027.456364, 10114299248.145563, 9617616557.252869, 9331963727.8859, 9392621492.11716, 9359136796.497765, 9481200998.381884, 9317681663.935097, 9730019505.93798, 9918324622.086472, 10212397987.969025, 9909079555.328358, 10038316248.158846, 10784716792.67266, 11710756153.06275, 13124336545.352495, 13378331896.136389, 14038220792.579697, 13660934390.039871, 13341349719.076485, 13651869633.78799, 14294759732.850603, 15098891197.111784, 14635289668.4705, 14200606930.027258, 14100340846.68249, 13495067142.72102, 13534362450.34154, 14640174033.820152, 15392435689.377382, 15068988681.496334, 15774372939.0985, 15856694133.343346, 15426388187.054926, 16778743441.283794, 17833537134.270878, 18002747057.91168, 18119764913.788105, 18172526543.55761, 18186229605.547832, 19066043597.646626, 19314707704.75803, 20016647095.20077, 20404029366.517426, 20854480838.806107, 20915205489.610767, 22597565377.239437, 22436731933.642548, 23027111982.65332, 22605773962.6395, 23427318012.820107, 24940815066.58885, 25141409765.08568, 23704002569.80752, 24472026475.47082, 23481026676.702805, 23780879387.364304, 24961270555.37447, 25451186027.2361, 25109197791.235157, 24431878562.839382, 24815923543.843517, 25040669203.577568, 26824463792.11619, 26027750393.02655, 27707666107.723816, 27707666107.723816, 27707666107.723816, 27156283552.18011, 27409590895.170288, 28130822296.60232, 27750230555.96378, 27823554990.1618, 29125984641.90665, 29022150506.658257, 31096154224.19388, 32396282346.302773, 36180671022.405136, 35691170060.90816, 35923162666.30405, 36437669992.88194, 43425709601.43703, 42991452505.42265, 42991452505.42265, 45245952069.5893, 45901637736.07037, 45901637736.07037, 45871179559.0702, 43287664801.10236, 46307996755.55687, 44612804543.49057, 47097462427.7122, 49552599976.26678, 48645912486.48231, 50037921651.38365, 50496242741.29955, 49991280313.88656, 51289993763.53404, 48597427391.54924, 48738934717.057205, 49198579639.05572, 51630577308.18433, 51446514300.08065, 54219369362.1958, 53140403911.88811, 51373019812.45835, 50988472209.95416, 57185015218.26363, 57071748319.70925, 58170322403.115326, 58197197073.45314, 56462920600.66424, 60181060321.92043, 68489172547.58229, 68489172547.58229, 69807520629.95068, 66379484110.52611, 66572769717.79495, 69239009144.99265, 67802285473.14536, 67049147777.07002, 73580694207.48328, 71519306860.75436, 76135395676.9367, 77045904862.2439, 76476988424.0948, 80078421779.58858, 82386687356.26482, 81769990046.72823, 79691973271.22432, 80731335909.62096, 83347079631.89423, 87940657723.35277, 89528453475.72871, 90348487102.65186, 96154024632.97176, 96901326220.09764, 96121485407.88351, 98008064168.97554, 100913954267.55354, 105936867628.33669, 105428531538.74164, 110788303320.34776, 118083823758.25296, 117211878279.31168, 130555217294.64424, 131262036452.20616, 125640278384.53096, 138422698916.62613, 129019001023.94966, 143678544652.8971, 145917001780.74225, 153171174975.47644, 156552672817.913, 164946180515.16708, 168435120303.2712, 174556426795.82065, 182727469864.97165, 182587589087.36185, 183774408416.4297, 193088653534.77075, 190940615599.26703, 197891102229.8806, 211134531241.09445, 216048687455.73093, 212365896064.29584, 210854426231.31277, 233371960879.71432, 241151026242.3715, 231161747321.28864, 278132187423.1729, 264879358498.6805, 270005570048.10223, 309597826811.89966, 322542091182.3399, 321765541332.2008, 338538815764.304, 329425528170.5607, 333297130679.8318, 345040567598.4811, 334792517700.2386, 344280101082.4202, 379208339969.1891, 364266018333.1266, 365326698142.62445, 362726561804.07263, 360048144256.3443, 360214486383.78784, 365552514386.5214, 393280307202.13336, 419202104156.7848, 422503218420.5782, 416787024502.4397, 434721985622.2859, 429196270722.1057, 435825654419.3153, 426711024443.7469, 440478774457.1181, 466921860532.5924, 479131816343.5656, 485250329638.27295, 478685208090.9814, 496773934475.07697, 493055382865.9572, 529471373740.5914, 552204386013.5562, 537363530513.1292];
        var Nikes_Baby_Rocket_drawdown = [0.0, 0.0, 0.0, -4.920400000000007, -2.7236440540000086, -6.821991999999989, -1.999999999999995, -6.812484039999994, -9.58024645152796, -5.36065795259926, -0.9999999999999992, -4.900995009999997, -4.624083112047466, -0.9999999999999976, -6.206765186259986, -7.215779741816303, -8.143621944398143, -12.391137209405716, -11.286512959700248, -10.7098752939383, -8.414844709368312, -7.693633962848398, -1.989999999999989, -4.29261753699998, -8.429765328307322, -2.9137090090865443, -1.4141100642148965, -4.638108667600002, -2.970099999999998, -2.6846014851412185, -2.000000000000004, -0.9999999999999948, 0.0, -9.59869663839998, -2.980000000000002, -5.861591019999998, 0.0, -1.000000000000002, -2.339405649999998, 0.0, 0.0, 0.0, -1.0000000000000004, -0.999999999999996, -6.181633608164971, -8.840585008701034, -3.3779077094668764, -4.16911212795033, -2.919261730601088, -1.42980673628622, -4.367198495544887, 0.0, -1.8909449125912985, -2.9700999999999977, -2.9700999999999977, 0.0, -2.349369999999986, -3.67051955099049, -7.466285435308465, -7.2250512643923, -12.208219710100291, -7.194074327195881, -5.367175759437883, -0.3304414764856884, -6.821991999999979, -8.67623435919998, -9.32621215393346, -14.24146824126061, -14.24146824126061, -12.812159378614965, -11.124812663130092, -13.203963817136898, -16.403769749547752, -15.01049924537354, -3.1641245522211348, -4.13248330669892, -1.263524440224154, -2.339405650000002, -4.920400000000003, -2.979999999999997, -2.979999999999996, -2.979999999999996, -7.744359199600008, -10.48444398701269, -1.947612166410992, -4.946677591677707, -2.9800000000000075, 0.0, -3.3357400000000066, -4.98776893581879, -3.774462745129192, 0.0, -4.292617537000002, -2.970099999999999, -4.365883056912315, -5.081742675878081, -1.0063988221896842, -1.000000000000005, -2.3394056499999945, -0.0663553165037495, -1.999999999999991, -2.9700999999999804, 0.0, -2.970100000000004, -2.9700999999999915, -3.9403989999999984, -4.178428770748316, -1.337367094586612, -3.045245070349386, -1.9999999999999976, -8.685552160000027, -6.927840353527865, -9.701390710992737, -5.247393748090521, -5.705382365461227, 0.0, -1.0000000000000022, -1.352935000000007, -5.249691361630007, -1.9900000000000029, -4.282851477565027, -7.466285435308502, -6.964927144369866, -7.625259990046055, -1.6839168578758126, -4.613736135511098, 0.0, 0.0, -1.3630000000000022, -2.0000000000000058, -6.812484040000011, -5.597109159970699, -6.541138068370985, -7.805575722045677, -9.640244765176968, -11.22251636714291, -14.183902399677583, -13.934020726742252, -14.569542503811386, -11.358223354483844, -9.025783113402513, -9.93552528226849, -9.93552528226849, -4.142773408119197, -7.9293255729516705, -4.834727904709201, -5.521687462029896, -6.473043122848048, -3.0523760781582587, -2.796211371469598, 0.0, 0.0, 0.0, -2.9800000000000093, -0.74854000000007, -1.1023714549000765, -2.000000000000004, -7.763184000000008, -6.3357270355840125, 0.0, 0.0, -1.990000000000007, -1.000000000000005, -1.3629999999999869, -4.920400000000001, -2.707422309999999, -2.9799999999999867, -2.970100000000023, -4.900995010000024, -1.3630000000000178, -0.103405510000009, -0.103405510000009, -1.9999999999999931, 0.0, -1.0000000000000022, -1.999999999999996, 0.0, -1.9900000000000004, -0.3564999999999903, -2.339405649999996, -3.0443779280719205, -1.9900000000000015, -3.670519550990518, -3.670519550990518, -1.0000000000000082, -1.2118028059546748, -2.1996847778951363, 0.0, -0.3666666666666839, 0.0, 0.0, -2.979999999999998, -3.950200000000001, -2.349370000000002, -3.3258763000000044, 0.0, 0.0, -3.3258762999999907, 0.0, 0.0, 0.0, -2.979999999999983, -2.0, -4.312146666666666, 0.0, -3.960000000000009, -3.9502000000000086, -3.960000000000007, -3.706233507999996, -2.5238547525238246, -2.9800000000000137, -3.9403990000000015, -0.4271027008554966, -1.999999999999998, 0.0, -4.920400000000004, -5.871196000000007, -5.360657952599259, -3.529102292759221, -0.6687884111883896, -0.7448280297976291, -1.7373797494996526, -1.7112711418482167, 0.0, 0.0, -0.9999999999999944, -3.960000000000004, -1.0000000000000004, -2.000000000000001, -3.71605839999999, 0.0, -4.920399999999992, -7.744359199600002, -9.920977284884106, -3.9024416648841513, -3.9502, -2.69749449595, -4.973794807303307, -0.1428561072060719, -4.087413005259233, -5.871195999999997, -2.811450086002617, -2.880313324609847, 0.0, 0.0, -2.0, -3.9501999999999935, -4.292617536999997, -1.999999999999996, 0.0, -2.979999999999995, -1.9999999999999951, -3.670519550990485, -2.3593333333333555, 0.0, 0.0, -2.980000000000004, 0.0, -1.0000000000000009, 0.0, -2.9799999999999915, -6.206765186259998, -6.735273421782863, -8.917113704810726, -11.937432774053605, -10.318223602918032, -12.41624414697173, -11.59026325795342, -13.349617019120156, -8.401880150911916, -4.813941493250427, -4.536776110473869, -2.349370000000001, -3.3258763000000044, -1.9900000000000064, 0.0, -5.851985059900013, -2.443826919068391, -1.1658410517081783, 0.0, -1.0000000000000038, 0.0, -4.900995010000002, -2.466610738683899, 0.0, -3.950200000000016, 0.0, -1.989999999999985, 0.0, -0.9999999999999992, 0.0, -0.9999999999999968, -2.980000000000004, -5.861591020000015, -6.197194448013711, -2.1300132706726718, -3.960000000000009, 0.0, 0.0, -2.970099999999997, -1.7046117867249957, -3.940399000000004, 0.0, -0.9999999999999978, -3.325876299999987, -1.999999999999996, 0.0, -1.0000000000000049, -1.7046117867250032, 0.0, -6.793465209301029, -6.187622733161494, 0.0, -4.910698000000012, -6.1971944480137005, -1.990000000000012, 0.0, -1.999999999999997, -4.3023825999999925, -3.17768793011618, -5.104451940306864, -6.388327023448277, -1.9999999999999944, -0.9999999999999956, -3.959999999999993, -5.009025577688558, -1.2881873135379005, 0.0, -4.900995009999997, -3.060543490933525, -4.989638675463943, -2.495179615948941, -2.2475173721734456, -0.6183093283763393, -1.989999999999996, -2.339405650000012, -2.9799999999999938, -0.9999999999999976, 0.0, 0.0, 0.0, 0.0, -0.7485399999999787, 0.0, 0.0, -1.0000000000000062, -0.3564999999999926, -2.970100000000008, -4.900995009999997, -5.851985059900003, -5.240022962789361, -7.47572580519799, -7.951816779787396, -3.6375184322560177, -9.742782229103236, -10.190856326767287, -7.870367672538276, 0.0, -1.9999999999999951, -4.312146666666675, -4.653273863800005, -5.943253093050765, -5.331884238155579, -3.154517575633166, -1.989999999999997, -4.282851477565003, -0.9999999999999944, -0.999999999999998, -3.940399, -2.9799999999999947, -1.000000000000005, 0.0, -2.0000000000000004, -2.9799999999999938, 0.0, -5.861591020000011, -4.643544606031028, -1.9900000000000104, -2.9700999999999884, -4.900995009999983, 0.0, -1.9900000000000049, -1.0000000000000056, -0.9999999999999992, -3.950200000000005, -2.0000000000000027, -2.980000000000004, 0.0, -5.861591019999991, -4.633814355480584, -3.73877409335109, 0.0, 0.0, -0.9999999999999996, -1.000000000000005, -2.9799999999999973, -2.9799999999999978, -4.292617537000004, -4.01393414879123, -3.7603101669925976, -2.8887144536230664, -2.605943141808775, 0.0, -1.0000000000000058, -2.9701000000000017, -4.463281075656683, -7.300817164428597, -10.374732814335909, -11.902495682666505, -11.645970946913929, -5.943314373764101, -2.980129426372772, -7.154295456475039, -4.666892285924287, -1.999999999999996, -0.999999999999998, -2.9700999999999955, -7.144697534397413, -9.755278015424537, -10.98531357607432, -11.975568154762565, -11.16931057628811, -8.51093782577088, -12.791617557719768, -13.980267810408042, -16.295873943816126, -14.6530110004973, -14.841115048182122, -10.05980015026174, -3.678721951920503, -1.9899999999999989, -0.9999999999999974, -5.249691361630003, -0.4327786803432434, 0.0, 0.0, -1.0000000000000075, 0.0, -4.9106979999999965, -1.4493237911074213, -1.9899999999999909, -1.999999999999993, 0.0, -4.910698000000009, -8.666915607604004, -2.0000000000000058, -5.610991626666703, -3.90676891050699, -3.325876300000008, -1.4228316738469655, -3.129230233661358, -5.056958552011494, -2.215665797514582, -1.9899999999999984, -4.900995009999991, 0.0, 0.0, -0.9999999999999992, -1.0000000000000029, 0.0, -1.9900000000000029, -3.316011593500015, -1.0000000000000009, -4.282851477564999, -4.004139652275776, 0.0, 0.0, 0.0, 0.0, 0.0, -1.363000000000002, -4.910697999999997, -2.476562109897153, -1.8426597636114777, -0.9999999999999992, -2.0594452795300318, -6.6115640693535145, -6.7081328578086, -7.37211820320223, -9.539060103431844, -1.1596355999699048, -1.000000000000001, -6.8029751097999815, -1.0000000000000022, -0.9999999999999908, -1.9899999999999824, -4.643544606030981, -1.394558846812268, 0.0, 0.0, -5.632108837840011, -5.121205982218444, -4.606600121109501, -4.364300885417098, -3.456880824961744, -6.324308007579547, -1.8309981394358563, -3.3160115935000096, 0.0, -1.0000000000000044, -2.9701000000000137, -1.0656917633387677, -6.6187251947227175, -5.119958307662613, -4.260676261275584, -6.509947211396098, 0.0, -1.000000000000005, -0.3565000000000098, 0.0, -3.960000000000005, 0.0, -3.68034808689999, -1.362999999999991, -6.560208386523968, 0.0, 0.0, -10.284541193236718, -11.92610761314858, -5.384951944631132, -5.384951944631132, -7.267791400932984, -7.34295188636039, -0.7560652882374014, -3.950200000000016, 0.0, -3.960000000000009, 0.0, -2.000000000000006, -1.7410545999999905, -6.8124840400000055, -4.643544606031001, -4.023727645970213, -3.1446353835491143, -0.3470928273263473, -2.8022746809742705, -2.7335692000000087, -1.4914768786840096, 0.0, -4.900995010000011, -6.767522083325188, -9.868318970258615, -9.052040499695952, -2.5994637595887693, -10.142511148382782, -13.139923347001725, -15.17193288781629, -14.088678470793695, -13.86167498591706, -17.218793777520634, -12.348905864804438, -4.959681386912657, -10.279191765671095, -8.564317321094801, -10.383887406405016, -11.705769569858912, -8.243113928059048, -8.994478879545568, -4.96389713633458, 0.0, 0.0, -3.9403990000000095, -2.980000000000004, -1.709037999999996, -1.000000000000004, -6.8124840400000055, -6.54113806837098, -1.4926788221959424, 0.0, -3.95020000000001, -2.980000000000011, -5.249691361630003, -7.466285435308483, 0.0, 0.0, -2.000000000000005, 0.0, -0.999999999999996, -2.687565668857753, -1.9900000000000035, -2.444785166954782, -5.0921584896091945, 0.0, -5.24969136162999, -4.039729920318002, -1.2645647864290934, -3.940398999999997, -3.660690012169182, -2.970100000000012, -2.6875656688577543, -1.7962348642781425, -0.9999999999999972, 0.0, -2.000000000000001, 0.0, 0.0, 0.0, -9.589472015607994, -11.163568671964526, -16.677437364348798, -19.15545131886349, -14.27910580733888, -14.584700795135731, -10.3450146526654, -11.882275704802552, -11.935726549213516, -10.811145777246953, -7.214196448465914, -6.969028519741706, -2.468373233727596, -3.5533755389905437, -5.819263600090179, -5.20708881349077, 0.0, -1.0000000000000084, 0.0, -2.9700999999999955, -1.990000000000008, -0.999999999999994, -0.356499999999991, -9.01108816654961, -11.507362896772865, -9.447696768195224, -2.366603311438012, -4.309507905540406, -4.089830056638784, -1.0, -4.910697999999989, 0.0, 0.0, 0.0, 0.0, 0.0, -3.0542703494648675, 0.0, -4.9106979999999965, 0.0, 0.0, -1.9899999999999969, -1.0000000000000029, -1.065691763338728, -2.970099999999989, -3.3258763000000067, -2.980000000000006, 0.0, -1.999999999999996, -2.9800000000000013, -8.536247365328084, -8.269920695153266, -6.134903099332966, -4.910691039280231, -7.734938604722573, -6.556713770704876, -1.0465772815125904, -1.9999999999999936, -5.601360095199995, -10.236992217032215, -4.808503840773355, -5.147861524580997, -1.3225143015916878, -1.9899999999999956, -1.7046117867250057, -1.3529349999999876, -1.3529349999999876, -2.065028210173661, -6.864816290637958, -7.196843220561834, -4.900995009999993, -10.257074474739696, -6.3857523693958695, -10.774971951947805, -12.559472512908842, -11.505604406259136, -8.302107285765757, -9.869259117451255, -7.171980653212093, 0.0, -3.95020000000001, -4.427030867699197, -3.551632651034763, -1.990000000000001, -2.3394056499999887, -1.000000000000002, -0.999999999999998, -1.000000000000009, 0.0, -0.0932119799499916, -3.0704342629474923, -2.788192087871304, 0.0, 0.0, 0.0, -3.9502000000000015, -8.666915607603991, -11.379599547142554, -10.80356694419896, -11.121552228042878, -9.962377292589087, -11.515228259760804, -7.599488149137727, -5.811260581285644, -3.01861155189047, -5.899055770187762, -4.671767730306427, 0.0, 0.0, 0.0, -0.3565000000000028, 0.0, -2.687565668857759, -4.96409825575306, -2.7521376426557063, 0.0, -1.9900000000000049, -6.197194448013697, -8.983231575713246, -9.625872762358888, -13.50528839707258, -13.253430717222622, -6.165888803676144, -2.0694381940000066, -5.871195999999999, -1.4649960928986965, -0.9507747525088104, -3.638691303136624, 0.0, -0.9999999999999908, -1.9899999999999944, -1.3529349999999902, -1.0656917633387168, -0.999999999999998, 0.0, 0.0, -1.9900000000000095, -0.9999999999999948, -1.0000000000000049, -1.9899999999999916, -2.980000000000006, -3.670519550990506, -1.1357919642427328, -4.302382599999987, -1.000000000000007, 0.0, -4.920400000000001, -10.356376042758065, -7.451868840369058, -11.199624648310818, -10.065643855069732, -5.601649148862097, -3.748890392147396, -5.042219007666453, -7.603699923410905, -6.1513214165147305, -5.301379920436609, -1.000000000000001, -3.940398999999996, 0.0, 0.0, 0.0, -1.990000000000004, -3.950200000000001, -1.4228316738469466, -2.756516686140405, -2.4995702301802014, -0.9999999999999966, -1.3529349999999989, -0.9999999999999996, -1.0, 0.0, -1.352935000000005, -1.000000000000005, -2.0000000000000093, 0.0, -1.000000000000008, -1.000000000000008, 0.0, 0.0, 0.0, -2.339405650000033, -8.676234359199988, -2.30425544018472, -5.880593803600196, -0.6387237383329323, -2.0, -6.2067651862600135, -3.522859472645326, -2.6391795207374105, -3.6127877255300342, -1.9900000000000115, -7.135222503533541, -6.864816290637951, -5.986481249055171, -1.3391792304543415, -1.690905056497784, -1.000000000000002, -2.970099999999997, -6.197194448013691, -6.8993459686925664, 0.0, -4.647838479999969, -2.9799999999999978, -4.414458629295984, -7.262907762142961, -1.1560776050199977, -0.999999999999999, -0.999999999999999, -2.000000000000004, -6.812484040000004, -6.541138068370995, -2.798110648009236, -4.815069831925949, -5.87236986993526, -1.9999999999999996, -7.135222503533544, -1.1414275461339956, -2.339405650000007, -3.0605434909334948, 0.0, -3.9403989999999873, -4.659443537325394, -7.0823284563781925, -5.870472954688378, -2.820619800704048, -1.9900000000000115, -2.9800000000000098, -2.0913477403510488, -0.9999999999999974, -2.9799999999999995, -3.760795873001384, -1.8719066437104623, 0.0, -1.990000000000004, -3.96, 0.0, -1.36299999999998, -2.0913477403509986, 0.0, 0.0, -4.282851477564974, -1.999999999999992, -8.657595905114972, -0.999999999999998, -1.9900000000000104, -0.3565000000000031, -0.3565000000000253, 0.0, -1.8490105134039216, -0.9999999999999936, -1.0000000000000036, -4.910698000000011, -4.292617537000017, 0.0, -2.0000000000000004, -1.989999999999999, -1.0000000000000069, 0.0, -1.70461178672502, -3.940399000000009, 0.0, 0.0, -4.142333158077885, 0.0, -9.011088166549603, -7.25017930089228, 0.0, -4.920400000000002, -5.1493129113680425, -1.999999999999996, -4.6381086675999725, -3.517359647869485, -1.0922798601504795, -4.029938056024152, -1.3102716454926626, -1.9900000000000035, -5.851985059899997, -5.577842280927013, -6.2498722330165934, -6.942134707713704, -6.899142003723949, -5.519477926213346, 0.0, -1.0000000000000049, -2.4637332952000324, -3.783335585142627, -0.7161999999999991, -2.5421611578906527, -2.970100000000005, -4.999332621114851, -1.9341541639120277, -0.9999999999999972, 0.0, -1.99, -3.316011593499999, -2.9800000000000004, -3.706233508000014, 0.0, 0.0, -4.282851477564999];

        var Nikes_Massive_Rocket_dates = ['2013-10-06 21:00:00', '2013-10-31 00:00:00', '2013-11-07 00:00:00', '2013-11-15 18:00:00', '2013-11-21 07:00:00', '2013-11-26 08:00:00', '2013-11-30 16:00:00', '2013-12-05 03:00:00', '2013-12-09 11:00:00', '2013-12-14 10:00:00', '2013-12-19 00:00:00', '2013-12-23 10:00:00', '2013-12-29 03:00:00', '2014-01-03 22:00:00', '2014-01-08 14:00:00', '2014-01-14 20:00:00', '2014-01-23 02:00:00', '2014-01-30 03:00:00', '2014-02-07 09:00:00', '2014-02-12 09:00:00', '2014-02-17 22:00:00', '2014-02-23 02:00:00', '2014-02-27 19:00:00', '2014-03-07 09:00:00', '2014-03-15 23:00:00', '2014-03-23 21:00:00', '2014-03-30 10:00:00', '2014-04-04 22:00:00', '2014-04-14 02:00:00', '2014-04-19 13:00:00', '2014-04-29 00:00:00', '2014-05-08 08:00:00', '2014-05-22 08:00:00', '2014-05-28 23:00:00', '2014-06-05 14:00:00', '2014-06-14 23:00:00', '2014-06-25 13:00:00', '2014-07-07 03:00:00', '2014-07-28 02:00:00', '2014-08-14 07:00:00', '2014-09-08 17:00:00', '2014-10-06 05:00:00', '2014-10-27 15:00:00', '2014-11-15 21:00:00', '2014-12-02 10:00:00', '2014-12-18 02:00:00', '2015-01-05 21:00:00', '2015-01-15 20:00:00', '2015-01-28 06:00:00', '2015-02-13 11:00:00', '2015-02-27 22:00:00', '2015-03-11 09:00:00', '2015-03-23 18:00:00', '2015-04-03 16:00:00', '2015-04-12 06:00:00', '2015-04-21 02:00:00', '2015-04-27 15:00:00', '2015-05-04 23:00:00', '2015-05-13 00:00:00', '2015-05-24 11:00:00', '2015-06-06 17:00:00', '2015-06-20 06:00:00', '2015-06-30 22:00:00', '2015-07-09 21:00:00', '2015-07-14 20:00:00', '2015-07-24 15:00:00', '2015-08-05 14:00:00', '2015-08-12 13:00:00', '2015-08-17 17:00:00', '2015-08-23 02:00:00', '2015-08-28 06:00:00', '2015-09-03 13:00:00', '2015-09-09 20:00:00', '2015-09-15 21:00:00', '2015-09-24 07:00:00', '2015-10-02 21:00:00', '2015-10-13 00:00:00', '2015-10-20 02:00:00', '2015-10-26 18:00:00', '2015-10-31 15:00:00', '2015-11-05 11:00:00', '2015-11-10 08:00:00', '2015-11-16 12:00:00', '2015-11-23 18:00:00', '2015-11-29 22:00:00', '2015-12-05 04:00:00', '2015-12-10 02:00:00', '2015-12-14 21:00:00', '2015-12-21 02:00:00', '2015-12-27 09:00:00', '2016-01-03 18:00:00', '2016-01-09 21:00:00', '2016-01-15 02:00:00', '2016-01-19 14:00:00', '2016-01-23 23:00:00', '2016-01-28 12:00:00', '2016-02-01 22:00:00', '2016-02-06 14:00:00', '2016-02-10 22:00:00', '2016-02-15 02:00:00', '2016-02-19 06:00:00', '2016-02-23 10:00:00', '2016-02-27 14:00:00', '2016-03-02 19:00:00', '2016-03-06 23:00:00', '2016-03-11 03:00:00', '2016-03-15 07:00:00', '2016-03-19 11:00:00', '2016-03-23 15:00:00', '2016-03-27 19:00:00', '2016-03-31 23:00:00', '2016-04-05 04:00:00', '2016-04-09 08:00:00', '2016-04-13 12:00:00', '2016-04-17 17:00:00', '2016-04-21 22:00:00', '2016-04-26 04:00:00', '2016-04-30 08:00:00', '2016-05-04 12:00:00', '2016-05-08 17:00:00', '2016-05-12 23:00:00', '2016-05-17 04:00:00', '2016-05-21 08:00:00', '2016-05-25 13:00:00', '2016-05-29 17:00:00', '2016-06-02 21:00:00', '2016-06-07 01:00:00', '2016-06-11 05:00:00', '2016-06-15 09:00:00', '2016-06-19 13:00:00', '2016-06-23 17:00:00', '2016-06-27 21:00:00', '2016-07-02 01:00:00', '2016-07-06 06:00:00', '2016-07-10 10:00:00', '2016-07-14 14:00:00', '2016-07-18 18:00:00', '2016-07-22 22:00:00', '2016-07-27 03:00:00', '2016-07-31 08:00:00', '2016-08-04 12:00:00', '2016-08-08 16:00:00', '2016-08-12 20:00:00', '2016-08-17 00:00:00', '2016-08-21 05:00:00', '2016-08-25 10:00:00', '2016-08-29 14:00:00', '2016-09-02 18:00:00', '2016-09-06 23:00:00', '2016-09-11 03:00:00', '2016-09-15 07:00:00', '2016-09-19 11:00:00', '2016-09-23 15:00:00', '2016-09-27 19:00:00', '2016-10-02 01:00:00', '2016-10-06 07:00:00', '2016-10-10 11:00:00', '2016-10-14 15:00:00', '2016-10-18 20:00:00', '2016-10-23 01:00:00', '2016-10-27 05:00:00', '2016-10-31 09:00:00', '2016-11-04 13:00:00', '2016-11-08 17:00:00', '2016-11-12 21:00:00', '2016-11-17 01:00:00', '2016-11-21 05:00:00', '2016-11-25 09:00:00', '2016-11-29 13:00:00', '2016-12-03 17:00:00', '2016-12-07 21:00:00', '2016-12-12 01:00:00', '2016-12-16 05:00:00', '2016-12-20 10:00:00', '2016-12-24 14:00:00', '2016-12-28 18:00:00', '2017-01-01 22:00:00', '2017-01-06 02:00:00', '2017-01-10 06:00:00', '2017-01-14 10:00:00', '2017-01-18 14:00:00', '2017-01-22 18:00:00', '2017-01-26 22:00:00', '2017-01-31 02:00:00', '2017-02-04 06:00:00', '2017-02-08 10:00:00', '2017-02-12 14:00:00', '2017-02-16 18:00:00', '2017-02-20 22:00:00', '2017-02-25 02:00:00', '2017-03-01 06:00:00', '2017-03-05 10:00:00', '2017-03-09 14:00:00', '2017-03-13 18:00:00', '2017-03-17 22:00:00', '2017-03-22 02:00:00', '2017-03-26 06:00:00', '2017-03-30 10:00:00', '2017-04-03 14:00:00', '2017-04-07 18:00:00', '2017-04-11 22:00:00', '2017-04-16 02:00:00', '2017-04-20 06:00:00', '2017-04-24 10:00:00', '2017-04-28 14:00:00', '2017-05-02 18:00:00', '2017-05-06 22:00:00', '2017-05-11 02:00:00', '2017-05-15 06:00:00', '2017-05-19 10:00:00', '2017-05-23 14:00:00', '2017-05-27 18:00:00', '2017-05-31 22:00:00', '2017-06-05 02:00:00', '2017-06-09 06:00:00', '2017-06-13 10:00:00', '2017-06-17 14:00:00', '2017-06-21 18:00:00', '2017-06-25 22:00:00', '2017-06-30 02:00:00', '2017-07-04 06:00:00', '2017-07-08 10:00:00', '2017-07-12 14:00:00', '2017-07-16 18:00:00', '2017-07-20 22:00:00', '2017-07-25 02:00:00', '2017-07-29 06:00:00', '2017-08-02 10:00:00', '2017-08-06 14:00:00', '2017-08-10 18:00:00', '2017-08-14 22:00:00', '2017-08-19 02:00:00', '2017-08-23 06:00:00', '2017-08-27 10:00:00', '2017-08-31 14:00:00', '2017-09-04 18:00:00', '2017-09-08 22:00:00', '2017-09-13 02:00:00', '2017-09-17 06:00:00', '2017-09-21 10:00:00', '2017-09-25 14:00:00', '2017-09-29 18:00:00', '2017-10-03 22:00:00', '2017-10-08 02:00:00', '2017-10-12 06:00:00', '2017-10-16 10:00:00', '2017-10-20 14:00:00', '2017-10-24 18:00:00', '2017-10-28 22:00:00', '2017-11-02 02:00:00', '2017-11-06 06:00:00', '2017-11-10 10:00:00', '2017-11-14 14:00:00', '2017-11-18 18:00:00', '2017-11-22 22:00:00', '2017-11-27 02:00:00', '2017-12-01 06:00:00', '2017-12-05 10:00:00', '2017-12-09 14:00:00', '2017-12-13 20:00:00', '2017-12-18 00:00:00', '2017-12-22 04:00:00', '2017-12-26 08:00:00', '2017-12-30 12:00:00', '2018-01-03 16:00:00', '2018-01-07 20:00:00', '2018-01-14 02:00:00', '2018-01-18 06:00:00', '2018-01-22 10:00:00', '2018-01-26 14:00:00', '2018-01-30 18:00:00', '2018-02-03 22:00:00', '2018-02-08 02:00:00', '2018-02-12 06:00:00', '2018-02-16 10:00:00', '2018-02-20 14:00:00', '2018-02-24 18:00:00', '2018-02-28 22:00:00', '2018-03-05 02:00:00', '2018-03-09 06:00:00', '2018-03-13 10:00:00', '2018-03-17 14:00:00', '2018-03-21 18:00:00', '2018-03-25 22:00:00', '2018-03-30 02:00:00', '2018-04-03 06:00:00', '2018-04-07 10:00:00', '2018-04-11 14:00:00', '2018-04-15 18:00:00', '2018-04-19 22:00:00', '2018-04-24 03:00:00', '2018-04-28 07:00:00', '2018-05-02 11:00:00', '2018-05-06 15:00:00', '2018-05-10 19:00:00', '2018-05-14 23:00:00', '2018-05-19 03:00:00', '2018-05-23 07:00:00', '2018-05-27 11:00:00', '2018-05-31 15:00:00', '2018-06-04 19:00:00', '2018-06-08 23:00:00', '2018-06-13 03:00:00', '2018-06-17 07:00:00', '2018-06-21 11:00:00', '2018-06-25 15:00:00', '2018-06-29 19:00:00', '2018-07-03 23:00:00', '2018-07-08 03:00:00', '2018-07-12 07:00:00', '2018-07-16 11:00:00', '2018-07-20 15:00:00', '2018-07-24 19:00:00', '2018-07-28 23:00:00', '2018-08-02 03:00:00', '2018-08-06 07:00:00', '2018-08-10 11:00:00', '2018-08-14 15:00:00', '2018-08-18 19:00:00', '2018-08-22 23:00:00', '2018-08-27 03:00:00', '2018-08-31 07:00:00', '2018-09-04 11:00:00', '2018-09-08 15:00:00', '2018-09-12 19:00:00', '2018-09-16 23:00:00', '2018-09-21 03:00:00', '2018-09-25 07:00:00', '2018-09-29 11:00:00', '2018-10-03 15:00:00', '2018-10-07 19:00:00', '2018-10-11 23:00:00', '2018-10-16 03:00:00', '2018-10-20 07:00:00', '2018-10-24 11:00:00', '2018-10-28 15:00:00', '2018-11-01 19:00:00', '2018-11-05 23:00:00', '2018-11-10 03:00:00', '2018-11-14 07:00:00', '2018-11-18 11:00:00', '2018-11-22 15:00:00', '2018-11-26 19:00:00', '2018-11-30 23:00:00', '2018-12-05 03:00:00', '2018-12-09 07:00:00', '2018-12-13 11:00:00', '2018-12-17 15:00:00', '2018-12-21 19:00:00', '2018-12-25 23:00:00', '2018-12-30 03:00:00', '2019-01-03 07:00:00', '2019-01-07 11:00:00', '2019-01-11 15:00:00', '2019-01-15 19:00:00', '2019-01-19 23:00:00', '2019-01-24 03:00:00', '2019-01-28 07:00:00', '2019-02-01 11:00:00', '2019-02-05 15:00:00', '2019-02-09 19:00:00', '2019-02-13 23:00:00', '2019-02-18 03:00:00', '2019-02-22 07:00:00', '2019-02-26 11:00:00', '2019-03-02 15:00:00', '2019-03-06 19:00:00', '2019-03-10 23:00:00', '2019-03-15 03:00:00', '2019-03-19 07:00:00', '2019-03-23 11:00:00', '2019-03-27 15:00:00', '2019-03-31 19:00:00', '2019-04-04 23:00:00', '2019-04-09 03:00:00', '2019-04-13 07:00:00', '2019-04-17 11:00:00', '2019-04-21 15:00:00', '2019-04-25 19:00:00', '2019-04-29 23:00:00', '2019-05-04 03:00:00', '2019-05-08 07:00:00', '2019-05-12 11:00:00', '2019-05-16 15:00:00', '2019-05-20 19:00:00', '2019-05-24 23:00:00', '2019-05-29 03:00:00', '2019-06-02 07:00:00', '2019-06-06 11:00:00', '2019-06-10 15:00:00', '2019-06-14 19:00:00', '2019-06-18 23:00:00', '2019-06-23 03:00:00', '2019-06-27 07:00:00', '2019-07-01 11:00:00', '2019-07-05 15:00:00', '2019-07-09 19:00:00', '2019-07-13 23:00:00', '2019-07-18 06:00:00', '2019-07-22 10:00:00', '2019-07-26 14:00:00', '2019-07-30 18:00:00', '2019-08-03 22:00:00', '2019-08-08 02:00:00', '2019-08-12 06:00:00', '2019-08-16 10:00:00', '2019-08-20 14:00:00', '2019-08-24 18:00:00', '2019-08-28 22:00:00', '2019-09-02 02:00:00', '2019-09-06 06:00:00', '2019-09-10 10:00:00', '2019-09-14 14:00:00', '2019-09-18 18:00:00', '2019-09-22 22:00:00', '2019-09-27 02:00:00', '2019-10-01 06:00:00', '2019-10-05 10:00:00', '2019-10-09 14:00:00', '2019-10-13 18:00:00', '2019-10-17 22:00:00', '2019-10-22 02:00:00', '2019-10-26 06:00:00', '2019-10-30 10:00:00', '2019-11-03 14:00:00', '2019-11-07 18:00:00', '2019-11-11 22:00:00', '2019-11-16 02:00:00', '2019-11-20 06:00:00', '2019-11-24 10:00:00', '2019-11-28 14:00:00', '2019-12-02 18:00:00', '2019-12-06 22:00:00', '2019-12-11 02:00:00', '2019-12-15 06:00:00', '2019-12-19 10:00:00', '2019-12-23 14:00:00', '2019-12-27 18:00:00', '2019-12-31 22:00:00', '2020-01-05 02:00:00', '2020-01-09 06:00:00', '2020-01-13 10:00:00', '2020-01-17 14:00:00', '2020-01-21 18:00:00', '2020-01-25 22:00:00', '2020-01-30 02:00:00', '2020-02-03 06:00:00', '2020-02-07 10:00:00', '2020-02-11 14:00:00', '2020-02-15 18:00:00', '2020-02-19 22:00:00', '2020-02-24 02:00:00', '2020-02-28 06:00:00', '2020-03-03 10:00:00', '2020-03-07 14:00:00', '2020-03-11 18:00:00', '2020-03-15 22:00:00', '2020-03-20 02:00:00', '2020-03-24 06:00:00', '2020-03-28 10:00:00', '2020-04-01 14:00:00', '2020-04-05 18:00:00', '2020-04-09 22:00:00', '2020-04-14 02:00:00', '2020-04-18 06:00:00', '2020-04-22 10:00:00', '2020-04-26 14:00:00', '2020-04-30 18:00:00', '2020-05-04 22:00:00', '2020-05-09 02:00:00', '2020-05-13 06:00:00', '2020-05-17 10:00:00', '2020-05-21 14:00:00', '2020-05-25 18:00:00', '2020-05-29 22:00:00', '2020-06-03 02:00:00', '2020-06-07 06:00:00', '2020-06-11 10:00:00', '2020-06-15 14:00:00', '2020-06-19 18:00:00', '2020-06-23 22:00:00', '2020-06-28 02:00:00', '2020-07-02 06:00:00', '2020-07-06 10:00:00', '2020-07-10 14:00:00', '2020-07-14 18:00:00', '2020-07-18 22:00:00', '2020-07-23 02:00:00', '2020-07-27 06:00:00', '2020-07-31 10:00:00', '2020-08-04 14:00:00', '2020-08-08 18:00:00', '2020-08-12 22:00:00', '2020-08-17 02:00:00', '2020-08-21 06:00:00', '2020-08-25 10:00:00', '2020-08-29 14:00:00', '2020-09-02 18:00:00', '2020-09-06 22:00:00', '2020-09-11 02:00:00', '2020-09-15 06:00:00', '2020-09-19 10:00:00', '2020-09-23 14:00:00', '2020-09-27 18:00:00', '2020-10-01 22:00:00', '2020-10-06 02:00:00', '2020-10-10 06:00:00', '2020-10-14 10:00:00', '2020-10-18 14:00:00', '2020-10-22 18:00:00', '2020-10-26 23:00:00', '2020-10-31 03:00:00', '2020-11-04 07:00:00', '2020-11-08 11:00:00', '2020-11-12 15:00:00', '2020-11-16 19:00:00', '2020-11-20 23:00:00', '2020-11-25 03:00:00', '2020-11-29 07:00:00', '2020-12-03 11:00:00', '2020-12-07 15:00:00', '2020-12-11 19:00:00', '2020-12-15 23:00:00', '2020-12-20 03:00:00', '2020-12-24 07:00:00', '2020-12-28 11:00:00', '2021-01-01 15:00:00', '2021-01-05 19:00:00', '2021-01-09 23:00:00', '2021-01-14 03:00:00', '2021-01-18 07:00:00', '2021-01-22 11:00:00', '2021-01-26 15:00:00', '2021-01-30 20:00:00', '2021-02-04 00:00:00', '2021-02-08 04:00:00', '2021-02-12 08:00:00', '2021-02-16 12:00:00', '2021-02-20 16:00:00', '2021-02-24 20:00:00', '2021-03-01 00:00:00', '2021-03-05 04:00:00', '2021-03-09 08:00:00', '2021-03-13 12:00:00', '2021-03-17 16:00:00', '2021-03-21 20:00:00', '2021-03-26 00:00:00', '2021-03-30 04:00:00', '2021-04-03 08:00:00', '2021-04-07 12:00:00', '2021-04-11 16:00:00', '2021-04-15 20:00:00', '2021-04-20 00:00:00', '2021-04-24 04:00:00', '2021-04-28 08:00:00', '2021-05-02 12:00:00', '2021-05-06 16:00:00', '2021-05-10 20:00:00', '2021-05-15 00:00:00', '2021-05-19 04:00:00', '2021-05-23 08:00:00', '2021-05-27 12:00:00', '2021-05-31 16:00:00', '2021-06-04 20:00:00', '2021-06-09 00:00:00', '2021-06-13 04:00:00', '2021-06-17 08:00:00', '2021-06-21 12:00:00', '2021-06-25 16:00:00', '2021-06-29 20:00:00', '2021-07-04 00:00:00', '2021-07-08 04:00:00', '2021-07-12 08:00:00', '2021-07-16 12:00:00', '2021-07-20 16:00:00', '2021-07-25 01:00:00', '2021-07-29 05:00:00', '2021-08-02 09:00:00', '2021-08-06 13:00:00', '2021-08-10 17:00:00', '2021-08-14 21:00:00', '2021-08-19 01:00:00', '2021-08-23 05:00:00', '2021-08-27 09:00:00', '2021-08-31 13:00:00', '2021-09-04 17:00:00', '2021-09-08 21:00:00', '2021-09-13 01:00:00', '2021-09-17 05:00:00', '2021-09-21 09:00:00', '2021-09-25 13:00:00', '2021-09-29 17:00:00', '2021-10-03 21:00:00', '2021-10-08 01:00:00', '2021-10-12 05:00:00', '2021-10-16 09:00:00', '2021-10-20 13:00:00', '2021-10-24 17:00:00', '2021-10-28 21:00:00', '2021-11-02 01:00:00', '2021-11-06 05:00:00', '2021-11-10 09:00:00', '2021-11-14 13:00:00', '2021-11-18 17:00:00', '2021-11-22 21:00:00', '2021-11-27 01:00:00', '2021-12-01 05:00:00', '2021-12-05 09:00:00', '2021-12-09 13:00:00', '2021-12-13 17:00:00', '2021-12-17 21:00:00', '2021-12-22 01:00:00', '2021-12-26 05:00:00', '2021-12-30 09:00:00', '2022-01-03 13:00:00', '2022-01-07 17:00:00', '2022-01-11 21:00:00', '2022-01-16 01:00:00', '2022-01-20 05:00:00', '2022-01-24 09:00:00', '2022-01-28 13:00:00', '2022-02-01 17:00:00', '2022-02-05 21:00:00', '2022-02-10 01:00:00', '2022-02-14 05:00:00', '2022-02-18 09:00:00', '2022-02-22 13:00:00', '2022-02-26 17:00:00', '2022-03-02 21:00:00', '2022-03-07 01:00:00', '2022-03-11 05:00:00', '2022-03-15 09:00:00', '2022-03-19 13:00:00', '2022-03-23 17:00:00', '2022-03-27 21:00:00', '2022-04-01 01:00:00', '2022-04-05 05:00:00', '2022-04-09 09:00:00', '2022-04-13 13:00:00', '2022-04-17 17:00:00', '2022-04-21 21:00:00', '2022-04-26 01:00:00', '2022-04-30 05:00:00', '2022-05-04 09:00:00', '2022-05-08 13:00:00', '2022-05-12 17:00:00', '2022-05-16 21:00:00', '2022-05-21 01:00:00', '2022-05-25 05:00:00', '2022-05-29 09:00:00', '2022-06-02 13:00:00', '2022-06-06 17:00:00', '2022-06-10 21:00:00', '2022-06-15 01:00:00', '2022-06-19 05:00:00', '2022-06-23 09:00:00', '2022-06-27 13:00:00', '2022-07-01 17:00:00', '2022-07-05 21:00:00', '2022-07-10 01:00:00', '2022-07-14 05:00:00', '2022-07-18 09:00:00', '2022-07-22 13:00:00', '2022-07-26 17:00:00', '2022-07-30 21:00:00', '2022-08-04 01:00:00', '2022-08-08 05:00:00', '2022-08-12 09:00:00', '2022-08-16 13:00:00', '2022-08-20 17:00:00', '2022-08-24 21:00:00', '2022-08-29 01:00:00', '2022-09-02 05:00:00', '2022-09-06 09:00:00', '2022-09-10 13:00:00', '2022-09-14 17:00:00', '2022-09-18 21:00:00', '2022-09-23 01:00:00', '2022-09-27 05:00:00', '2022-10-01 09:00:00', '2022-10-05 13:00:00', '2022-10-09 17:00:00', '2022-10-13 21:00:00', '2022-10-18 01:00:00', '2022-10-22 05:00:00', '2022-10-26 09:00:00', '2022-10-30 13:00:00', '2022-11-03 17:00:00', '2022-11-07 21:00:00', '2022-11-12 01:00:00', '2022-11-16 05:00:00', '2022-11-20 09:00:00', '2022-11-24 13:00:00', '2022-11-28 17:00:00', '2022-12-02 21:00:00', '2022-12-07 01:00:00', '2022-12-11 05:00:00', '2022-12-15 09:00:00', '2022-12-19 13:00:00', '2022-12-23 17:00:00', '2022-12-27 21:00:00', '2023-01-01 01:00:00', '2023-01-05 05:00:00', '2023-01-09 09:00:00', '2023-01-13 13:00:00', '2023-01-17 17:00:00', '2023-01-21 21:00:00', '2023-01-26 01:00:00', '2023-01-30 05:00:00', '2023-02-03 09:00:00', '2023-02-07 13:00:00', '2023-02-11 17:00:00', '2023-02-15 21:00:00', '2023-02-20 01:00:00', '2023-02-24 05:00:00', '2023-02-28 09:00:00', '2023-03-04 13:00:00', '2023-03-08 17:00:00', '2023-03-12 21:00:00', '2023-03-17 01:00:00', '2023-03-21 05:00:00', '2023-03-25 09:00:00', '2023-03-29 13:00:00', '2023-04-02 17:00:00', '2023-04-06 21:00:00', '2023-04-11 01:00:00', '2023-04-15 05:00:00', '2023-04-19 09:00:00', '2023-04-23 13:00:00', '2023-04-27 17:00:00', '2023-05-01 21:00:00', '2023-05-06 01:00:00', '2023-05-10 06:00:00', '2023-05-14 10:00:00', '2023-05-18 14:00:00', '2023-05-22 18:00:00', '2023-05-26 22:00:00', '2023-05-31 02:00:00', '2023-06-04 06:00:00', '2023-06-08 10:00:00', '2023-06-12 14:00:00', '2023-06-16 18:00:00', '2023-06-20 22:00:00', '2023-06-25 02:00:00', '2023-06-29 06:00:00', '2023-07-03 10:00:00', '2023-07-07 14:00:00', '2023-07-11 18:00:00', '2023-07-15 22:00:00', '2023-07-20 02:00:00', '2023-07-24 06:00:00', '2023-07-28 10:00:00', '2023-08-01 14:00:00', '2023-08-05 18:00:00', '2023-08-09 22:00:00', '2023-08-14 02:00:00', '2023-08-18 06:00:00', '2023-08-22 10:00:00', '2023-08-26 14:00:00', '2023-08-30 18:00:00', '2023-09-03 22:00:00', '2023-09-08 02:00:00', '2023-09-12 06:00:00', '2023-09-16 10:00:00', '2023-09-20 14:00:00', '2023-09-24 18:00:00', '2023-09-28 22:00:00', '2023-10-03 02:00:00', '2023-10-07 06:00:00', '2023-10-11 10:00:00', '2023-10-15 14:00:00', '2023-10-19 18:00:00', '2023-10-23 22:00:00', '2023-10-28 02:00:00', '2023-11-01 06:00:00', '2023-11-05 10:00:00', '2023-11-09 14:00:00', '2023-11-13 18:00:00', '2023-11-17 22:00:00', '2023-11-22 02:00:00', '2023-11-26 06:00:00', '2023-11-30 10:00:00', '2023-12-04 14:00:00', '2023-12-08 18:00:00', '2023-12-12 22:00:00', '2023-12-17 02:00:00', '2023-12-21 06:00:00', '2023-12-25 10:00:00', '2023-12-29 14:00:00', '2024-01-02 18:00:00', '2024-01-06 22:00:00', '2024-01-11 04:00:00', '2024-01-15 08:00:00', '2024-01-19 12:00:00', '2024-01-23 20:00:00', '2024-01-28 00:00:00', '2024-02-01 04:00:00', '2024-02-05 08:00:00', '2024-02-09 12:00:00', '2024-02-13 16:00:00', '2024-02-17 20:00:00', '2024-02-22 00:00:00', '2024-02-26 04:00:00', '2024-03-01 08:00:00', '2024-03-05 12:00:00', '2024-03-09 16:00:00', '2024-03-13 20:00:00', '2024-03-18 00:00:00', '2024-03-22 04:00:00', '2024-03-26 08:00:00', '2024-03-30 12:00:00', '2024-04-03 17:00:00', '2024-04-07 21:00:00', '2024-04-12 01:00:00', '2024-04-16 10:00:00', '2024-04-20 14:00:00', '2024-04-24 18:00:00', '2024-04-28 22:00:00', '2024-05-03 02:00:00', '2024-05-07 06:00:00', '2024-05-11 10:00:00', '2024-05-15 14:00:00', '2024-05-19 18:00:00', '2024-05-23 22:00:00', '2024-05-28 02:00:00', '2024-06-01 06:00:00', '2024-06-05 10:00:00', '2024-06-09 14:00:00', '2024-06-13 18:00:00', '2024-06-17 22:00:00', '2024-06-22 02:00:00', '2024-06-26 06:00:00', '2024-06-30 10:00:00', '2024-07-04 14:00:00', '2024-07-08 18:00:00', '2024-07-12 22:00:00', '2024-07-17 02:00:00', '2024-07-21 06:00:00', '2024-07-25 10:00:00', '2024-07-29 14:00:00', '2024-08-02 18:00:00', '2024-08-06 22:00:00', '2024-08-11 02:00:00', '2024-08-15 06:00:00', '2024-08-19 10:00:00', '2024-08-23 14:00:00', '2024-08-27 18:00:00', '2024-08-31 22:00:00', '2024-09-05 02:00:00', '2024-09-09 06:00:00', '2024-09-13 10:00:00', '2024-09-17 14:00:00', '2024-09-21 18:00:00', '2024-09-25 22:00:00', '2024-09-30 02:00:00', '2024-10-04 06:00:00', '2024-10-08 10:00:00', '2024-10-12 14:00:00', '2024-10-16 18:00:00', '2024-10-20 22:00:00', '2024-10-25 02:00:00', '2024-10-29 06:00:00', '2024-11-02 10:00:00', '2024-11-06 14:00:00', '2024-11-10 18:00:00', '2024-11-14 22:00:00', '2024-11-19 02:00:00', '2024-11-23 06:00:00', '2024-11-27 10:00:00', '2024-12-01 14:00:00', '2024-12-05 18:00:00', '2024-12-09 22:00:00', '2024-12-14 02:00:00', '2024-12-18 06:00:00', '2024-12-22 10:00:00', '2024-12-26 14:00:00', '2024-12-30 18:00:00', '2025-01-03 22:00:00', '2025-01-08 02:00:00', '2025-01-12 06:00:00', '2025-01-16 10:00:00', '2025-01-20 14:00:00', '2025-01-24 18:00:00', '2025-01-29 01:00:00', '2025-02-02 05:00:00', '2025-02-06 09:00:00', '2025-02-10 13:00:00', '2025-02-14 17:00:00', '2025-02-18 21:00:00', '2025-02-23 01:00:00', '2025-02-27 05:00:00', '2025-03-03 09:00:00', '2025-03-07 13:00:00', '2025-03-11 17:00:00', '2025-03-15 21:00:00', '2025-03-20 01:00:00', '2025-03-24 05:00:00', '2025-03-28 09:00:00'];
        var Nikes_Massive_Rocket_equity = [10000.0, 10000.0, 10000.0, 10335.453183999998, 14055.270058231086, 13452.122427177914, 20175.53171588647, 18229.48708486908, 17157.447408382097, 18758.80916649776, 21658.75739109842, 19977.348202713856, 20076.913352815467, 22945.859655569788, 23064.95030764404, 22534.57524392745, 22083.883739048902, 20061.57084961228, 20536.529760078567, 20796.659137039565, 21825.053835239392, 22077.87801224575, 25685.72196474166, 24471.21883593839, 22391.80944165266, 25097.66147933107, 25811.96590900749, 25621.39419760646, 28123.04216612916, 28278.5430143216, 29354.56545567508, 35811.488378647075, 37660.703689406175, 33342.67036479177, 42204.28589088524, 39722.33624621406, 45242.26978679908, 47673.29369544723, 47497.9317447588, 52189.89142628349, 56018.02800351059, 62240.48274928114, 64795.7281388754, 69809.61925076728, 62574.67342309512, 58835.64997562659, 65870.82476204552, 64597.7551725315, 66244.5959878433, 68101.60746440319, 64069.99230251052, 80098.39490269532, 82284.90200987205, 86706.3548912024, 86706.3548912024, 106942.69361905144, 111914.17190711504, 108843.81406178814, 100394.06850355366, 100782.33409242232, 89909.12479626789, 100230.18931106477, 104111.69916764996, 115158.16329992255, 121722.43068507349, 116902.22242994457, 115135.154642574, 102749.66867416956, 102749.66867416956, 106174.6576299752, 110145.88050795453, 104981.55490689196, 97212.97955692312, 100453.41220882058, 129707.50630473506, 127113.35617864036, 134699.08769476766, 141507.20691383007, 144250.1322587233, 168826.04880738235, 190011.8964952063, 190011.8964952063, 171684.16981067156, 161587.7671524456, 193050.38292769177, 181033.63622041568, 209416.86754449672, 267021.0877381588, 265817.3139512611, 256516.3448707214, 262782.1841214302, 311833.93262542784, 285324.6851359897, 313987.50064780354, 304313.26604690147, 299276.7597685189, 324726.8297811756, 325089.59808828565, 316170.7866373734, 330848.1371548283, 351656.8086846388, 378754.88245099055, 432878.1134757208, 436668.84286064765, 453001.45308589, 443941.4240241722, 441534.6762667025, 467397.2265131654, 450656.07830601657, 474673.61013165646, 411561.61451077234, 426777.3217736453, 401512.1043246455, 440531.80011217657, 435009.1524957944, 533763.2207665625, 571893.2299946311, 567554.4666897384, 523276.1374164718, 586090.1119250756, 558610.5534015435, 521553.7057052887, 526779.2821531649, 519301.4643420853, 587115.0038314362, 552357.7956046151, 665304.8980481112, 676966.0074718194, 753122.4472184591, 863771.783059587, 780456.088359762, 800352.8624417194, 784345.805192885, 762827.3303171385, 732619.3680365798, 705790.2606835855, 658695.4174331083, 661978.2909878212, 651293.5447662738, 699730.4595878074, 736034.6806767182, 721313.9870631839, 721313.9870631839, 815507.5488084829, 751884.9118806404, 802846.0003525508, 790381.0347603039, 774144.0045567313, 830581.510936932, 833852.4516712027, 889442.6151159493, 889442.6151159493, 909779.0320921025, 876827.388695377, 916576.8969828996, 909623.1335911226, 931454.0887973089, 828046.6013367657, 851735.358507808, 989800.348828021, 1192693.2295197477, 1433159.3344027428, 1568041.9213596862, 1593493.2070225624, 1506354.369530827, 1576282.6868186025, 1627084.2288994782, 1633491.9035374487, 1568805.6241573656, 1826737.991766117, 1871359.111778324, 1871359.111778324, 2014252.8545221952, 2407793.1011212883, 3371751.79536725, 3717182.168435859, 4334937.130627313, 4794434.590891791, 4954249.07725485, 4843474.84009321, 4768275.178885251, 4884748.247254822, 4712729.620703339, 4712729.620703339, 6317783.803029401, 6548441.600146407, 6417472.768143479, 7484199.352714883, 7770794.293261509, 10629958.59497497, 11165186.93152665, 12556496.65519504, 12348681.6129529, 12760304.33338466, 12505098.246716969, 13507729.23503152, 14612356.661025446, 13643008.924979832, 16885735.34050818, 18239596.07625382, 20931079.51218871, 19691959.60506714, 20164566.635588758, 20827448.017127544, 26077156.54779696, 28728164.454791028, 31661861.19737549, 33349267.171850253, 33735589.2900058, 34491657.77255921, 35478395.11811658, 45619945.21905329, 48945781.15424074, 49180720.903781086, 56478337.60626663, 55770307.09296643, 54654900.9511071, 55117054.864945374, 57178583.61514232, 60527774.67816757, 60342803.67538112, 59135947.601873495, 59115316.00537732, 71893643.78943484, 72268832.75182396, 71163408.68605207, 71411872.39329907, 80310606.74100772, 89913613.69105248, 90917302.12604426, 121881776.59581682, 117880777.62212226, 110948444.8517205, 105658423.00119048, 119790722.63675237, 126742707.46652608, 129973857.88563162, 123879873.08427392, 136685381.44642976, 126021734.72750507, 126018149.8225034, 133999862.97644404, 133621570.28593951, 150272343.01108986, 188676869.3561052, 207276886.7063893, 199068721.99281636, 212422402.2207249, 223118068.43344724, 283687931.0644416, 305421180.24816203, 333917657.9389862, 322292828.80892897, 362940112.0623085, 430379740.2664723, 516815088.0224856, 510336128.6982077, 572496792.9756842, 616355604.3531587, 639408991.4006436, 679624577.2875209, 634539957.4252251, 627388917.2903256, 597973013.2869856, 558537584.0564446, 577273905.1519849, 550207705.3680859, 559953918.6726853, 537779743.4932469, 599636364.6559368, 645574330.6588943, 648791810.0000747, 769717522.4630542, 754323172.0137931, 812450028.8073697, 984894151.4646974, 872460980.0325166, 934812858.0721736, 958868708.953231, 1029201876.4917588, 1062814370.0546432, 1232011041.9539957, 1168876966.683843, 1226167024.3567345, 1539533827.8996985, 1483772848.6897388, 1622258314.567448, 1558016885.3105772, 1704252396.3292916, 1670167348.4027057, 2184522919.209349, 2370291641.38187, 2378635067.959534, 2517726384.700802, 2498625233.862205, 2715236230.545725, 2618526901.456233, 2997969336.6668315, 3586503129.041404, 3375588053.028737, 3461644544.7219734, 3405771423.241861, 3818573160.687566, 4023758326.999936, 3833502157.2727337, 4832861378.457407, 5572632145.29017, 5530354442.7479, 5446758844.773893, 5838831955.503224, 5068839104.909226, 5133044400.238075, 6825242555.158927, 6372472781.44734, 6197644415.519852, 7029535590.980569, 8136334278.137474, 8072899394.022168, 7691186877.394739, 7847926620.770578, 7537148726.588063, 7330367553.750701, 8108134537.73287, 8687595886.029512, 8670834776.949953, 8466161088.342869, 9114751205.913555, 9660947608.177256, 8732731459.82664, 9059359524.702284, 8700608887.524075, 9149886476.816608, 9182094077.215, 9488163879.788834, 9997995799.134064, 9922144337.671291, 10421733565.741076, 11440045497.623938, 12183170601.739746, 14555345317.200642, 15234594765.336666, 17433793156.3506, 18288274033.168785, 18492422530.70467, 20323894962.928387, 21110957215.978527, 21378362674.047592, 22014856560.53626, 21143068240.739025, 20720206875.924244, 20982662829.68595, 19999873164.82573, 19785173199.94321, 21647265767.41876, 18912018279.59933, 18664485351.44382, 19598406426.469143, 25562418902.17113, 25767834410.481956, 24539224065.79017, 24353053152.544373, 23684928762.84121, 23984937860.503872, 25072255043.513386, 26540842882.044365, 25296442694.425697, 29906076158.7765, 30023850274.166588, 28258207687.2434, 32191654617.14796, 36818093092.4582, 37756725375.9589, 45324824907.17396, 59971803832.46849, 70276164497.79024, 68035593168.05241, 69741018703.4649, 74085912308.10696, 74455149251.09468, 71506725340.75133, 94055934435.98148, 98357972007.23808, 121581021078.60236, 127092694034.16566, 129736767802.71536, 136269135149.232, 153249824673.8889, 168438500275.7565, 149147817189.60864, 152950158497.08115, 155630987301.80008, 194719588862.71628, 238349792690.7046, 307605202422.6024, 308816592723.7696, 303705554587.5541, 344327568209.38794, 334880964330.7346, 336549978313.0424, 337875357000.9696, 343359497515.54285, 345070767678.23175, 393432222761.6892, 413748074546.02094, 397363650793.99854, 384755381393.2968, 362128686924.3198, 338246839603.0613, 326471296678.9853, 328098397699.9954, 371243156346.297, 394053111506.3577, 360404178647.405, 379586553167.4582, 421750839083.405, 560605653799.7985, 553808379314.0923, 506517645075.1759, 476716793988.6596, 463444998444.0152, 452128599955.0335, 459658658472.5657, 487089663970.8068, 441472329396.8404, 429181739746.4324, 405881343611.4024, 421501023759.8455, 417856069898.4137, 464271314737.5478, 530994280643.764, 595160240970.437, 625387584217.2677, 572222894578.5956, 631373787125.5902, 685064916028.92, 794125074691.5686, 778242573197.7372, 813522903182.7014, 735053998360.6396, 788149333504.0897, 845762638225.279, 971033183356.3716, 1129714021990.5005, 1116016378654.6333, 1028949244857.5134, 1221016145248.3206, 1130689864162.264, 1167130528768.3015, 1302610295230.8257, 1353055123630.1436, 1304591643163.8215, 1252929814094.5342, 1327700785957.7532, 1393355583686.4358, 1311415128521.004, 1499535466148.1775, 1621452142614.0725, 1589023099761.7908, 1596277519886.5698, 1704469662901.104, 1657707651330.7231, 1612228552430.308, 1813130630965.003, 1693557367465.8513, 1701997891793.025, 1860972059321.568, 1908415267452.5652, 2414901239234.882, 2414901239234.882, 2597369621031.099, 2525058850781.5933, 2577590917413.2217, 2703926140473.245, 2738175871585.9067, 2786679092914.1694, 2757730146584.2007, 2502648950411.1035, 2492404677939.0347, 2454730047569.052, 2339636998515.411, 2785562336346.83, 2989185488561.279, 2646844338223.6484, 3146229271319.855, 3226438541200.824, 3280173852395.2, 3100076122208.8457, 3310331001824.759, 4055707617679.188, 4055707617679.188, 3842518896119.392, 3874553576534.3735, 3907316239508.976, 3921069992672.05, 3991458831439.7207, 3756729120480.4136, 4120561070908.523, 4296989473140.5713, 4956103945736.064, 4981968200194.212, 4784682259466.521, 4968812936279.17, 4417078661028.41, 4553325607741.811, 4627231789190.217, 4408441463055.575, 5167244722935.866, 5260004188172.54, 5593161314171.088, 8030587667045.479, 8130589016106.746, 9883945402120.314, 10008857316619.342, 11877142084662.62, 10641238225022.002, 12275215398025.713, 13743003820552.305, 11020211775896.08, 10583510314851.469, 12174503657553.51, 12174503657553.51, 11692393312714.39, 11654528666210.5, 13336924626559.473, 12853869325435.664, 18169395561127.445, 19616169236555.47, 22014653860673.723, 26442143984969.64, 26546276673601.645, 24100869045608.047, 25219684944191.94, 25539134286818.367, 25964480473027.285, 27405953863148.484, 25990713647012.47, 26174010306425.805, 26813354131510.77, 34188893521593.53, 30904051873749.18, 29684617646331.51, 27715413254571.76, 28165269148344.73, 32213053780886.074, 27382851204578.547, 25555692121961.355, 24357481944062.0, 24952454036348.96, 25050720127337.977, 23117277185476.293, 25775544528520.227, 30165824064161.71, 26832980593624.727, 27806076452382.74, 26704955824868.38, 25844451304600.656, 27829479660525.617, 27291625999579.387, 29738737676988.4, 34963944030308.137, 41321763467801.92, 41671174305275.18, 43966318930352.7, 45097718870827.12, 49955446266875.1, 44215797284081.84, 44436164494423.31, 49296929223359.945, 53489069973326.25, 49316066690287.23, 50726900726162.94, 48348507354803.37, 46081627893183.0, 57411536200730.766, 58137095135892.95, 57432190019860.375, 72424262734364.56, 76048179710224.88, 73400679424556.3, 78291830843712.98, 77365650861204.52, 73102690513047.89, 97560868141546.84, 87481799285670.7, 89618688036222.06, 94600212684980.44, 93073305717177.66, 93537173972371.06, 98264839487531.16, 98754581839419.4, 100399308096947.05, 105498446343551.6, 110395918663811.16, 122545034088268.64, 141699129761187.5, 160718977902783.03, 165898741651633.56, 148017710638887.22, 142656599154314.8, 125308079649439.05, 117880713718650.02, 132168626026070.56, 131165906716619.4, 144075823006693.94, 139002127508299.66, 138667467058589.28, 142054651053940.44, 153604115087295.6, 154209028573058.03, 168597075069654.2, 164480272230114.0, 156703118622203.03, 158688024791417.6, 185347522764110.38, 188630712866164.0, 201213456273429.2, 193925627224987.0, 199556565506793.1, 234185516897642.4, 237151866778345.88, 208957935872243.72, 197197448908656.47, 206351792703549.5, 239246122367337.84, 229771975921591.28, 230222982080190.4, 249875421572801.7, 253907475024693.72, 321072406203678.56, 329334669456653.2, 353041110664120.6, 375245607783266.4, 394021897341787.25, 369562966144456.2, 445358961748957.6, 439959039205975.75, 495227569749185.8, 535491038510742.06, 603137057430277.8, 617869685286441.4, 660278640354576.2, 715661935663454.5, 754902469523806.1, 828241739232312.6, 1002901985438319.4, 1062309441713528.0, 1048991990613185.6, 928054576947434.6, 932679910215826.0, 975976984270067.2, 1000858324623274.0, 941999848268828.4, 965009764562541.8, 1076578907243506.0, 1137946261548684.8, 1054204253588539.6, 952520265449764.4, 1068540451183331.2, 1060433790960353.6, 1146650200191478.0, 1215078733770138.0, 1221134567371549.8, 1242765376702474.8, 1242765376702474.8, 1223470234604202.5, 1105920189324514.0, 1097529941488172.0, 1156985904585832.5, 1027658261209341.4, 1115846616716851.6, 1010923184422503.0, 970486257045602.8, 991385315144684.0, 1062236319000356.4, 1025068585219628.4, 1086241504469472.0, 1257188199608265.8, 1228566272325350.0, 1212485989283861.0, 1232679559104269.2, 1295073107214106.5, 1285247819240709.2, 1499956940560121.2, 1518956395140549.5, 1677013765242173.2, 2072485060921831.2, 2064861609826180.0, 1942621802524470.0, 1952303639643008.0, 2260221242720782.5, 2260221242720782.5, 2260221242720782.5, 2181135920620283.2, 1970752972224428.0, 1854856931433853.5, 1878351785898682.8, 1864101357016332.0, 1911624342767359.5, 1843537483296196.8, 1997338734162998.0, 2072044671090286.5, 2193413076109100.5, 2064422839929276.8, 2117052777049804.8, 2435908308740759.0, 2866889656323790.5, 3582374253632237.0, 3716364858462707.0, 4077528132622517.0, 3856863763455133.5, 3676029943964413.0, 3846679422918673.0, 4212111918433183.0, 4681941215969104.0, 4388567742068060.0, 4130484850292521.5, 4068999664419959.0, 3723090807643561.0, 3741646328193309.5, 4365693187649205.0, 4795932610578745.0, 4585249032571807.0, 5015620642628229.0, 5053819609442488.0, 4775346768304751.0, 5621028587245438.0, 6321472903889155.0, 6434952422958602.0, 6516461820316080.0, 6548939228863378.0, 6556171649296082.0, 7167877094837147.0, 7350613360631349.0, 7879731305487037.0, 8174454829265510.0, 8514814888372738.0, 8557251893216710.0, 9938806871844474.0, 9788574095229310.0, 1.029643616301427e+16, 9888415989955908.0, 1.0600212147607998e+16, 1.1996936040729264e+16, 1.2143491846031498e+16, 1.0747955225954094e+16, 1.1423947153360128e+16, 1.0489582250146458e+16, 1.074580777924337e+16, 1.1803456694056132e+16, 1.2257693433706996e+16, 1.1923635896889756e+16, 1.127366485409911e+16, 1.1606602208213638e+16, 1.179499190899438e+16, 1.3504166808343012e+16, 1.2710013766677976e+16, 1.438137357885211e+16, 1.438137357885211e+16, 1.438137357885211e+16, 1.3811871185129566e+16, 1.4050676056140002e+16, 1.4779669403508718e+16, 1.4374190158734744e+16, 1.4430797636138528e+16, 1.5785272729845688e+16, 1.5665515127401924e+16, 1.7950045016598406e+16, 1.939321737827737e+16, 2.405648209905679e+16, 2.3396494116434644e+16, 2.3692849708576136e+16, 2.426078794210765e+16, 3.4161077724108416e+16, 3.347785616962625e+16, 3.347785616962625e+16, 3.692298511802667e+16, 3.783168141144331e+16, 3.783168141144331e+16, 3.773178399473604e+16, 3.351413304057265e+16, 3.8167787961388216e+16, 3.538020939670175e+16, 3.924206629330914e+16, 4.32314210314175e+16, 4.15182756662124e+16, 4.377652832758296e+16, 4.450561253069218e+16, 4.361550028007834e+16, 4.581996603250201e+16, 4.108627924380457e+16, 4.12910492422306e+16, 4.202246643170141e+16, 4.61254991148721e+16, 4.577556032825393e+16, 5.075912088121098e+16, 4.874905969431503e+16, 4.551516719187726e+16, 4.476187296878479e+16, 5.608970302584314e+16, 5.565239997389424e+16, 5.770990630252907e+16, 5.744437033543259e+16, 5.404366361157498e+16, 6.1055593297279416e+16, 7.847572394299549e+16, 7.847572394299549e+16, 8.137702377431728e+16, 7.352774760978934e+16, 7.389420271449677e+16, 7.98188756788058e+16, 7.639195360269246e+16, 7.44830826934835e+16, 8.906998614358653e+16, 8.386450264322954e+16, 9.489263856495e+16, 9.69071740856078e+16, 9.534303130895123e+16, 1.044005684807679e+17, 1.1017085228790776e+17, 1.0834748061420195e+17, 1.0279525097294904e+17, 1.0541589025786435e+17, 1.1204601564790674e+17, 1.2401140161193971e+17, 1.2800812789091213e+17, 1.3014006268520328e+17, 1.471001799189997e+17, 1.4874223103127302e+17, 1.4628559743354506e+17, 1.519151595659287e+17, 1.6081347188126768e+17, 1.7674028947326778e+17, 1.745359678421171e+17, 1.9257781548090778e+17, 2.175639472665677e+17, 2.140523282374411e+17, 2.644436764487901e+17, 2.666483158772013e+17, 2.4408199162303725e+17, 2.945091075514259e+17, 2.556708760390972e+17, 3.156340778287341e+17, 3.247990140845615e+17, 3.5688344013647904e+17, 3.71743008305401e+17, 4.117855257752279e+17, 4.2879381180576186e+17, 4.5965960345311546e+17, 5.028032674527685e+17, 5.012667162565687e+17, 5.076160946624852e+17, 5.594112941012389e+17, 5.4575159091199104e+17, 5.851729989842812e+17, 6.637190112004521e+17, 6.945303226315131e+17, 6.703513188134218e+17, 6.602184385561069e+17, 8.058929472446881e+17, 8.596191437276675e+17, 7.863563193388576e+17, 1.127601823521096e+18, 1.0183849132113056e+18, 1.0564752970769928e+18, 1.3807565549007365e+18, 1.4917685159513738e+18, 1.477636743137388e+18, 1.6278535308666135e+18, 1.5394769094814426e+18, 1.570850118788625e+18, 1.6769223578582638e+18, 1.578305907837335e+18, 1.6624210772467146e+18, 2.0040248673481096e+18, 1.84844872949012e+18, 1.857661217220689e+18, 1.829581226707544e+18, 1.8000506342920315e+18, 1.7917682055614436e+18, 1.84395831853216e+18, 2.126830887523052e+18, 2.403339210131912e+18, 2.4311797947396644e+18, 2.3644805391496346e+18, 2.5663188191824666e+18, 2.4990481478670003e+18, 2.5701735458833096e+18, 2.458290616483253e+18, 2.611443113772188e+18, 2.924920280892846e+18, 3.074674703102004e+18, 3.149778757183109e+18, 3.0633647869650406e+18, 3.280832872899192e+18, 3.22653377651956e+18, 3.705976912386993e+18, 4.021127918293669e+18, 3.803515770303183e+18];
        var Nikes_Massive_Rocket_drawdown = [0.0, 0.0, 0.0, -9.68320000000001, -5.549500416, -13.295872000000008, -3.999999999999999, -13.259745279999988, -18.360766179573744, -10.741104356333947, -1.999999999999996, -9.607920319999971, -9.15741503321154, -1.999999999999996, -12.161035386880013, -14.180879168552607, -15.897261585181557, -23.59889839612273, -21.79009766719561, -20.79943890431341, -16.882971524214298, -15.920133380572487, -3.959999999999993, -8.501078527999981, -16.27607813678401, -6.158783008566333, -3.487968556852984, -9.211866726400022, -5.880800000000006, -5.36038633547759, -3.9999999999999942, -1.9999999999999984, 0.0, -18.428756377600003, -5.920000000000012, -11.45265664000001, 0.0, -2.0, -4.688623466666673, 0.0, 0.0, 0.0, -1.999999999999998, -1.9999999999999996, -12.156547173892214, -17.405455587726433, -7.529350597202833, -9.31650860080422, -7.004643822960109, -4.397737688016988, -10.05739161688638, 0.0, -3.763890938059532, -5.880799999999995, -5.880799999999995, 0.0, -4.728319999999975, -7.342092189354636, -14.535296063245404, -14.204768532126211, -23.461048581234028, -14.674805168476688, -11.370505465661989, -1.9667348974910384, -13.295871999999996, -16.729355468800005, -17.988055864105547, -26.81036375512296, -26.81036375512296, -24.37070921362706, -21.54196668202504, -25.22056844365676, -30.754203844547085, -28.446010639365305, -7.608021250386962, -9.45586082537922, -4.052466950920637, -4.688623466666657, -9.683199999999992, -5.920000000000009, -5.920000000000006, -5.920000000000006, -14.99455037440001, -19.99355085598229, -4.415563652376668, -10.365378117014972, -5.919999999999996, 0.0, -6.672639999999993, -9.938171792588784, -7.738261535575755, 0.0, -8.501078527999976, -5.880800000000002, -8.780696395145219, -10.290412357526984, -2.6616366113801178, -2.5528951398880677, -5.226349965251588, -0.8267465225273928, -3.999999999999999, -5.880800000000004, 0.0, -5.880799999999995, -5.880800000000002, -7.763184000000001, -8.26322913670266, -2.889819134206786, -6.368093796693426, -3.9999999999999942, -16.764037120000005, -13.68674808168448, -18.79649259524876, -10.904984165774415, -12.021907794677324, 0.0, -1.9999999999999951, -2.7434933333333444, -10.331056957440014, -3.9600000000000057, -8.462953977386686, -14.53529606324546, -13.679003913211396, -14.904360915425912, -3.792055486547018, -9.487565801743438, 0.0, 0.0, -2.784000000000006, -4.000000000000003, -13.25974528000001, -11.048408501780498, -12.82744033174489, -15.2190136947395, -18.57634075242781, -21.558140293001333, -26.79228320055441, -26.427423098113795, -27.61493079098524, -22.231629421268643, -18.196761308084383, -19.83282608192269, -19.83282608192269, -9.364109570347429, -16.43515919810719, -10.771319988201094, -12.156682094632798, -13.961273226357044, -7.688782368519526, -7.325248485324459, -1.1469317176794462, -1.1469317176794462, 0.0, -5.919999999999998, -1.6550400000001235, -2.401150429866803, -3.9999999999999942, -15.065344000000003, -12.635533361151996, 0.0, 0.0, -3.959999999999996, -2.0000000000000044, -2.784000000000011, -9.683199999999994, -5.490493439999995, -5.919999999999994, -5.880799999999998, -9.607920320000003, -2.784000000000024, -0.409337173333361, -0.409337173333361, -4.0, 0.0, -1.9999999999999931, -3.999999999999997, 0.0, -3.959999999999992, -0.7586666666666553, -4.688623466666668, -6.168425357086512, -3.959999999999985, -7.342092189354665, -7.342092189354665, -2.000000000000001, -2.827254120447993, -4.770709038039038, 0.0, -0.8000000000000275, 0.0, 0.0, -5.92000000000001, -7.801600000000006, -4.728320000000017, -6.63375360000001, 0.0, 0.0, -6.633753599999991, 0.0, 0.0, 0.0, -5.919999999999988, -4.000000000000002, -8.57727999999999, 0.0, -7.839999999999997, -7.801599999999997, -7.839999999999988, -7.477061631999998, -5.403474684639314, -5.920000000000014, -7.763183999999994, -1.0388331546168668, -4.000000000000007, 0.0, -9.683199999999998, -11.489536, -10.74110435633396, -7.40257728823989, -1.978755273116221, -2.278305157907337, -4.23273905474919, -4.266150736906865, 0.0, 0.0, -2.0000000000000044, -7.840000000000007, -2.0000000000000067, -3.999999999999993, -7.5155967999999875, 0.0, -9.683200000000005, -14.994550374399996, -19.04761021254859, -8.219856057272475, -7.801599999999997, -5.4511144789333175, -9.884155712945848, -0.5688475127544574, -8.32606830519941, -11.489536000000006, -5.8834773825290485, -6.149176106177392, 0.0, 0.0, -3.999999999999997, -7.801599999999993, -8.501078527999976, -3.999999999999994, 0.0, -5.919999999999992, -3.999999999999996, -7.342092189354673, -4.768000000000012, 0.0, 0.0, -5.920000000000003, 0.0, -2.000000000000008, 0.0, -5.919999999999996, -12.161035386880004, -13.150949345813723, -17.2229743153631, -22.68199581928925, -20.088338750994765, -23.835095652918803, -22.485933514968632, -25.55549054777588, -16.992717638337833, -10.63355408541375, -10.188160457027948, -4.7283199999999805, -6.633753599999979, -3.960000000000005, 0.0, -11.415761913599995, -5.084941698358627, -2.6424608647297103, 0.0, -1.9999999999999951, 0.0, -9.607920320000009, -5.177541755238253, 0.0, -7.8016, 0.0, -3.960000000000002, 0.0, -2.000000000000005, 0.0, -1.9999999999999916, -5.919999999999996, -11.452656640000008, -12.124435818291207, -4.506321151249352, -7.90754624903539, 0.0, 0.0, -5.880800000000008, -3.481346030577781, -7.763184000000005, 0.0, -1.9999999999999984, -6.633753599999976, -4.000000000000004, 0.0, -2.000000000000002, -3.4813460305777566, 0.0, -13.187446675327983, -12.087820999882164, 0.0, -9.645568, -12.12443581829119, -3.960000000000009, 0.0, -4.000000000000002, -8.539187200000002, -6.675294857278303, -10.370953180930083, -12.829920105141488, -3.999999999999997, -1.9999999999999984, -7.84000000000001, -10.015422277930211, -3.121730174118644, 0.0, -9.60792032, -6.227009066540981, -9.940419507505943, -5.289968977039838, -4.956589667839027, -1.7884759901003269, -3.959999999999998, -4.688623466666729, -5.920000000000006, -2.000000000000003, 0.0, 0.0, 0.0, 0.0, -1.6550400000000118, -0.5572340622820072, 0.0, -2.000000000000003, -0.7586666666666525, -5.880799999999985, -9.607920319999977, -11.41576191359998, -10.293694897838911, -14.495374648536274, -15.413272472635391, -7.452345618771053, -19.14623536307488, -20.204502588554657, -16.211748686078987, 0.0, -4.000000000000006, -8.577280000000021, -9.270873702400028, -11.76002123376622, -10.642314836060551, -6.591433108628626, -3.959999999999996, -8.462953977386684, -1.9999999999999996, -2.0000000000000027, -7.76318400000001, -5.920000000000002, -2.000000000000003, 0.0, -4.0, -5.919999999999996, 0.0, -11.452656640000018, -9.23306989977604, -3.959999999999991, -5.880799999999995, -9.60792032, 0.0, -3.959999999999992, -1.9999999999999944, -1.9999999999999996, -7.80160000000001, -4.000000000000002, -5.919999999999986, 0.0, -11.452656640000008, -9.195250345567583, -7.603673122824531, 0.0, 0.0, -2.000000000000009, -1.999999999999996, -5.920000000000002, -5.92, -8.501078527999994, -8.045056849944775, -7.682926023160776, -6.184504208450606, -5.7169369504962, 0.0, -2.000000000000004, -5.880799999999996, -8.867183447523058, -14.226522123341107, -19.88315518453741, -22.672299781344677, -22.28690608438561, -12.067677023375788, -6.664931383680218, -14.63498761607623, -10.09146749207838, -4.000000000000002, -1.999999999999997, -5.880799999999985, -13.91781467914238, -18.982440582881704, -21.2379694370543, -23.16118044730177, -21.88145426384984, -17.219581332480203, -24.972203352305264, -27.06097721097707, -31.02085706441705, -28.36630748600851, -28.985763879752056, -21.097537779825537, -9.758034068274265, -3.960000000000007, -2.0000000000000013, -10.33105695744001, -1.0619451044109698, 0.0, 0.0, -2.000000000000006, 0.0, -9.645568000000017, -3.118974226705119, -3.959999999999994, -3.9999999999999982, 0.0, -9.645567999999988, -16.694659366911992, -3.999999999999993, -11.101726720000023, -8.236650925717099, -6.633753600000027, -3.018056491524592, -6.49173797184668, -10.194665148161556, -4.8353608281295175, -3.9600000000000057, -9.607920320000003, 0.0, 0.0, -2.000000000000003, -1.999999999999997, 0.0, -3.96, -6.594850997333338, -2.0, -8.462953977386665, -8.00674229029892, 0.0, 0.0, 0.0, 0.0, 0.0, -2.7840000000000025, -9.64556800000001, -5.217034657464279, -4.016450429792141, -2.3162267893071093, -4.231128859306644, -13.089442366512374, -13.44519958645752, -14.75354254584578, -18.750427954543262, -3.2645885332917555, -1.999999999999996, -13.223603507200016, -1.9999999999999984, -2.000000000000006, -3.960000000000007, -9.233069899776028, -3.0770307546046998, 0.0, 0.0, -11.177979166720048, -10.437479216523176, -9.680151533314444, -9.36222566671166, -7.735147421301729, -13.161058871749818, -4.75087535557374, -6.594850997333336, 0.0, -2.000000000000004, -5.880799999999996, -2.2587764136318405, -13.111908509590751, -10.431802928548962, -8.977998831272412, -13.281810316933647, 0.0, -1.999999999999996, -0.7586666666666975, 0.0, -7.839999999999997, 0.0, -7.380683571200006, -2.784000000000004, -12.90003875430402, 0.0, 0.0, -19.81220466943595, -22.989832113528905, -11.413081037299456, -11.413081037299456, -14.9211230282224, -15.19664246340778, -2.954806673236539, -7.801599999999984, 0.0, -7.839999999999987, 0.0, -3.9999999999999982, -3.6219392, -13.259745280000004, -9.233069899776, -8.08335545183985, -6.552512872424004, -1.3645767530001318, -6.458098343666438, -5.79840433442758, -3.4973733576365325, 0.0, -9.607920320000009, -13.17467578299117, -18.934453853948657, -17.618658437847092, -5.779185978801936, -19.90717340038022, -25.251479385197094, -28.7561560637289, -27.015906435845555, -26.728485344177432, -32.38366380334731, -24.608427259453357, -11.767182388897348, -21.51550451120288, -18.66927066586533, -21.889967547497072, -24.406880005410432, -18.600818002639777, -20.174000418170497, -13.01637867219785, 0.0, 0.0, -7.76318399999998, -5.920000000000004, -3.499007999999995, -2.0000000000000058, -13.259745279999995, -12.82744033174484, -3.2918445352229355, 0.0, -7.801600000000006, -5.919999999999997, -10.331056957439982, -14.535296063245411, 0.0, -0.0028446719999986, -3.999999999999996, 0.0, -2.0, -5.411719109966234, -3.959999999999988, -5.096138017996744, -10.325479411918678, 0.0, -10.331056957439989, -8.14074357538701, -3.034675185824407, -7.763183999999996, -7.303484727766898, -5.880800000000005, -5.41171910996621, -3.8363812741257712, -2.000000000000004, 0.0, -3.999999999999996, 0.0, 0.0, 0.0, -18.394768359424, -21.35046023346481, -30.91505859609141, -35.00991937082713, -27.13269718927444, -27.685517126598494, -20.5680127092913, -23.365246193264912, -23.549751435250325, -21.682326696975363, -15.314867805074114, -14.981366463139542, -7.048938227746761, -9.31861695665825, -13.60632292737321, -12.512003017786608, 0.0, -1.9999999999999971, 0.0, -5.880800000000004, -3.959999999999998, -1.9999999999999984, -0.7586666666666525, -17.39552062504962, -22.04463289143883, -18.425771516110263, -5.422106616173072, -9.167391194172613, -8.989101105449901, -2.000000000000002, -9.645568, 0.0, 0.0, 0.0, 0.0, 0.0, -6.20750556310189, 0.0, -9.645568, 0.0, 0.0, -3.959999999999992, -1.999999999999999, -2.2587764136317534, -5.880799999999995, -6.633753599999996, -5.919999999999996, 0.0, -4.000000000000004, -5.920000000000002, -16.76640491012996, -16.35157681059804, -12.468431120761156, -10.236920755213596, -15.515707919440995, -13.452038278219874, -3.445837051374036, -3.999999999999997, -11.0646857728, -19.64300198259825, -9.85524819599653, -10.539146379682911, -3.265713911155192, -3.9599999999999977, -3.4813460305777912, -2.743493333333326, -2.743493333333326, -4.253495262333122, -13.452743146713164, -14.109348335373436, -9.607920319999977, -19.71192815499531, -12.82201806509097, -21.019392998232377, -24.178617278303083, -22.545832196443907, -17.01044100408317, -19.91425231251503, -15.13498285972986, -1.7793946656369033, -7.801599999999986, -9.00835327116565, -7.492916237226888, -3.960000000000005, -4.688623466666638, -1.9999999999999984, -2.000000000000005, -1.999999999999999, 0.0, -0.3678410638221984, -6.2660648728439305, -5.798904105266103, 0.0, 0.0, 0.0, -7.801599999999991, -16.694659366911996, -21.593679838862627, -20.600533116821545, -21.202910405575235, -19.19407491391936, -22.072162173369385, -15.570857456932904, -12.412976377592322, -7.282634592150368, -12.73515741705518, -10.510446912432714, 0.0, 0.0, 0.0, -0.7586666666666805, 0.0, -5.411719109966257, -9.846607444492973, -5.661486621193968, 0.0, -3.959999999999996, -12.12443581829122, -17.29222199668915, -18.52338571909817, -25.44977668107332, -25.07822565740698, -12.582470077553864, -4.271015936000011, -11.489535999999994, -3.1819412264276967, -2.444574890808124, -7.820021285412078, 0.0, -2.000000000000005, -3.959999999999988, -2.7434933333332854, -2.2587764136317383, -2.150834409914866, 0.0, 0.0, -3.960000000000003, -2.000000000000004, -1.9999999999999984, -3.960000000000002, -5.91999999999999, -7.34209218935467, -2.534707967757259, -8.539187200000011, -2.0000000000000067, 0.0, -9.683200000000005, -20.062455275688844, -15.034788729422946, -21.984095334190563, -20.078426836220384, -12.21219966527874, -8.833829647176723, -11.31837100675899, -16.152508124963465, -13.6762980854337, -12.27515621098722, -1.9999999999999996, -7.763184000000001, 0.0, 0.0, 0.0, -3.960000000000003, -7.801599999999996, -3.018056491524555, -5.678749646216716, -5.307299986156813, -2.0000000000000044, -2.743493333333346, -1.999999999999996, -1.9999999999999971, 0.0, -2.743493333333326, -2.0000000000000067, -3.9999999999999942, 0.0, -1.9999999999999991, -1.9999999999999991, 0.0, 0.0, 0.0, -4.688623466666725, -16.729355468800005, -5.16668594627426, -12.092822555023396, -2.497488178375536, -3.999999999999992, -12.161035386880004, -7.383318286961033, -5.840816810460328, -7.724000474251113, -3.959999999999994, -13.881947101925366, -13.452743146713216, -11.919671148646948, -3.319593649414559, -4.053075665594344, -1.9999999999999944, -5.880799999999989, -12.12443581829118, -13.578811555724185, 0.0, -9.249679359999972, -5.920000000000004, -8.957428645887951, -14.347148870051385, -3.234064941842723, -1.9999999999999996, -1.9999999999999996, -4.000000000000002, -13.259745279999995, -12.827440331744866, -5.838138214343388, -9.88085818674311, -12.132742057456683, -4.000000000000002, -13.88194710192534, -2.5574705624993683, -4.688623466666678, -6.227009066540951, 0.0, -7.76318400000001, -9.289740199526426, -13.9381564836419, -11.744114969889935, -6.193266964646812, -3.960000000000012, -5.920000000000001, -4.353127421269388, -1.999999999999998, -5.919999999999995, -7.473829650609809, -3.913111206745005, 0.0, -3.959999999999993, -7.839999999999991, 0.0, -2.7839999999999727, -4.35312742126934, 0.0, 0.0, -8.462953977386638, -4.000000000000002, -16.65994880831489, -2.000000000000007, -3.960000000000004, -0.7586666666666763, -0.7586666666666736, 0.0, -3.721658704528848, -1.9999999999999944, -1.999999999999999, -9.645567999999994, -8.501078528000003, 0.0, -4.000000000000005, -3.960000000000007, -1.999999999999991, 0.0, -3.481346030577785, -7.763183999999995, 0.0, 0.0, -8.522707401688574, 0.0, -17.39552062504958, -14.30588694371846, 0.0, -9.683200000000012, -10.538786161819631, -4.000000000000006, -9.211866726399975, -7.361683011231138, -2.3604842425457524, -8.102468885210122, -3.204827458640135, -3.960000000000005, -11.415761913600017, -10.974266732547376, -12.319959759027162, -13.735170788488482, -14.13209423402351, -11.630958379187376, 0.0, -1.9999999999999944, -5.163988172799996, -7.765807834878445, -1.5295999999999916, -5.042125448501535, -5.880800000000007, -9.97792870387932, -4.369517339625427, -1.9999999999999931, 0.0, -3.96000000000001, -6.594850997333354, -5.920000000000009, -7.477061632000033, 0.0, 0.0, -8.462953977386665];

        var equityTraces = [
            {
                x: Nikes_Baby_Rocket_dates,
                y: Nikes_Baby_Rocket_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket (Lower Risk)',
                line: { color: '#3f5efb', width: 3 }
            },
            {
                x: Nikes_Massive_Rocket_dates,
                y: Nikes_Massive_Rocket_equity,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket (Higher Risk)',
                line: { color: '#11998e', width: 4 }
            }
        ];
        
        var equityLayout = {
            title: {
                text: 'Nike Rocket Algorithms - Equity Curves Comparison (Log Scale)',
                font: { size: 18 }
            },
            xaxis: { 
                title: 'Date',
                gridcolor: '#e1e5e9'
            },
            yaxis: { 
                title: 'Equity ($)', 
                type: 'log',
                gridcolor: '#e1e5e9'
            },
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff'
        };
        
        Plotly.newPlot('equityChart', equityTraces, equityLayout);

        var drawdownTraces = [
            {
                x: Nikes_Baby_Rocket_dates,
                y: Nikes_Baby_Rocket_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🛡️ Baby Rocket (Better Risk Control)',
                line: { color: '#3f5efb', width: 3 },
                fill: 'tonexty'
            },
            {
                x: Nikes_Massive_Rocket_dates,
                y: Nikes_Massive_Rocket_drawdown,
                type: 'scatter',
                mode: 'lines',
                name: '🚀 Massive Rocket (Higher Risk)',
                line: { color: '#11998e', width: 4 },
                fill: 'tonexty'
            }
        ];
        
        var drawdownLayout = {
            title: {
                text: 'Nike Rocket Algorithms - Drawdown Comparison',
                font: { size: 18 }
            },
            xaxis: { 
                title: 'Date',
                gridcolor: '#e1e5e9'
            },
            yaxis: { 
                title: 'Drawdown (%)',
                gridcolor: '#e1e5e9'
            },
            showlegend: true,
            hovermode: 'x unified',
            plot_bgcolor: '#ffffff',
            paper_bgcolor: '#ffffff'
        };
        
        Plotly.newPlot('drawdownChart', drawdownTraces, drawdownLayout);
    </script>
    
    <div class="summary">
        <h3>🎯 KEY INSIGHTS FROM VISUAL ANALYSIS:</h3>
        <ul style="font-size: 16px; line-height: 1.6;">
            <li><strong>🚀 Massive Rocket Equity Curve:</strong> Exponentially higher growth due to 2x position sizing</li>
            <li><strong>🛡️ Baby Rocket Risk Control:</strong> Much shallower drawdowns, better risk management</li>
            <li><strong>📊 Same Trading Logic:</strong> Identical trade frequency and win rates, only position sizing differs</li>
            <li><strong>⚖️ Risk vs Reward:</strong> Choose based on your risk tolerance and return objectives</li>
            <li><strong>🎯 CALEB's Genius:</strong> Same algorithm, different risk levels = perfect customization</li>
        </ul>
    </div>
    
    <div class="summary">
        <h3>🏆 IMPLEMENTATION RECOMMENDATIONS:</h3>
        <div style="display: flex; justify-content: space-around; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 300px; margin: 10px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4>🚀 Choose MASSIVE ROCKET if:</h4>
                <ul>
                    <li>You can handle -30% drawdowns</li>
                    <li>You want maximum returns</li>
                    <li>You have larger account size</li>
                    <li>You prefer aggressive growth</li>
                </ul>
            </div>
            <div style="flex: 1; min-width: 300px; margin: 10px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                <h4>🛡️ Choose BABY ROCKET if:</h4>
                <ul>
                    <li>You prefer -16% max drawdowns</li>
                    <li>You want steady growth</li>
                    <li>You have smaller account size</li>
                    <li>You prefer conservative approach</li>
                </ul>
            </div>
        </div>
    </div>
    
</body>
</html>
